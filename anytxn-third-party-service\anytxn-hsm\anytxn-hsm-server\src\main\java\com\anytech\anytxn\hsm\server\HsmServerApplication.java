package com.anytech.anytxn.hsm.server;

import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.anytech.anytxn.common.core.annotation.EnableCacheAnnotation;
import com.anytech.anytxn.hsm.EnableHsmAPI;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 */

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, DruidDataSourceAutoConfigure.class})
@ComponentScan(basePackages = {
        "com.anytech.anytxn.common",
        "com.anytech.anytxn.hsm"
})
@EnableHsmAPI
@EnableCacheAnnotation
@EnableMethodCache(basePackages = "jrx.encryption")
public class HsmServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(HsmServerApplication.class, args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}")String applicatonName,
                                                      @Value("${spring.cloud.client.ip-address}") String serverAddr,
                                                      @Value("${server.port}") String port) {
        return (registry) -> registry.config().commonTags("application", applicatonName.concat("-")
                .concat(serverAddr).concat(":").concat(port));
    }

}

