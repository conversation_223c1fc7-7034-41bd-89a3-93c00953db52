package com.anytech.anytxn.file.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: zhangnan
 * @create: 2021-03-18
 **/
@Getter
@Setter
@ToString
public class FileManagerScanParamDTO extends BaseFileEntity implements Serializable {

    @Schema(description = "技术主键")
    private String id;

    @Schema(description = "文件类别")
    private String fileType;


    @Schema(description = "监控路径")
    private String scanPath;

    @Schema(description = "需要复制的路径")
    private String copyPath;

    @Schema(description = "文件名匹配规则 0-正则")
    private String fileNameMatchRule;

    /**
     * 文件名匹配内容
     * 表字段:FILE_NAME_MATCH_CONTENT
     */
    private String fileNameMatchContent;

    @Schema(description = "描述")
    private String description;


    @Schema(description = "启用状态 1-启动  0-停用")
    private String enableStatus;


    @Schema(description = "关联调度计划")
    private String scheduleTask;


    @Schema(description = "md5防重天数")
    private Integer md5CheckDays;

    @Schema(description = "扫描cron表达式")
    private String scanCron;

    /**
     * 文件名处理方式 1-调用批量调度 2-发送邮件
     * 表字段:FILE_PROCESS_TYPE
     */
    private String fileProcessType;

    /**
     * 触发方式 1-达到即触发 2-扫描即触发
     * 表字段:TRIGGER_TYPE
     */
    private String triggerType;

    /**
     * 上次扫描时间
     * LAST_SCAN_TIME
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime lastScanTime;

    /**
     * 上次扫描结果
     *
     */
    private String lastScanResultDes;
}
