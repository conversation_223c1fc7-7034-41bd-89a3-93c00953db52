package com.anytech.anytxn.file.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.constants.ShardingConstant;
import com.anytech.anytxn.common.core.utils.BaseContextHandler;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.file.constants.FileConstant;
import com.anytech.anytxn.file.domain.dto.FileManagerScanParamDTO;
import com.anytech.anytxn.file.domain.dto.FileManagerScanProcessDTO;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.domain.model.FileManagerScanProcess;
import com.anytech.anytxn.file.enums.AnyTxnFileRespCodeEnum;
import com.anytech.anytxn.file.enums.FileResponseDetailEnum;
import com.anytech.anytxn.file.enums.ScanFileProcessStatusEnum;
import com.anytech.anytxn.file.enums.ScheduleExecuteStateEnum;
import com.anytech.anytxn.file.exception.AnyTxnFileException;
import com.anytech.anytxn.file.mapper.FileManagerScanParamMapper;
import com.anytech.anytxn.file.mapper.FileManagerScanProcessMapper;
import com.anytech.anytxn.file.mapper.FileManagerScanProcessSelfMapper;
import com.anytech.anytxn.file.monitor.FileScanParamMonitor;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 页面对文件扫描配置进行增删改查时相应的处理
 * @author: zhangnan
 * @create: 2021-03-18
 **/
@Service
public class FileManagerScanParamService {

    private static final Logger logger = LoggerFactory.getLogger(FileManagerScanParamService.class);

    @Autowired
    private FileManagerScanParamMapper fileManagerScanParamMapper;
    @Autowired
    private FileScanParamMonitor fileScanParamMonitor;

    @Autowired
    private FileManagerScanProcessSelfMapper fileManagerScanProcessSelfMapper;

    @Autowired
    private FileManagerScanProcessMapper fileManagerScanProcessMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public FileManagerScanParamDTO add(FileManagerScanParamDTO fleManagerScanParamReqDTO){
        String tenantId = (String) BaseContextHandler.get(ShardingConstant.TENANT_ID);

        //名称不能重复
        String id =  fileManagerScanParamMapper.countByFileType(fleManagerScanParamReqDTO.getFileType());
        if(id != null){
            logger.error("File type already exists: fileType={}", fleManagerScanParamReqDTO.getFileType());
            throw new AnyTxnFileException(AnyTxnFileRespCodeEnum.C_ADD_ERR, FileResponseDetailEnum.FILE_TYPE_DUPLICATE);
        }

        FileManagerScanParam record = BeanMapping.copy(fleManagerScanParamReqDTO, FileManagerScanParam.class);
        convertDTOStringToInteger(fleManagerScanParamReqDTO,record);

        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setUpdateBy(FileConstant.DEFAULT_USER);
        record.setVersionNumber(1);
        record.setId(sequenceIdGen.generateId(tenantId));

        fileManagerScanParamMapper.insertSelective(record);
        if(record.getEnableStatus() == 1){
            logger.info("Adding file scan task to monitor: fileType={}", record.getFileType());
            fileScanParamMonitor.addTasks(record);
            logger.info("File scan task added to monitor successfully: fileType={}", record.getFileType());
        }

        return BeanMapping.copy(record, FileManagerScanParamDTO.class);

    }
    public FileManagerScanParamDTO update(FileManagerScanParamDTO req){

        FileManagerScanParam record = fileManagerScanParamMapper.selectByPrimaryKey(req.getId());
        BeanMapping.copy(req, record);
        convertDTOStringToInteger(req,record);
        record.setUpdateTime(LocalDateTime.now());
        record.setUpdateBy(FileConstant.DEFAULT_USER);

        fileManagerScanParamMapper.updateByPrimaryKeySelective(record);
        //如果启动状态修改，需要启动
        if(record.getEnableStatus() == 1){
            logger.info("Adding file scan task to monitor: fileType={}", record.getFileType());
            fileScanParamMonitor.addTasks(record);
            logger.info("File scan task added to monitor successfully: fileType={}", record.getFileType());
        }else {
            logger.info("Stopping file scan task: fileType={}", record.getFileType());
            fileScanParamMonitor.stopTask(record);
            logger.info("File scan task stopped successfully: fileType={}", record.getFileType());
        }
        FileManagerScanParamDTO res = BeanMapping.copy(record, FileManagerScanParamDTO.class);
        convertDTOIntegerToString(record,res);
        return res;

    }
    public PageResultDTO<FileManagerScanParamDTO> findPage(Integer pageNum, Integer pageSize) {
        Page<FileManagerScanParam> page = PageHelper.startPage(pageNum, pageSize);
        Page<FileManagerScanParam> pageResult = fileManagerScanParamMapper.selectAll(page, OrgNumberUtils.getOrg());
        List<FileManagerScanParamDTO> listData = new ArrayList<>();
        for (FileManagerScanParam fileManagerScanParam: pageResult) {
            FileManagerScanParamDTO dto = new FileManagerScanParamDTO();
            BeanMapping.copy(fileManagerScanParam, dto);
            convertDTOIntegerToString(fileManagerScanParam,dto);
            listData.add(dto);
        }
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    public FileManagerScanParamDTO findByID(String id) {
        FileManagerScanParam record = fileManagerScanParamMapper.selectByPrimaryKey(id);
        FileManagerScanParamDTO res = new FileManagerScanParamDTO();
        BeanMapping.copy(record, res);
        convertDTOIntegerToString(record,res);
        return res;
    }

    public Boolean delete(String id) {
        int i = fileManagerScanParamMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    public PageResultDTO<FileManagerScanProcessDTO> findProcessPage(Integer pageNum, Integer pageSize,String fileType,String triggerTimeStr) {
        LocalDate triggerTimeDate = null;
        LocalDateTime triggerTimeStart =null;
        LocalDateTime triggerTimeEnd =null;
        if (StringUtils.isNotBlank(triggerTimeStr)) {
            triggerTimeDate = DateHelper.toLocalDate(triggerTimeStr);
            triggerTimeStart = triggerTimeDate.atStartOfDay();
            triggerTimeEnd = triggerTimeDate.atTime(LocalTime.MAX);
        }

        Page<FileManagerScanProcess> page = new Page<>(pageNum, pageSize);
        Page<FileManagerScanProcess> pageResult = fileManagerScanProcessSelfMapper.selectByCondition(page, OrgNumberUtils.getOrg(), fileType, triggerTimeStart, triggerTimeEnd);
        List<FileManagerScanProcessDTO> listData = BeanMapping.copyList(pageResult, FileManagerScanProcessDTO.class);
        listData.forEach(data -> data.setStatusInfoStr(convertStatus(data.getScanStatus(),data.getScheduleExecuteState())));
        return new PageResultDTO<>(pageNum, pageSize, page.getTotal(), page.getPages(), listData);
    }

    private String convertStatus(Integer status,String scheduleExecuteState){
        if(ScanFileProcessStatusEnum.COMPLETE.getcode() == status){
            return FileResponseDetailEnum.FILE_PROCESS_STATUS_100.message();
        }else if(ScanFileProcessStatusEnum.RUNNING.getcode() == status){
            if(ScheduleExecuteStateEnum.FAIL.toString().equals(scheduleExecuteState)){
                return FileResponseDetailEnum.FILE_PROCESS_STATUS_201.message();
            }

            return FileResponseDetailEnum.FILE_PROCESS_STATUS_200.message();
        }else if(ScanFileProcessStatusEnum.SCHEDULER_FAIL.getcode() == status
        || ScanFileProcessStatusEnum.SCHEDULER_ERROR.getcode() == status){
            return FileResponseDetailEnum.FILE_PROCESS_STATUS_300.message();
        }else if(ScanFileProcessStatusEnum.COMPLETE_MANUAL.getcode() == status){
            return FileResponseDetailEnum.FILE_PROCESS_STATUS_301.message();
        } else {
            return "-";
        }

    }

    private void convertDTOStringToInteger(FileManagerScanParamDTO req,FileManagerScanParam record){
        record.setEnableStatus(Integer.parseInt(req.getEnableStatus()) );
        if(req.getFileProcessType() !=null ){
            record.setFileProcessType(Integer.parseInt(req.getFileProcessType()));
        }
        if(req.getFileNameMatchRule() != null){
            record.setFileNameMatchRule(Integer.parseInt(req.getFileNameMatchRule()));
        }
        if(req.getTriggerType() != null){
            record.setTriggerType(Integer.parseInt(req.getTriggerType()));
        }

    }
    private void convertDTOIntegerToString(FileManagerScanParam record,FileManagerScanParamDTO res){
        res.setEnableStatus(record.getEnableStatus().toString());
        res.setFileProcessType(record.getFileProcessType().toString());
        res.setFileNameMatchRule(record.getFileNameMatchRule().toString());
        res.setTriggerType(record.getTriggerType().toString());
    }


    /**
     * 调度失败后，手动调整任务状态为成功
     * @param fileManagerScanProcessDTO
     * @return
     */
    public FileManagerScanProcessDTO fixProcessStatusStatus(FileManagerScanProcessDTO fileManagerScanProcessDTO){
        FileManagerScanProcess fileManagerScanProcess = fileManagerScanProcessMapper.selectByPrimaryKey(fileManagerScanProcessDTO.getId());
        fileManagerScanProcess.setScanStatus(ScanFileProcessStatusEnum.COMPLETE_MANUAL.getcode());
        fileManagerScanProcess.setUpdateTime(LocalDateTime.now());
        fileManagerScanProcessMapper.updateByPrimaryKeySelective(fileManagerScanProcess);
        return BeanMapping.copy(fileManagerScanProcess, FileManagerScanProcessDTO.class);
    }

}
