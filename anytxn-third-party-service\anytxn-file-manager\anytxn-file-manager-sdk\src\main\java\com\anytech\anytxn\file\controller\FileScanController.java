package com.anytech.anytxn.file.controller;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.base.BizBaseController;
import com.anytech.anytxn.file.domain.dto.FileManagerScanParamDTO;
import com.anytech.anytxn.file.domain.dto.FileManagerScanProcessDTO;
import com.anytech.anytxn.file.monitor.FileScanParamMonitor;
import com.anytech.anytxn.file.service.FileManagerScanParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * @description: cms页面的增删改查
 * @author: zhangnan
 * @create: 2021-03-17
 **/
@Lazy
@RestController
public class FileScanController extends BizBaseController{
    
    private static final Logger logger = LoggerFactory.getLogger(FileScanController.class);
    
    @Autowired
    private FileScanParamMonitor fileScanParamMonitor;
    @Autowired
    private FileManagerScanParamService fileManagerScanParamService;

    @PostMapping(value = "/fm/scan/add")
    public AnyTxnHttpResponse<FileManagerScanParamDTO> create(@RequestBody FileManagerScanParamDTO fleManagerScanParamReqDTO) {
        logger.info("File scan parameter creation started: fileType={}", fleManagerScanParamReqDTO.getFileType());
        FileManagerScanParamDTO result = fileManagerScanParamService.add(fleManagerScanParamReqDTO);
        logger.info("File scan parameter creation completed: id={}, fileType={}", result.getId(), result.getFileType());
        return AnyTxnHttpResponse.success(result);
    }

    @PutMapping(value = "/fm/scan/update")
    public AnyTxnHttpResponse<FileManagerScanParamDTO> update(@RequestBody FileManagerScanParamDTO fleManagerScanParamReqDTO) {
        logger.info("File scan parameter update started: id={}, fileType={}", fleManagerScanParamReqDTO.getId(), fleManagerScanParamReqDTO.getFileType());
        FileManagerScanParamDTO result = fileManagerScanParamService.update(fleManagerScanParamReqDTO);
        logger.info("File scan parameter update completed: id={}, fileType={}", result.getId(), result.getFileType());
        return AnyTxnHttpResponse.success(result);
    }

    @GetMapping(value = "/fm/scan/query/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<FileManagerScanParamDTO>> getPage(
            @PathVariable(value = "pageNum") Integer pageNum,
            @PathVariable(value = "pageSize") Integer pageSize) {
        logger.info("File scan parameter page query started: pageNum={}, pageSize={}", pageNum, pageSize);
        PageResultDTO<FileManagerScanParamDTO> page = fileManagerScanParamService.findPage(pageNum, pageSize);
        logger.info("File scan parameter page query completed: total={}, pageNum={}", page.getTotal(), pageNum);
        return AnyTxnHttpResponse.success(page);
    }


    @GetMapping(value = "/fm/scan/query/id/{id}")
    public AnyTxnHttpResponse<FileManagerScanParamDTO> queryOneByID(
            @PathVariable String id) {
        logger.info("File scan parameter query by ID started: id={}", id);
        FileManagerScanParamDTO res = fileManagerScanParamService.findByID(id);
        logger.info("File scan parameter query by ID completed: id={}, found={}", id, res != null);
        return AnyTxnHttpResponse.success(res);
    }

    @DeleteMapping(value = "/fm/scan/delete/id/{id}")
    public AnyTxnHttpResponse<Boolean> remove(@PathVariable String id) {
        logger.info("File scan parameter deletion started: id={}", id);
        Boolean result = fileManagerScanParamService.delete(id);
        logger.info("File scan parameter deletion completed: id={}, success={}", id, result);
        return AnyTxnHttpResponse.success(result);
    }


    /**
     * 查询文件处理记录明细
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/fm/scan/history/query/pageNum/{pageNum}/pageSize/{pageSize}")
    public AnyTxnHttpResponse<PageResultDTO<FileManagerScanProcessDTO>> getHistoryPage(
            @PathVariable(value = "pageNum") Integer pageNum,
            @PathVariable(value = "pageSize") Integer pageSize,
            @RequestParam(value = "fileType", required = false) String fileType,
            @RequestParam(value = "triggerTime", required = false) String triggerTime) {
        logger.info("File scan history page query started: pageNum={}, pageSize={}, fileType={}", pageNum, pageSize, fileType);
        PageResultDTO<FileManagerScanProcessDTO> page = fileManagerScanParamService.findProcessPage(pageNum, pageSize,fileType,triggerTime);
        logger.info("File scan history page query completed: total={}, pageNum={}", page.getTotal(), pageNum);
        return AnyTxnHttpResponse.success(page);
    }


    /**
     * 调度执行失败时，强制更新任务状态为人工处理
     * @param fileManagerScanProcessDTO
     * @return
     */
    @PostMapping(value = "/fm/scan/history/done")
    public AnyTxnHttpResponse<FileManagerScanProcessDTO> fixProcessStatus(@RequestBody FileManagerScanProcessDTO fileManagerScanProcessDTO) {
        logger.info("File scan process status fix started: id={}", fileManagerScanProcessDTO.getId());
        FileManagerScanProcessDTO result = fileManagerScanParamService.fixProcessStatusStatus(fileManagerScanProcessDTO);
        logger.info("File scan process status fix completed: id={}, status={}", result.getId(), result.getScanStatus());
        return AnyTxnHttpResponse.success(result);
    }
}
