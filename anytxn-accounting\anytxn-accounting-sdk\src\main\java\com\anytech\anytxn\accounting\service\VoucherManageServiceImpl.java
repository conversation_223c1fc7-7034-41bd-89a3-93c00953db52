package com.anytech.anytxn.accounting.service;

import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.accounting.base.service.IVoucherManageService;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 传票管理
 * @author: ZXL
 * @create: 2019-10-08 16:21
 */
@Service
public class VoucherManageServiceImpl implements IVoucherManageService {

  private static final Logger logger = LoggerFactory.getLogger(VoucherManageServiceImpl.class);

  @Autowired private ITPmsGlacgnService pmsGlacgnService;

  @Override
  public void checkBalance(List<TAmsGlvcherDTO> tAmsGlvchers,List<TAmsGlvcherDTO> tAmsGlvcherDtoList) {
      tAmsGlvcherDtoList.addAll(tAmsGlvchers);
      // 借/贷不平
      if (!caculateVoucherProcessType(tAmsGlvchers)) {
        tAmsGlvchers.forEach(tAmsGlvcher -> tAmsGlvcher.setProcessType("1"));
      }
  }

  /**
   * @Description: 计算传票平衡结果 @Param: [tAmsGlvchers]
   *
   * @return: boolean @Author: ZXL
   * @date: 2019/10/8
   */
  private boolean caculateVoucherProcessType(List<TAmsGlvcherDTO> tAmsGlvchers) {
    tAmsGlvchers=tAmsGlvchers.stream().filter(tAmsGlvcher -> tAmsGlvcher.getCurrCode()!=null).collect(Collectors.toList());
    // 基于币种分类
    Map<String, List<TAmsGlvcherDTO>> map = tAmsGlvchers.stream()
            .filter(tAmsGlvcher -> {
                  logger.info("Calling pmsGlacgnService.findByGlAcctAndCurrCode: orgNumber={}, branchid={}, glAcct={}, currCode={}", 
                          tAmsGlvcher.getOrganizationNumber(), tAmsGlvcher.getBranchid(), 
                          tAmsGlvcher.getGlAcct(), tAmsGlvcher.getCurrCode());
                  TPmsGlacgnDTO tPmsGlacgn = pmsGlacgnService.findByGlAcctAndCurrCode(
                          tAmsGlvcher.getOrganizationNumber(),
                          tAmsGlvcher.getBranchid(),
                          tAmsGlvcher.getGlAcct(),
                          tAmsGlvcher.getCurrCode());
                  logger.info("pmsGlacgnService.findByGlAcctAndCurrCode completed: result={}", tPmsGlacgn != null ? "found" : "not found");
                  if (tPmsGlacgn==null){                    
                    logger.info("The account number does not exist: {}", tAmsGlvcher.getGlAcct());
                    return false;
                  }
                  return !"OFTA".equals(tPmsGlacgn.getGlClass());
                })
            .collect(Collectors.groupingBy(TAmsGlvcherDTO::getCurrCode));

    // 判断借/贷金额是否为0
    for (Map.Entry<String, List<TAmsGlvcherDTO>> entry : map.entrySet()) {
        Map<String, BigDecimal> mapByDrcr = entry.getValue().stream()
                .collect(Collectors.groupingBy(TAmsGlvcherDTO::getDrcr, AmountUtil.summingBigDecimal(TAmsGlvcherDTO::getGlAmount)));
        BigDecimal result = AmountUtil.subtractAmount(mapByDrcr.get("C"), mapByDrcr.get("D"));
        if (AmountUtil.isZero(result)) {
          continue;
        }
        return false;
    }
    return true;
  }
}
