package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallProductEnquiryService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.*;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallEarlyTerminationParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:39
 * 分期产品查询
 **/
@Service
public class InstallProductEnquiryServiceImpl implements IInstallProductEnquiryService {
    private static final Logger logger = LoggerFactory.getLogger(InstallProductEnquiryServiceImpl.class);

    @Autowired
    private IInstallTypeParmService installTypeParmService;
    @Autowired
    private IInstallFeeCodeInfoService installFeeCodeInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallEarlyTerminationParmService installEarlyTerminationParmService ;

    @Override
    public InstallParameterDTO installmentProductEnquiry(String orgNum, String productCode) {

        //获取分期产品信息
        InstallProductInfoResDTO installProInfo = getInstallProByOrgAndCode(orgNum,productCode);
        if (installProInfo == null) {
            logger.error("Failed to query installment product parameters by organization number and product code: organizationNumber={}, productCode={}", orgNum, productCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        //获取分期类型信息
        String prodType = installProInfo.getProdType();
        InstallTypeParmResDTO intallType = getIntallTypeByOrgNumAndType(orgNum, prodType);
        if (intallType == null) {
            logger.error("Failed to query installment type parameters by organization number and type: organizationNumber={}, type={}", orgNum, prodType);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_NOT_EXIST_FAULT);
        }
        //获取分期费用代码信息
        String feeCodeId = installProInfo.getFeeCodeId();
        InstallFeeCodeInfoResDTO feeCode = getInstallFeeCodeByOrgNumAndFeeCode(orgNum, feeCodeId);
        if (feeCode == null) {
            logger.error("Failed to query installment fee parameters by organization number and fee code: organizationNumber={}, feeCode={}", orgNum, feeCodeId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        //获取分期入帐参数信息
        String tableId = installProInfo.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = getInstallAccountTranByOrgNumAndTableId(orgNum, tableId);
        if (accountTran == null) {
            logger.error("Failed to query installment accounting transaction parameters by organization number and table ID: organizationNumber={}, tableId={}", orgNum, tableId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        //获取提前终止入账
        String advancedEndParmId = installProInfo.getAdvancedEndParmId();
        InstallEarlyTerminationParmResDTO installEarlyTermina = getInstallEarlyTerminaByOrgNumAndTableId(orgNum, advancedEndParmId);
        if (installEarlyTermina == null) {
            logger.error("Failed to query installment early termination parameters by organization number and table ID: organizationNumber={}, tableId={}", orgNum, advancedEndParmId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_EARLY_TERMINATE_NOT_EXIST_FAULT);
        }
        InstallParameterDTO installParameterDTO = new InstallParameterDTO();
        installParameterDTO.setInstallProInfo(installProInfo);
        installParameterDTO.setInstallProdType(intallType);
        installParameterDTO.setInstallFeeCode(feeCode);
        installParameterDTO.setInstallAccountTran(accountTran);
        installParameterDTO.setInstallEarlyTermina(installEarlyTermina);
        return installParameterDTO;
    }

    private InstallProductInfoResDTO getInstallProByOrgAndCode(String organizationNumber, String productCode) {
        logger.info("Calling installProductInfoService.findByIndex: organizationNumber={}, productCode={}", organizationNumber, productCode);
        InstallProductInfoResDTO result = installProductInfoService.findByIndex(organizationNumber, productCode);
        logger.info("installProductInfoService.findByIndex completed: productCode={}", result != null ? result.getProductCode() : null);
        if (result == null){
            logger.error("Failed to query installment product by organization number and product code: organizationNumber={}, productCode={}", organizationNumber, productCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallTypeParmResDTO getIntallTypeByOrgNumAndType(String organizationNumber, String type) {
        logger.info("Calling installTypeParmService.findByOrgNumAndType: organizationNumber={}, type={}", organizationNumber, type);
        InstallTypeParmResDTO result = installTypeParmService.findByOrgNumAndType(organizationNumber, type);
        logger.info("installTypeParmService.findByOrgNumAndType completed: type={}", result != null ? result.getType() : null);
        if (result == null){
            logger.error("Failed to query installment type parameters by organization number and type: organizationNumber={}, type={}", organizationNumber, type);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_TYPE_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallFeeCodeInfoResDTO getInstallFeeCodeByOrgNumAndFeeCode(String organizationNumber, String feeCode) {
        logger.info("Calling installFeeCodeInfoService.getByIndex: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getByIndex(organizationNumber, feeCode);
        logger.info("installFeeCodeInfoService.getByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null){
            logger.error("Failed to query installment fee parameters by organization number and fee code: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        logger.info("Calling installAccountingTransParmService.selectByIndex: organizationNumber={}, tableId={}", organizationNumber, tableId);
        InstallAccountingTransParmResDTO result = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        logger.info("installAccountingTransParmService.selectByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null){
            logger.error("Failed to query installment accounting transaction parameters by organization number and table ID: organizationNumber={}, tableId={}", organizationNumber, tableId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    private InstallEarlyTerminationParmResDTO getInstallEarlyTerminaByOrgNumAndTableId(String organizationNumber, String tableId) {
        logger.info("Calling installEarlyTerminationParmService.findByIndex: organizationNumber={}, tableId={}", organizationNumber, tableId);
        InstallEarlyTerminationParmResDTO result = installEarlyTerminationParmService.findByIndex(organizationNumber, tableId);
        logger.info("installEarlyTerminationParmService.findByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null){
            logger.error("Failed to query installment early termination parameters by organization number and table ID: organizationNumber={}, tableId={}", organizationNumber, tableId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_EARLY_TERMINATE_NOT_EXIST_FAULT);
        }
        return result;
    }
}
