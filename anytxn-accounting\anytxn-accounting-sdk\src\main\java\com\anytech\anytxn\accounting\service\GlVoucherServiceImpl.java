package com.anytech.anytxn.accounting.service;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlamsSumDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvatbalDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherAbsDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherDTO;
import com.anytech.anytxn.accounting.base.domain.dto.TAmsGlvcherSumDTO;
import com.anytech.anytxn.accounting.base.service.IGlVoucherService;
import com.anytech.anytxn.accounting.base.service.IVoucherManageService;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvatbalSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsControlDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamtrDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsControlService;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamtrService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.google.common.collect.Lists;
import com.anytech.anytxn.accounting.base.domain.bo.GenerateVoucherBO;
import com.anytech.anytxn.accounting.base.constants.AccountantConstants;
import com.anytech.anytxn.accounting.base.utils.AmountUtil;
import com.anytech.anytxn.accounting.base.utils.BeanCopyUtils;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsSumSelfMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlamsSum;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvatbal;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlacgnService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
@Service
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class GlVoucherServiceImpl implements IGlVoucherService {
    private static final Logger logger = LoggerFactory.getLogger(GlVoucherServiceImpl.class);
    @Resource
    private AccountantGlamsSumSelfMapper amsGlamsSumSelfMapper;
    @Resource
    private AccountantGlamsSelfMapper glamsSelfMapper;
    @Resource
    private AccountantGlvatbalSelfMapper amsGlvatbalSelfMapper;
    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Resource
    private ITPmsGlamtrService pmsGlamtrService;
    @Resource
    private IVoucherManageService voucherManageService;
    @Resource
    private ITPmsGlamsControlService pmsGlamsControlService;
    @Resource
    private ITPmsGlacgnService pmsGlacgnService;
    @Resource
    private IOrganizationInfoService organizationInfoService;
    @Resource
    private ITransactionTypeService transactionTypeService;
    @Resource
    private ITransactionCodeService transactionCodeService;
    @Resource
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;
    @Resource
    private ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;
    @Resource
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;


    private static final Map<String, ImmutablePair<String,TxnRuleMatcher>> RULE_MATCHER_MANAGER_MAP = new HashMap<>(8);


    /**
     * 根据交易流水生成会计传票
     * @param accountManagementId   管理帐号
     * @return  流水生成传票
     */
    @Override
    public GenerateVoucherBO amsToVoucher(String accountManagementId,OrganizationInfoResDTO organizationInfo) {
        String crdNum="";
        String ind = "";
        // 管理账户对应机构
        // 查询当前账户号未处理会计流水，并汇总入账金额,即新的汇总流水
        List<AccountantGlamsSum> newAmsGlamsSums = amsGlamsSumSelfMapper.selectGroupByParams(accountManagementId, OrgNumberUtils.getOrg(), "0", organizationInfo.getToday());
        if (CollectionUtils.isNotEmpty(newAmsGlamsSums)){
           if ("M".equals(newAmsGlamsSums.get(0).getModuleFlag())){
               crdNum = accountManagementId;
               ind = "M";
               CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(accountManagementId);
               if (ObjectUtils.isNotEmpty(cardAuthorizationInfo)){
                   String cardProNum = cardAuthorizationInfo.getProductNumber();
                   String priCustId = cardAuthorizationInfo.getPrimaryCustomerId();
                   List<ParmCardProductInfo> parmCardProductInfos = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(OrgNumberUtils.getOrg(),cardProNum);
                   if (parmCardProductInfos.size()>0){
                       String acctProNum = parmCardProductInfos.get(0).getAccountProductNumber();
                       AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(priCustId,acctProNum,OrgNumberUtils.getOrg());
                       if (ObjectUtils.isNotEmpty(accountManagementInfo)){
                           accountManagementId = accountManagementInfo.getAccountManagementId();
                       }else {
                           logger.info("Management account not found for this card through primary customer and account product: acctProNum={}, accountManagementId={}",acctProNum,accountManagementId);
                       }

                   }else {
                       logger.info("Account product not found for this card through card product: cardProNum={}, accountManagementId={}",cardProNum,accountManagementId);
                   }

               }else {
                   logger.info("Card not found in our system: accountManagementId={}",accountManagementId);
               }

           }
        }

        // 查询管理账户信息
        AccountManagementInfo accManInfo = accountManagementInfoSelfMapper.selectFinanceStatus(accountManagementId);


        // 查询当前账户号未处理的汇总流水(旧值)
        List<AccountantGlamsSum> oldAmsGlamsSums = amsGlamsSumSelfMapper.selectGlamsSum(accountManagementId, OrgNumberUtils.getOrg(), "0",organizationInfo.getToday());
        if (oldAmsGlamsSums.size()>0){
            if ("M".equals(oldAmsGlamsSums.get(0).getModuleFlag())){
                ind = "M";
            }
        }

        if (CollectionUtils.isEmpty(newAmsGlamsSums) && CollectionUtils.isEmpty(oldAmsGlamsSums)) {
            return null;
        }

        // 1. 生成汇总流水  2. 会计传票分录   3. 传票平衡检查
        Pair<Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>>, Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>> tuple = produceVoucher(newAmsGlamsSums,oldAmsGlamsSums, accManInfo,ind);

        //生成结果处理
        return dealProduceVoucherResult(crdNum,accountManagementId, tuple);
    }

    @Override
    public TAmsGlvcherSumDTO geneVoucherByGlAcct(TPmsGlacgnDTO pmsGlacgn) {
        return null;
    }

    /**
     * 生成处理结果
     * @param accountManagementId
     * @param tuple
     * @return
     */
    private GenerateVoucherBO dealProduceVoucherResult(String crdNum,String accountManagementId,
                                                       Pair<Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>>, Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>> tuple) {
        // 会计传票
        List<TAmsGlvcherDTO> tAmsGlvchers = tuple.getRight().getLeft();
        // abs会计传票
        List<TAmsGlvcherDTO> absAmsGlvchers = tuple.getRight().getRight();
        // 新流水汇总
        List<TAmsGlamsSumDTO> newAmsGlamsSums = tuple.getLeft().getLeft();
        // 旧流水汇总
        List<TAmsGlamsSumDTO> oldAmsGlamsSums = tuple.getLeft().getRight();


        // 增值税分户余额处理
        Pair<List<TAmsGlvatbalDTO>, List<TAmsGlvatbalDTO>> map = new Pair<List<TAmsGlvatbalDTO>, List<TAmsGlvatbalDTO>>() {
            @Override
            public List<TAmsGlvatbalDTO> setValue(List<TAmsGlvatbalDTO> value) {
                return null;
            }

            @Override
            public List<TAmsGlvatbalDTO> getLeft() {
                return null;
            }

            @Override
            public List<TAmsGlvatbalDTO> getRight() {
                return null;
            }
        };
        //glVatBalHandle(tAmsGlvchers);

        // 对规则id赋值
        Map<String, List<TAmsGlamsSumDTO>> newAmsSumMap = newAmsGlamsSums.stream().collect(Collectors.groupingBy(this::getGroupParam));
        List<AccountantGlams> glamsByAcct;
        // 管理账户中未处理流水
        if (!"".equals(crdNum)){
            glamsByAcct = glamsSelfMapper.getByAccountManageId(OrgNumberUtils.getOrg(),crdNum);
        }else {
            glamsByAcct = glamsSelfMapper.getByAccountManageId(OrgNumberUtils.getOrg(),accountManagementId);
        }

        Map<String, List<AccountantGlams>> amsMap = glamsByAcct.stream().collect(Collectors.groupingBy(this::getGroupParam));

        // 更新的会计流水
        List<TAmsGlamsDTO> updateGlAms = new ArrayList<>();
        for (Map.Entry<String, List<AccountantGlams>> entry : amsMap.entrySet()) {
            String key = entry.getKey();
            List<AccountantGlams> glamsList = entry.getValue();
            List<TAmsGlamsSumDTO> resultSums = newAmsSumMap.get(key);

            if (CollectionUtils.isNotEmpty(resultSums)) {
                TAmsGlamsSumDTO tAmsGlamsSum = resultSums.get(0);
                for (AccountantGlams amsGlams : glamsList) {
                    TAmsGlamsDTO updateAmsGlams = new TAmsGlamsDTO();
                    updateAmsGlams.setId(amsGlams.getId());
                    // 标记已处理
                    updateAmsGlams.setProcessInd("1");
                    updateAmsGlams.setUpdateTime(LocalDateTime.now());
                    updateAmsGlams.setUpdateBy(AccountantConstants.DEFAULT_USER);
                    updateAmsGlams.setVersionNumber(amsGlams.getVersionNumber() + 1);
                    if(tAmsGlamsSum.getRuleId() == null){
                        logger.error("RuleId not found, this may be an error: key={}", key);
                    }
                    updateAmsGlams.setScheme(tAmsGlamsSum.getScheme());
                    updateAmsGlams.setRuleId(tAmsGlamsSum.getRuleId());
                    updateGlAms.add(updateAmsGlams);
                }
            }else {
                logger.error("ResultSums not found, this may be an error: key={}", key);
            }
        }
        return new GenerateVoucherBO(tAmsGlvchers, newAmsGlamsSums, map.getLeft(), map.getRight(), oldAmsGlamsSums, revertToAbs(absAmsGlvchers), updateGlAms);
    }

    /**
     * GLOBAL_FLOW_NO,ORGANIZATION_NUMBER,BRANCHID,ACCOUNT_MANAGEMENT_ID,MODULE_FLAG,TXN_CODE,",
     * "TXN_CODE_ORIG,POSTING_CURRENCY_CODE,FINANCE_STATUS,INTEREST_IND,PRICE_TAX_FLG,POSTING_DATE,ACCT_LOGO,",
     * "BAL_PROCESS_IND,ABS_STATUS, ASSET_NO,ORDER_ID,CHANNEL_ID,TRANSACTION_ATTRIBUTE,
     * DEBIT_CREDIT_INDICATOR," "AMORTIZE_IND,TRANSACTION_TYPE_CODE,BAL_TYPE"
     */
    private String getGroupParam(Object o) {
        AccountantGlamsSum tAmsGlamsSum = new AccountantGlamsSum();
        if (o instanceof AccountantGlams){
            AccountantGlams o1 = (AccountantGlams) o;
            tAmsGlamsSum= BeanMapping.copy(o1, AccountantGlamsSum.class);
        } else if (o instanceof TAmsGlamsDTO) {
            TAmsGlamsDTO o1 = (TAmsGlamsDTO) o;
            tAmsGlamsSum= BeanMapping.copy(o1, AccountantGlamsSum.class);
        }else if (o instanceof AccountantGlamsSum){
            tAmsGlamsSum = (AccountantGlamsSum) o;
        } else if (o instanceof TAmsGlamsSumDTO) {
            TAmsGlamsSumDTO o1 = (TAmsGlamsSumDTO)o;
            tAmsGlamsSum = BeanMapping.copy(o1, AccountantGlamsSum.class);
        } else {
            return null;
        }

        List<? extends Serializable> list = Arrays.asList(tAmsGlamsSum.getGlobalFlowNo(),
                tAmsGlamsSum.getOrganizationNumber(), tAmsGlamsSum.getBranchid(), tAmsGlamsSum.getAccountManagementId(),
                tAmsGlamsSum.getModuleFlag(), tAmsGlamsSum.getTxnCode(), tAmsGlamsSum.getTxnCodeOrig(),
                tAmsGlamsSum.getPostingCurrencyCode(),
                tAmsGlamsSum.getFinanceStatus(), tAmsGlamsSum.getInterestInd(), tAmsGlamsSum.getPriceTaxFlg(),
                tAmsGlamsSum.getPostingDate(), tAmsGlamsSum.getAcctLogo(),
                tAmsGlamsSum.getBalProcessInd(), tAmsGlamsSum.getAbsStatus(), tAmsGlamsSum.getAssetNo(),
                tAmsGlamsSum.getOrderId(), tAmsGlamsSum.getChannelId(),
                tAmsGlamsSum.getTransactionAttribute(), tAmsGlamsSum.getDebitCreditIndicator(),
                tAmsGlamsSum.getAmortizeInd(), tAmsGlamsSum.getTransactionTypeCode(), tAmsGlamsSum.getBalType(),
                tAmsGlamsSum.getNpasFirst(), tAmsGlamsSum.getFiveTypeIndicator(), tAmsGlamsSum.getAbsType()
        );
        StringBuilder sb = new StringBuilder();
        list.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList()).forEach(sb::append);
        return sb.toString();
    }

    /**
     * 传票转abs传票
     * @param absAmsGlvchers
     * @return
     */
    private List<TAmsGlvcherAbsDTO> revertToAbs(List<TAmsGlvcherDTO> absAmsGlvchers) {
        return absAmsGlvchers.stream().map(BeanCopyUtils::copyTAmsGlvcherAbs).collect(Collectors.toList());
    }

    /**
     * 生成会计传票
     * @param newAmsGlamsSums   新会计流水汇总
     * @param oldAmsGlamsSums   旧会计流水汇总
     * @param accManInfo        管理帐号
     * @return
     */
    private Pair<Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>>, Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>> produceVoucher(List<AccountantGlamsSum> newAmsGlamsSums,
                                                                                                              List<AccountantGlamsSum> oldAmsGlamsSums,
                                                                                                              AccountManagementInfo accManInfo,String ind) {
        // 按全局业务流水号分组生成汇总会计流水
        List<List<TAmsGlamsSumDTO>> tamsGlAmsSumsByGroup = getListTamsGlAmsSumsByGroup(newAmsGlamsSums, oldAmsGlamsSums, accManInfo,ind);

        // 流水汇总容器
        Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>> glamsSumPair= Pair.of(Lists.newArrayList(), Lists.newArrayList());

        // 会计传票容器
        Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>> glvcherPair= Pair.of(Lists.newArrayList(), Lists.newArrayList());
        // 结果容器（新汇总、旧汇总、会计传票、abs会计传票）
        Pair<Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>>, Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>> result = Pair.of(glamsSumPair, glvcherPair);

        // 基于汇总会计流水生成会计传票
        for (List<TAmsGlamsSumDTO> amsGlamsSums : tamsGlAmsSumsByGroup) {
            // 会计传票分录
            Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> glvcherTuple = generateVoucher(amsGlamsSums,accManInfo);
            // 结果处理
            getResult(result, glvcherTuple);
        }
        logger.info("Voucher count result: {}",result.getRight().getLeft().size());
        return result;
    }

    /**
     * @Description: 统计拆分结果 @Param: [tuple, tuple]
     * @return: void @Author: ZXL
     * @date: 2019/10/8
     */
    private void getResult(
            Pair<Pair<List<TAmsGlamsSumDTO>, List<TAmsGlamsSumDTO>>, Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>> result,
            Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> glvcherTuple){
        // 拆分是否成功
        Boolean flag = glvcherTuple.getRight().getLeft();
        // 分录后流水汇总
        List<TAmsGlamsSumDTO> amsGlamsSums = glvcherTuple.getRight().getRight();
        // 分录后会计传票
        List<TAmsGlvcherDTO> tAmsGlvchers = glvcherTuple.getLeft().getLeft();
        // 分录后abs会计传票
        List<TAmsGlvcherDTO> absAmsGlvchers = glvcherTuple.getLeft().getRight();

        // 新汇总流水处理结果
        List<TAmsGlamsSumDTO> newAlamsSum = result.getLeft().getLeft();
        // 旧汇总流水处理结果
        List<TAmsGlamsSumDTO> oldAlamsSum = result.getLeft().getRight();
        // 会计传票处理结果
        List<TAmsGlvcherDTO> tAmsGlvchers2 = result.getRight().getLeft();
        // abs会计传票处理结果
        List<TAmsGlvcherDTO> absAmsGlvchers2 = result.getRight().getRight();

        // 汇总处理（拆分失败，标记汇总处理失败）
        if (flag) {
            handleGlAmsSums(amsGlamsSums, newAlamsSum, oldAlamsSum, "2");
            return;
        }

        // 剔除不做借贷传票（处理标记2）
        removeOutTableVoucher(tAmsGlvchers, tAmsGlvchers2);
        removeOutTableVoucher(absAmsGlvchers, absAmsGlvchers2);

        // 传票平衡检查
        logger.info("Calling voucherManageService.checkBalance for tAmsGlvchers: size={}", tAmsGlvchers.size());
        voucherManageService.checkBalance(tAmsGlvchers, tAmsGlvchers2);
        logger.info("voucherManageService.checkBalance for tAmsGlvchers completed");
        logger.info("Calling voucherManageService.checkBalance for absAmsGlvchers: size={}", absAmsGlvchers.size());
        voucherManageService.checkBalance(absAmsGlvchers, absAmsGlvchers2);
        logger.info("voucherManageService.checkBalance for absAmsGlvchers completed");

        // 汇总处理（拆分成功）
        handleGlAmsSums(amsGlamsSums, newAlamsSum, oldAlamsSum, "1");
    }

    /**
     * 汇总会计流水处理标记
     * @param amsGlamsSums
     * @param newGlamsSums
     * @param oldGlamsSums
     * @param processInd
     */
    private void handleGlAmsSums(List<TAmsGlamsSumDTO> amsGlamsSums,
                                 List<TAmsGlamsSumDTO> newGlamsSums,
                                 List<TAmsGlamsSumDTO> oldGlamsSums,
                                 String processInd) {
        amsGlamsSums.forEach(glamsSums -> {
            glamsSums.setProcessInd(processInd);
            if (glamsSums.getId() == null) {
                glamsSums.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                newGlamsSums.add(glamsSums);
            } else {
                oldGlamsSums.add(glamsSums);
            }
        });
    }

    /**
     * 去除'9999999',不做借贷平衡检查
     * @param tAmsGlvchers      处理后的传票
     * @param amsGlvcherDtos    传票结果容器
     */
    private void removeOutTableVoucher(List<TAmsGlvcherDTO> tAmsGlvchers, List<TAmsGlvcherDTO> amsGlvcherDtos) {
        List<TAmsGlvcherDTO> otherVouchers =tAmsGlvchers.stream()
                .filter(tAmsGlvcher -> {
                    String glAcct = tAmsGlvcher.getGlAcct();
                    return "9999999".equals(glAcct);
                })
                .peek(tAmsGlvcher -> tAmsGlvcher.setProcessType("2"))
                .collect(Collectors.toList());
        amsGlvcherDtos.addAll(otherVouchers);
        tAmsGlvchers.removeAll(otherVouchers);
    }

    /**
     * 分录会计传票
     * @param amsGlamsSums  汇总会计流水
     * @param accManInfo
     * @return
     */
    private Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> generateVoucher(
            List<TAmsGlamsSumDTO> amsGlamsSums, AccountManagementInfo accManInfo) {


        // 会计传票
        List<TAmsGlvcherDTO> tAmsGlvchers = new ArrayList<>(amsGlamsSums.size());
        // abs会计传票
        List<TAmsGlvcherDTO> absAmsGlvchers = new ArrayList<>(amsGlamsSums.size());
        // 总容器
        Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> result = Pair.of(Pair.of(tAmsGlvchers, absAmsGlvchers), Pair.of(Boolean.FALSE, amsGlamsSums));
        // 入账金额大于0的汇总
        List<TAmsGlamsSumDTO> tAmsGlamsSums = amsGlamsSums.stream()
                .filter(amsGlamsSum -> !(amsGlamsSum.getPostingAmt() != null && amsGlamsSum.getPostingAmt().compareTo(BigDecimal.ZERO) == 0))
                .peek(amsGlamsSum -> {
                    amsGlamsSum.setCreateTime(LocalDateTime.now());
                    amsGlamsSum.setUpdateTime(LocalDateTime.now());
                    amsGlamsSum.setUpdateBy(AccountantConstants.DEFAULT_USER);
                    amsGlamsSum.setVersionNumber(1L);
                }).collect(Collectors.toList());
        // 逐条处理生成会计传票
        for (TAmsGlamsSumDTO amsGlamsSum : tAmsGlamsSums) {

            List<TPmsGlamsControlDTO> pmsGlamsControlDTOList;
            BigDecimal postingAmt = amsGlamsSum.getPostingAmt();
            // 需要税处理
            if ("Y".equals(amsGlamsSum.getPriceTaxFlg())) {
                // 增值税税率参数
                logger.info("Calling pmsGlamtrService.selectByTxnCodeAndFinanceStatus: organizationNumber={}, branchid={}, txnCode={}", 
                        amsGlamsSum.getOrganizationNumber(), amsGlamsSum.getBranchid(), amsGlamsSum.getTxnCode());
                TPmsGlamtrDTO pmsGlamtr = pmsGlamtrService.selectByTxnCodeAndFinanceStatus(
                        amsGlamsSum.getOrganizationNumber(),
                        amsGlamsSum.getBranchid(),
                        amsGlamsSum.getTxnCode());
                logger.info("pmsGlamtrService.selectByTxnCodeAndFinanceStatus completed: pmsGlamtr={}", pmsGlamtr != null ? "found" : "null");
                if (pmsGlamtr != null && pmsGlamtr.getTaxRate() != null) {
                    // 价金额
                    Integer taxRate = pmsGlamtr.getTaxRate();
                    postingAmt = amsGlamsSum.getPostingAmt().divide(
                            new BigDecimal(AccountantConstants.S_ONE).add(new BigDecimal(String.valueOf(taxRate / 10000D))), 2,
                            RoundingMode.HALF_UP);

                    // 匹配会计核算事件参数
                    List<TPmsGlamsControlDTO> allPmsGlamsControl = getTableIds(amsGlamsSum);

                    if (CollectionUtils.isEmpty(allPmsGlamsControl)){
                        result = judgement(result,amsGlamsSum);
                    } else {
                        // 价分录
                        pmsGlamsControlDTOList = allPmsGlamsControl.stream()
                                .filter(tPmsGlamsControlDTO -> "1".equals(tPmsGlamsControlDTO.getPriceTaxInd()))
                                .collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(pmsGlamsControlDTOList)) {
                            // 分录会计传票
                            buildListVoucherByGlamsPms(pmsGlamsControlDTOList, result.getLeft(), amsGlamsSum, postingAmt, accManInfo);
                        }

                        // 税金额
                        postingAmt = amsGlamsSum.getPostingAmt().subtract(postingAmt).setScale(2, RoundingMode.HALF_UP);
                        // 税分录
                        pmsGlamsControlDTOList = allPmsGlamsControl.stream()
                                .filter(tPmsGlamsControlDTO -> "2".equals(tPmsGlamsControlDTO.getPriceTaxInd()))
                                .collect(Collectors.toList());
                        // 分录拆分失败
                        if (CollectionUtils.isNotEmpty(pmsGlamsControlDTOList)) {
                            // 分录会计传票
                            buildListVoucherByGlamsPms(pmsGlamsControlDTOList, result.getLeft(), amsGlamsSum, postingAmt, accManInfo);
                        }
                        // 非价税金额
                        postingAmt = amsGlamsSum.getPostingAmt();
                        // 非价税分录
                        pmsGlamsControlDTOList = allPmsGlamsControl.stream()
                                .filter(tPmsGlamsControlDTO -> !StringUtils.equalsAny(tPmsGlamsControlDTO.getPriceTaxInd(),"1","2"))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(pmsGlamsControlDTOList)) {
                            buildListVoucherByGlamsPms(pmsGlamsControlDTOList, result.getLeft(), amsGlamsSum, postingAmt, accManInfo);
                        }
                    }
                // 无税率
                } else {
                    pmsGlamsControlDTOList = getTableIds(amsGlamsSum);
                    if (CollectionUtils.isEmpty(pmsGlamsControlDTOList)) {
                        result = judgement(result,amsGlamsSum);
                    } else {
                        List<TPmsGlamsControlDTO> tPmsGlamsControlDTOS = pmsGlamsControlDTOList.stream().filter(x -> !"2".equals(x.getPriceTaxInd())).collect(Collectors.toList());
                        buildListVoucherByGlamsPms(tPmsGlamsControlDTOS, result.getLeft(), amsGlamsSum, postingAmt, accManInfo);
                    }
                }
            // 非价税分离
            } else {
                //匹配会计核算事件参数
                pmsGlamsControlDTOList = getTableIds(amsGlamsSum);

                if (CollectionUtils.isEmpty(pmsGlamsControlDTOList)) {
                    result = judgement(result,amsGlamsSum);
                } else {
                    buildListVoucherByGlamsPms(pmsGlamsControlDTOList, result.getLeft(), amsGlamsSum, postingAmt, accManInfo);
                }
            }
        }

        return result;
    }

    private Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> judgement(
            Pair<Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>>, Pair<Boolean, List<TAmsGlamsSumDTO>>> result,
                                  TAmsGlamsSumDTO amsGlamsSum){
        if ("M".equals(amsGlamsSum.getMkUpFeeInd()) && amsGlamsSum.getTxnCode().equals(amsGlamsSum.getTxnCodeOrig())){
            result = Pair.of(result.getLeft(),Pair.of(Boolean.FALSE, result.getValue().getRight()));
            logger.info("Ignoring original transaction flow due to mkupFee glams: globalFlowNo={}, txnCode={}", amsGlamsSum.getGlobalFlowNo(), amsGlamsSum.getTxnCode());
        }else {
            result = Pair.of(result.getLeft(),Pair.of(Boolean.TRUE, result.getValue().getRight()));
            logger.info("No corresponding split parameters: globalFlowNo={}, txnCode={}", amsGlamsSum.getGlobalFlowNo(), amsGlamsSum.getTxnCode());
        }
        return result;
    }
    /**
     * @Description: 获取全部规则参数
     * @return: void @Author: ZXL
     * @date: 2019/10/8
     */
    private List<TPmsGlamsControlDTO> getTableIds(TAmsGlamsSumDTO amsGlamsSum) {
        findScheme(amsGlamsSum);

        Map map = JSONObject.parseObject(JSONObject.toJSONString(amsGlamsSum), Map.class);
        map.put("markupFeeInd",amsGlamsSum.getMkUpFeeInd());
        map.put("acqId",amsGlamsSum.getAcqId());
        map.put("scheme",amsGlamsSum.getScheme());
        map.put("financeStatus",amsGlamsSum.getFinanceStatus());


        List<String> tableIds = new ArrayList<>();
        String cashTableId = executeRule("cashGlAmsParaRules",map);
        addTableIds(tableIds, cashTableId);
        String feeTableId = executeRule("feeGlAmsParaRules", map);
        addTableIds(tableIds, feeTableId);
        String intTableId = executeRule("intGlAmsParaRules", map);
        addTableIds(tableIds, intTableId);
        String absTableId = executeRule("absGlAmsParaRules", map);
        addTableIds(tableIds, absTableId);
        String ruleId = StringUtils.join(tableIds.toArray(), ";");
        amsGlamsSum.setRuleId(ruleId);
        logger.info("Calling pmsGlamsControlService.findByTableId for tableIds: {}", tableIds);
        List<TPmsGlamsControlDTO> result = tableIds.stream().map(id -> pmsGlamsControlService.findByTableId(id)).
                filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        logger.info("pmsGlamsControlService.findByTableId completed: resultSize={}", result.size());
        return result;
    }
    private void findScheme(TAmsGlamsSumDTO amsGlamsSum){
        if (StringUtils.isBlank(amsGlamsSum.getScheme())){
            if ("M".equals(amsGlamsSum.getModuleFlag().trim())){
                CardAuthorizationInfo cardAuthorizationInfoByAmid = cardAuthorizationInfoSelfMapper.selectByCardNumberInStatementFile(amsGlamsSum.getAccountManagementId());
                if (ObjectUtils.isNotEmpty(cardAuthorizationInfoByAmid)){
                    String crdPro = cardAuthorizationInfoByAmid.getProductNumber();
                    ParmCardProductInfo parmCardProductInfo = cardProductInfoSelfMapper.selectByOrgAndProductNum(OrgNumberUtils.getOrg(),crdPro);
                    if (ObjectUtils.isNotEmpty(parmCardProductInfo)){
                        String crdOrg = parmCardProductInfo.getScheme();
                        amsGlamsSum.setScheme(crdOrg);
                    }else {
                        amsGlamsSum.setScheme(amsGlamsSum.getPlatformId());
                    }
                }else {
                    amsGlamsSum.setScheme(amsGlamsSum.getPlatformId());
                }
            }else {
                AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(amsGlamsSum.getAccountManagementId());
                if (ObjectUtils.isNotEmpty(accountManagementInfo)){
                    List<ParmCardProductInfo> parmCardProductInfos = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(OrgNumberUtils.getOrg(),accountManagementInfo.getProductNumber());
                    if (CollectionUtils.isNotEmpty(parmCardProductInfos)){
                        ParmCardProductInfo parmCardProductInfo = parmCardProductInfos.get(0);
                        String crdOrg = parmCardProductInfo.getScheme();
                        amsGlamsSum.setScheme(crdOrg);
                    }else {
                        amsGlamsSum.setScheme("");
                    }
                }
            }
        }
    }


    private void addTableIds(List<String> tableIds, String cashTableId) {
        if (StringUtils.isNoneBlank(cashTableId)) {
            tableIds.add(cashTableId);
        }
    }

    /**
     * 分录会计传票
     * @param tPmsGlamsControlDTOList
     * @param glvcherPair       传票/abs传票
     * @param amsGlamsSum
     * @param postingAmt
     * @param accManInfo
     */
    private void buildListVoucherByGlamsPms(List<TPmsGlamsControlDTO> tPmsGlamsControlDTOList,
                                            Pair<List<TAmsGlvcherDTO>, List<TAmsGlvcherDTO>> glvcherPair,
                                            TAmsGlamsSumDTO amsGlamsSum,
                                            BigDecimal postingAmt, AccountManagementInfo accManInfo) {
        if (CollectionUtils.isNotEmpty(tPmsGlamsControlDTOList)){
            tPmsGlamsControlDTOList.forEach(
                    tPmsGlamsControlDTO -> {
                        if (!isPreExportTable(amsGlamsSum)) {
                            glvcherPair.getLeft().add(generate(amsGlamsSum, tPmsGlamsControlDTO, postingAmt, accManInfo));
                        } else {
                            glvcherPair.getRight().add(generate(amsGlamsSum, tPmsGlamsControlDTO, postingAmt, accManInfo));
                        }
                    });
        }
    }

    /**
     * 是否abs封包（预出表）
     * @param amsGlamsSum
     * @return
     */
    private boolean isPreExportTable(TAmsGlamsSumDTO amsGlamsSum) {
        return (StringUtils.equalsAny(amsGlamsSum.getModuleFlag(),"9","A","B","C")) && "F".equals(amsGlamsSum.getAbsStatus());
    }

    /**
     * @Description: 构建会计分录 @Param: [amsGlamsSum, tPmsGlamsControlDTO, postingAmt]
     * @return: com.anytech.anytxn.accountant.model.TAmsGlvcher @Author: ZXL
     * @date: 2019/10/8
     */
    /**
     * 构建会计分录
     * @param amsGlamsSum           汇总流水
     * @param tPmsGlamsControlDTO   会计核算参数
     * @param postingAmt            价金额
     * @param accManInfo
     * @return
     */
    private TAmsGlvcherDTO generate(TAmsGlamsSumDTO amsGlamsSum,
                                    TPmsGlamsControlDTO tPmsGlamsControlDTO,
                                    BigDecimal postingAmt,
                                    AccountManagementInfo accManInfo) {

        TAmsGlvcherDTO tAmsGlvcher = new TAmsGlvcherDTO();
        tAmsGlvcher.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        tAmsGlvcher.setOrganizationNumber(amsGlamsSum.getOrganizationNumber());
        tAmsGlvcher.setBranchid(amsGlamsSum.getBranchid());
        tAmsGlvcher.setAccountManagementId(amsGlamsSum.getAccountManagementId());
        if ("M".equals(amsGlamsSum.getModuleFlag())){
            tAmsGlvcher.setPartitionKey(0);
            logger.info("Acquirer data account product processing started: amid={}, global={}",amsGlamsSum.getAccountManagementId(),amsGlamsSum.getGlobalFlowNo());
            CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(amsGlamsSum.getAccountManagementId());
            if (!ObjectUtils.isEmpty(cardAuthorizationInfo)){
                String crdProduct = StringUtils.isBlank(cardAuthorizationInfo.getProductNumber())?amsGlamsSum.getCrdProductNumber():cardAuthorizationInfo.getProductNumber();
                ParmCardProductInfo parmCardProductInfo = cardProductInfoSelfMapper.selectByOrgAndProductNum(OrgNumberUtils.getOrg(),crdProduct);
                tAmsGlvcher.setAcctLogo(ObjectUtils.isNotEmpty(parmCardProductInfo) ? parmCardProductInfo.getAccountProductNumber() : amsGlamsSum.getAcctLogo());
                /*if (ObjectUtils.isNotEmpty(parmCardProductInfo) && StringUtils.isBlank(amsGlamsSum.getAcctLogo())){
                    amsGlamsSum.setAcctLogo(parmCardProductInfo.getAccountProductNumber());
                }*/
            }else {
                ParmCardProductInfo parmCardProductInfo = cardProductInfoSelfMapper.selectByOrgAndProductNum(OrgNumberUtils.getOrg(),amsGlamsSum.getCrdProductNumber());
                tAmsGlvcher.setAcctLogo(ObjectUtils.isEmpty(parmCardProductInfo) ? amsGlamsSum.getAcctLogo() : parmCardProductInfo.getAccountProductNumber());
                /*if (!ObjectUtils.isEmpty(parmCardProductInfo) && StringUtils.isBlank(amsGlamsSum.getAcctLogo())){
                    amsGlamsSum.setAcctLogo(parmCardProductInfo.getAccountProductNumber());
                }*/
            }

        }else {
            tAmsGlvcher.setPartitionKey(PartitionKeyUtils.partitionKey(accManInfo.getCustomerId()).intValue());
            tAmsGlvcher.setAcctLogo(amsGlamsSum.getAcctLogo());
        }

        //log.info("tAmsGlvcher.setPartitionKey : {}"+tAmsGlvcher.getPartitionKey());
        tAmsGlvcher.setGlobalFlowNo(amsGlamsSum.getGlobalFlowNo());
        tAmsGlvcher.setModuleFlag(amsGlamsSum.getModuleFlag());
        tAmsGlvcher.setVpTxnCode(amsGlamsSum.getTxnCode());
        tAmsGlvcher.setCurrCode(amsGlamsSum.getPostingCurrencyCode());
        //0是红标志
        if ("0".equals(tPmsGlamsControlDTO.getRbLogo())){
            if ("C".equals(tPmsGlamsControlDTO.getDrcr())){
                tAmsGlvcher.setDrcr("D");
            }else{
                if ("D".equals(tPmsGlamsControlDTO.getDrcr())){
                    tAmsGlvcher.setDrcr("C");
                }
            }
            tAmsGlvcher.setGlAmount(postingAmt.multiply(new BigDecimal("-1")));
        }else{
            tAmsGlvcher.setDrcr(tPmsGlamsControlDTO.getDrcr());
            tAmsGlvcher.setGlAmount(postingAmt);
        }
        tAmsGlvcher.setGlAcct(tPmsGlamsControlDTO.getGlAcct());
        tAmsGlvcher.setGlCnt(amsGlamsSum.getPostingCnt());
        tAmsGlvcher.setPostingDate(amsGlamsSum.getPostingDate());
        tAmsGlvcher.setProcessType("0");
        tAmsGlvcher.setAbsStatus(amsGlamsSum.getAbsStatus());
        tAmsGlvcher.setAssetNo(amsGlamsSum.getAssetNo());
        tAmsGlvcher.setOrderId(amsGlamsSum.getOrderId());
        tAmsGlvcher.setCreateTime(LocalDateTime.now());
        tAmsGlvcher.setUpdateTime(LocalDateTime.now());
        tAmsGlvcher.setUpdateBy(AccountantConstants.DEFAULT_USER);
        tAmsGlvcher.setVersionNumber(1L);
        tAmsGlvcher.setChannelId(amsGlamsSum.getChannelId());
        tAmsGlvcher.setTxnCodeOrig(amsGlamsSum.getTxnCodeOrig());
        // 网点号
        tAmsGlvcher.setFundId(amsGlamsSum.getFundId());
        // 五级分类
        tAmsGlvcher.setFiveTypeIndicator(amsGlamsSum.getFiveTypeIndicator());
        // abs状态
        tAmsGlvcher.setAbsType(amsGlamsSum.getAbsType());
        //for oriRecordDetail File
        tAmsGlvcher.setSubchannelId(amsGlamsSum.getSubchannelId());
        tAmsGlvcher.setCrdNumber(amsGlamsSum.getCrdNumber());
        tAmsGlvcher.setCrdProductNumber(amsGlamsSum.getCrdProductNumber());
        tAmsGlvcher.setTxnAmt(amsGlamsSum.getTxnAmt());
        tAmsGlvcher.setTxnCurrency(amsGlamsSum.getTxnCurrency());
        tAmsGlvcher.setTxnDate(amsGlamsSum.getTxnDate());
        tAmsGlvcher.setTxnSource(amsGlamsSum.getTxnSource());
        tAmsGlvcher.setTxnDescription(amsGlamsSum.getTxnDescription());
        tAmsGlvcher.setCurrencyRate(amsGlamsSum.getCurrencyRate());
        tAmsGlvcher.setRrn(amsGlamsSum.getRrn());
        tAmsGlvcher.setMerchantNumber(amsGlamsSum.getMerchantNumber());
        tAmsGlvcher.setAcqId(amsGlamsSum.getAcqId());
        tAmsGlvcher.setPlatformId(amsGlamsSum.getPlatformId());
        //V2,V3
        tAmsGlvcher.setVataCoupleIndicator(amsGlamsSum.getVataCoupleIndicator());
        tAmsGlvcher.setVaNumber(amsGlamsSum.getVaNumber());



        return tAmsGlvcher;
    }

    /**
     * 生成流水一套汇总流水(新增汇总 + 旧汇总)
     * @param newAmsGlamsSums   新汇总流水
     * @param oldAmsGlamsSums   旧汇总流水
     * @param accManInfo        管理帐号
     * @return
     */
    private List<List<TAmsGlamsSumDTO>> getListTamsGlAmsSumsByGroup(List<AccountantGlamsSum> newAmsGlamsSums,
                                                                    List<AccountantGlamsSum> oldAmsGlamsSums,
                                                                    AccountManagementInfo accManInfo,String ind) {
        Map<String, List<TAmsGlamsSumDTO>> map = new HashMap<>(8);

        // 处理新的流水号
        if (CollectionUtils.isNotEmpty(newAmsGlamsSums)) {
            // 按业务流水号分组
            map = BeanMapping.copyList(newAmsGlamsSums, TAmsGlamsSumDTO.class).stream()
                    .filter(tAmsGlamsSum -> tAmsGlamsSum.getGlobalFlowNo() != null)
                    .collect(Collectors.groupingBy(TAmsGlamsSumDTO::getGlobalFlowNo));
            // 按流水号增加汇总流水1
            map.forEach((globalFlowNo, t) -> {
                    List<TAmsGlamsSumDTO> addGlAmsSums = Lists.newArrayList();
                    //  汇总
                    t.forEach(glamsSum -> addGlAmsSum(t, addGlAmsSums, glamsSum, accManInfo,ind));
                    t.addAll(addGlAmsSums);
                });
            // 按流水号增加abs汇总流水
            map.forEach((globalFlowNo, t) -> {
                List<TAmsGlamsSumDTO> absDebitGlAmsSum = t.stream()
                        .filter(s -> (("2".equals(s.getOrigTxnAbsInd()) || "4".equals(s.getOrigTxnAbsInd()))) && "3".equals(s.getTxnInd()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(absDebitGlAmsSum)) {
                    return;
                }
                // 相同资产包编号,出表状态增加汇总流水
                Map<String, List<TAmsGlamsSumDTO>> resultMap = absDebitGlAmsSum.stream()
                        .collect(Collectors.groupingBy(o -> o.getAbsStatus() + o.getAssetNo()));
                // 借方余额变动交易码
//                TransactionCodeResDTO transactionCodeResDTO = transactionCodeService.findTransactionCode(accManInfo.getOrganizationNumber(), "OV999");
//                TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(accManInfo.getOrganizationNumber(), transactionCodeResDTO.getTransactionTypeCode());
                logger.info("Calling transactionTypeService.findTransactionType: org={}, txnCode=OV999", OrgNumberUtils.getOrg());
                TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(OrgNumberUtils.getOrg(), "OV999");
                logger.info("transactionTypeService.findTransactionType completed: transactionType={}", transactionType != null ? "found" : "null");
                String txnBalChgDb = transactionType.getDebitBalChangeCode();

                resultMap.values().forEach(amsGlamsSums -> {
                    TAmsGlamsSumDTO newGlamsSum = reduceAssertGlamsSum(amsGlamsSums.get(0));
                    // 入账金额（绝对值）累计
                    BigDecimal postAmt = amsGlamsSums.stream()
                                    .map(tAmsGlamsSum -> tAmsGlamsSum.getPostingAmt().abs())
                                    .reduce(BigDecimal::add)
                                    .orElse(BigDecimal.ZERO);
                    newGlamsSum.setPostingAmt(postAmt);
                    newGlamsSum.setTxnCode(txnBalChgDb);
                    newGlamsSum.setFinanceStatus("4");
                    newGlamsSum.setPriceTaxFlg("N");
                    newGlamsSum.setProcessInd("0");
                    newGlamsSum.setChannelId("2");
                    newGlamsSum.setBalType(transactionType.getBalType());
                    newGlamsSum.setTransactionAttribute("0");
                    newGlamsSum.setDebitCreditIndicator("T");
                    newGlamsSum.setAmortizeInd("N");
                    newGlamsSum.setTransactionTypeCode("OV999");
                    //newGlamsSum.setSubchannelId();
                    t.add(newGlamsSum);
                });
            });
        }

        /**旧未处理汇总流水处理*/
        if (CollectionUtils.isNotEmpty(oldAmsGlamsSums)) {
            List<TAmsGlamsSumDTO> tAmsGlamsSumDtos = BeanMapping.copyList(oldAmsGlamsSums, TAmsGlamsSumDTO.class);
            for (TAmsGlamsSumDTO oldAmsGlamsSum : tAmsGlamsSumDtos) {
                // 已添加汇总是否存在同流水号汇总流水
                List<TAmsGlamsSumDTO> amsGlamsSums = map.get(oldAmsGlamsSum.getGlobalFlowNo());
                if (CollectionUtils.isEmpty(amsGlamsSums)) {
                    amsGlamsSums = new ArrayList<>();
                    amsGlamsSums.add(oldAmsGlamsSum);
                    map.put(oldAmsGlamsSum.getGlobalFlowNo(), amsGlamsSums);
                } else {
                    amsGlamsSums.add(oldAmsGlamsSum);
                }
            }
        }

        return new ArrayList<>(map.values());
    }

    /**
     * 复制并修改汇总流水标记
     * @param tAmsGlamsSum
     * @return
     */
    private TAmsGlamsSumDTO reduceAssertGlamsSum(TAmsGlamsSumDTO tAmsGlamsSum) {
        TAmsGlamsSumDTO copyGlamsSum = BeanMapping.copy(tAmsGlamsSum, TAmsGlamsSumDTO.class);
        // 标记会计abs
        copyGlamsSum.setModuleFlag("A");
        // abs分录
        copyGlamsSum.setTxnInd("3");
        // 入账日期
        copyGlamsSum.setPostingDate(tAmsGlamsSum.getPostingDate());
        // 帐产品号
        copyGlamsSum.setAcctLogo(tAmsGlamsSum.getAcctLogo());
        // 原始交易码
        copyGlamsSum.setTxnCodeOrig(tAmsGlamsSum.getTxnCodeOrig());
        // 渠道id
        copyGlamsSum.setChannelId(tAmsGlamsSum.getChannelId());

        return copyGlamsSum;
    }

    /**
     * 会计流水表按规则增加
     * @param amsGlamsSumList       当前流水的会计流水汇总
     * @param addGlAmsSums          新增汇总流水
     * @param amsGlamsSum0              当前处理汇总流水
     * @param accManInfo            管理帐号
     */
    private void addGlAmsSum(List<TAmsGlamsSumDTO> amsGlamsSumList,
                             List<TAmsGlamsSumDTO> addGlAmsSums,
                             TAmsGlamsSumDTO amsGlamsSum0,
                             AccountManagementInfo accManInfo,String ind) {
        // 不处理 分期abs(9)、全账户abs(B)、不良abs(C)
        if (StringUtils.equalsAny(amsGlamsSum0.getModuleFlag(), "9", "B", "C")) {
            return;
        }
        // 非结转（4）且 未科目结转（2）
        if (!"4".equals(amsGlamsSum0.getModuleFlag()) && "2".equals(amsGlamsSum0.getBalProcessInd())) {
            // 获取交易码参数
            logger.info("Calling transactionCodeService.findTransactionCode: organizationNumber={}, txnCodeOrig={}", amsGlamsSum0.getOrganizationNumber(), amsGlamsSum0.getTxnCodeOrig());
            TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(amsGlamsSum0.getOrganizationNumber(), amsGlamsSum0.getTxnCodeOrig());
            logger.info("transactionCodeService.findTransactionCode completed: transactionCode={}", transactionCode != null ? "found" : "null");
            if (transactionCode == null) {
                logger.warn("Original transaction code does not exist: {}", amsGlamsSum0.getTxnCodeOrig());
                return;
            }

            // 处理借记流水
            if ("D".equals(transactionCode.getDebitCreditIndicator())) {
                // 编辑一笔汇总
                TAmsGlamsSumDTO copyAmsGlamsSumDTO = reduceAssertGlamsSum(amsGlamsSum0);
                // 查询交易类型响应参数
                //todo 借贷分离暂时注释，赋值为null，待业务确认
//                TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(copyAmsGlamsSumDTO.getOrganizationNumber(), transactionCode.getTransactionTypeCode());
                TransactionTypeResDTO transactionType = null;
                if (transactionType == null) {
//                    LOG.warn("原始交易码对应交易类型不存在:{}", transactionCode.getTransactionTypeCode());
                    return;
                }
                if ("M".equals(ind)){
                    if ("0".equals(accManInfo.getFinanceStatus())) {
                        // 应计转abs
                        copyAmsGlamsSumDTO.setFinanceStatus("A");
                    } else if ("3".equals(accManInfo.getFinanceStatus())) {
                        // 应计转abs
                        copyAmsGlamsSumDTO.setFinanceStatus("E");
                    } if ("1".equals(accManInfo.getFinanceStatus())) {
                        // 非应计转abs
                        copyAmsGlamsSumDTO.setFinanceStatus("B");
                    }
                }else {
                    logger.info("Acquirer data received, no assignment: ind={}", ind);
                }
                // 标记结转交易码
                copyAmsGlamsSumDTO.setTxnCode(transactionType.getJzCode());
                // 标记应计状态
                

                // 标记非利息
                copyAmsGlamsSumDTO.setInterestInd("0");
                // 标记不价码分离
                copyAmsGlamsSumDTO.setPriceTaxFlg("N");
                // 标记已科目结转
                copyAmsGlamsSumDTO.setBalProcessInd("1");
                // 标记未处理
                copyAmsGlamsSumDTO.setProcessInd("0");
                // 内生交易
                copyAmsGlamsSumDTO.setChannelId("2");
                // 设置余额类型
                copyAmsGlamsSumDTO.setBalType(transactionType.getBalType());
                // 标记结转交易
                copyAmsGlamsSumDTO.setTransactionAttribute("0");
                // 标记结转（借贷属性）
                copyAmsGlamsSumDTO.setDebitCreditIndicator("T");
                // 标记非递延
                copyAmsGlamsSumDTO.setAmortizeInd("N");
                // 标记设置交易类型
                //todo 借贷分离暂时注释，待业务确认
//                copyAmsGlamsSumDTO.setTransactionTypeCode(transactionCode.getTransactionTypeCode());

                addGlAmsSums.add(copyAmsGlamsSumDTO);
            }
        }

        // abs已出表或封包 且 资产编码不为空
        if (StringUtils.equalsAny(amsGlamsSum0.getAbsStatus(), "A", "F") && amsGlamsSum0.getAssetNo() != null) {
            boolean reduceamsGlamsSum = false;

            // 汇总交易流水条件
            //1. 模块标志 = 4
            //2. 交易标记 != 0
            //3. 交易标记 = 0 且 原始交易码 = 借贷
            if (!"4".equals(amsGlamsSum0.getModuleFlag()) && "0".equals(amsGlamsSum0.getTxnInd())) {
                logger.info("Calling transactionCodeService.findTransactionCode: organizationNumber={}, txnCodeOrig={}", amsGlamsSum0.getOrganizationNumber(), amsGlamsSum0.getTxnCodeOrig());
                TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(amsGlamsSum0.getOrganizationNumber(), amsGlamsSum0.getTxnCodeOrig());
                logger.info("transactionCodeService.findTransactionCode completed: transactionCode={}", transactionCode != null ? "found" : "null");
                if ("D".equals(transactionCode.getDebitCreditIndicator())) {
                    reduceamsGlamsSum = true;
                }
            } else {
                reduceamsGlamsSum = true;
            }

            if (reduceamsGlamsSum) {
                // 编辑一笔汇总
                TAmsGlamsSumDTO amsGlamsSum = reduceAssertGlamsSum(amsGlamsSum0);
                amsGlamsSum.setPostingAmt(amsGlamsSum0.getPostingAmt().negate());
                amsGlamsSum.setChannelId("2");
                amsGlamsSum.setProcessInd("0");
                addGlAmsSums.add(amsGlamsSum);
            }
        }

        // 原始交易流水
        Optional<TAmsGlamsSumDTO> amsGlamsSumOp = amsGlamsSumList.stream()
                .filter(tAmsGlamsSum -> "0".equals(tAmsGlamsSum.getTxnInd()))
                .findFirst();
        if (!amsGlamsSumOp.isPresent()) {
            return;
        }
        boolean result =
                ("DOV99".equals(amsGlamsSum0.getTxnCode()) || "COV99".equals(amsGlamsSum0.getTxnCode()))
                        && !"4".equals(amsGlamsSum0.getModuleFlag())
                        && "1".equals(amsGlamsSum0.getOrigTxnAbsInd());
        if (result) {
            // 借方余额变动交易码
            logger.info("Calling transactionCodeService.findTransactionCode: organizationNumber={}, txnCode=OV999", amsGlamsSum0.getOrganizationNumber());
            TransactionCodeResDTO transactionCodeResDTO = transactionCodeService.findTransactionCode(amsGlamsSum0.getOrganizationNumber(), "OV999");
            logger.info("transactionCodeService.findTransactionCode completed: transactionCodeResDTO={}", transactionCodeResDTO != null ? "found" : "null");
            //todo 借贷分离暂时注释，赋值为null，待业务确认
//            TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(amsGlamsSum0.getOrganizationNumber(), transactionCodeResDTO.getTransactionTypeCode());
            TransactionTypeResDTO transactionType = new TransactionTypeResDTO();
                    String debitBalChangeCode = transactionType.getDebitBalChangeCode();
            // 原始交易
            TAmsGlamsSumDTO amsGlamsSumP = amsGlamsSumOp.get();

            // 编辑一笔汇总
            TAmsGlamsSumDTO amsGlamsSum = reduceAssertGlamsSum(amsGlamsSum0);
            amsGlamsSum.setTxnCode(debitBalChangeCode);
            // 核算状态
            amsGlamsSum.setFinanceStatus("4");
            // 价税分离标识
            amsGlamsSum.setPriceTaxFlg("N");
            amsGlamsSum.setAbsStatus(amsGlamsSumP.getAbsStatus());
            amsGlamsSum.setAssetNo(amsGlamsSumP.getAssetNo());
            amsGlamsSum.setOrderId(amsGlamsSumP.getOrderId());
            // 渠道id
            amsGlamsSum.setChannelId("2");
            amsGlamsSum.setBalType(transactionType.getBalType());
            // 交易属性
            amsGlamsSum.setTransactionAttribute("0");
            // 借贷记属性
            amsGlamsSum.setDebitCreditIndicator("T");
            // 递延标志
            amsGlamsSum.setAmortizeInd("N");
            // 交易类型
            amsGlamsSum.setTransactionTypeCode("OV999");
            // 未处理标志
            amsGlamsSum.setProcessInd("0");
            addGlAmsSums.add(amsGlamsSum);
        }
    }

    /**
     * 增值税分户余额处理
     * @param tAmsGlvchers  待处理传票
     * @return
     */
    private Pair<List<TAmsGlvatbalDTO>, List<TAmsGlvatbalDTO>> glVatBalHandle(List<TAmsGlvcherDTO> tAmsGlvchers) {
        Pair<List<TAmsGlvatbalDTO>, List<TAmsGlvatbalDTO>> result = Pair.of(Lists.newArrayList(), Lists.newArrayList());
        // 会计科目
        logger.info("Calling pmsGlacgnService.findByCheckOption: checkOption=12");
        List<TPmsGlacgnDTO> tPmsGlacgns = pmsGlacgnService.findByCheckOption("12");
        logger.info("pmsGlacgnService.findByCheckOption completed: tPmsGlacgnsSize={}", tPmsGlacgns != null ? tPmsGlacgns.size() : 0);
        // 系统当前日期
        logger.info("Calling organizationInfoService.findOrganizationInfo: org={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "null");
        LocalDate today = organizationInfo.getToday();
        // 遍历处理传票
        tAmsGlvchers.stream()
                .filter(tAmsGlvcher -> {
                    List<TPmsGlacgnDTO> resultAcgn = tPmsGlacgns.parallelStream()
                            .filter(pmsGlacgn -> StringUtils.equals(pmsGlacgn.getGlAcct(), tAmsGlvcher.getGlAcct())
                                    && StringUtils.equals(pmsGlacgn.getCurrCode(), tAmsGlvcher.getCurrCode()))
                            .collect(Collectors.toList());
                    return CollectionUtils.isNotEmpty(resultAcgn);
                })
                .collect(Collectors.groupingBy(tAmsGlvcher -> tAmsGlvcher.getAccountManagementId()
                        + tAmsGlvcher.getGlAcct()
                        + tAmsGlvcher.getOrganizationNumber()
                        + tAmsGlvcher.getBranchid()
                        + tAmsGlvcher.getCurrCode()))
                .values()
                .forEach(list -> {
                    TAmsGlvatbalDTO tAmsGlvatbal = new TAmsGlvatbalDTO();
                    // 生成新的id
                    tAmsGlvatbal.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                    tAmsGlvatbal.setCreateTime(LocalDateTime.now());
                    tAmsGlvatbal.setUpdateTime(LocalDateTime.now());
                    tAmsGlvatbal.setUpdateBy(AccountantConstants.DEFAULT_USER);
                    tAmsGlvatbal.setVersionNumber(1L);
                    tAmsGlvatbal.setDbal(BigDecimal.ZERO);
                    tAmsGlvatbal.setCbal(BigDecimal.ZERO);
                    tAmsGlvatbal.setCrCurr(BigDecimal.ZERO);
                    tAmsGlvatbal.setDrCurr(BigDecimal.ZERO);
                    tAmsGlvatbal.setUpdateDate(today);
                    for (TAmsGlvcherDTO tAmsGlvcher : list) {
                        tAmsGlvatbal.setOrganizationNumber(tAmsGlvcher.getOrganizationNumber());
                        tAmsGlvatbal.setBranchid(tAmsGlvcher.getBranchid());
                        tAmsGlvatbal.setGlAcct(tAmsGlvcher.getGlAcct());
                        tAmsGlvatbal.setAccountManagementId(tAmsGlvcher.getAccountManagementId());
                        tAmsGlvatbal.setCurrCode(tAmsGlvcher.getCurrCode());
                        if ("D".equals(tAmsGlvcher.getDrcr())) {
                            tAmsGlvatbal.setDbal(AmountUtil.addAmount(tAmsGlvatbal.getDbal(), tAmsGlvcher.getGlAmount()));
                            tAmsGlvatbal.setDrCurr(AmountUtil.addAmount(tAmsGlvatbal.getDrCurr(), tAmsGlvcher.getGlAmount()));
                        }
                        if ("C".equals(tAmsGlvcher.getDrcr())) {
                            tAmsGlvatbal.setCbal(AmountUtil.addAmount(tAmsGlvatbal.getCbal(), tAmsGlvcher.getGlAmount()));
                            tAmsGlvatbal.setCrCurr(AmountUtil.addAmount(tAmsGlvatbal.getCrCurr(), tAmsGlvcher.getGlAmount()));
                        }
                    }
                    // 处理增值税分户余额
                    buildGlVatBalMap(result, tAmsGlvatbal,today);
                });
        return result;
    }

    /**
     * 比较生成新或更新增值税余额表
     * @param result            统计结果
     * @param tAmsGlvatbal      当前传票生成的增值税余额
     * @param today             系统处理日
     */
    private void buildGlVatBalMap(Pair<List<TAmsGlvatbalDTO>, List<TAmsGlvatbalDTO>> result, TAmsGlvatbalDTO tAmsGlvatbal, LocalDate today) {
        // 查询增值税分户余额表
        AccountantGlvatbal amsGlvatbal = amsGlvatbalSelfMapper.selectByParams(tAmsGlvatbal.getOrganizationNumber(),
                                                                        tAmsGlvatbal.getBranchid(),
                                                                        tAmsGlvatbal.getAccountManagementId(),
                                                                        tAmsGlvatbal.getGlAcct(),
                                                                        tAmsGlvatbal.getCurrCode());
        // 新增值税余额
        if (amsGlvatbal == null) {
            result.getLeft().add(tAmsGlvatbal);
        }else {
            TAmsGlvatbalDTO newAmsGlvatbal = new TAmsGlvatbalDTO();
            newAmsGlvatbal.setId(amsGlvatbal.getId());
            newAmsGlvatbal.setDbal(AmountUtil.addAmount(amsGlvatbal.getDbal(), tAmsGlvatbal.getDbal()));
            newAmsGlvatbal.setCbal(AmountUtil.addAmount(amsGlvatbal.getCbal(), tAmsGlvatbal.getCbal()));
            if (today.compareTo(amsGlvatbal.getUpdateDate())!=0){
                newAmsGlvatbal.setUpdateDate(today);
                newAmsGlvatbal.setCrCurr(tAmsGlvatbal.getCrCurr());
                newAmsGlvatbal.setDrCurr(tAmsGlvatbal.getDrCurr());
            }
            newAmsGlvatbal.setUpdateTime(LocalDateTime.now());
            newAmsGlvatbal.setUpdateBy(AccountantConstants.DEFAULT_USER);
            newAmsGlvatbal.setVersionNumber(amsGlvatbal.getVersionNumber() + 1);
            result.getRight().add(newAmsGlvatbal);
        }
    }

    /**
     * 通过汇总表中的规则因子计算出表id
     */
    private String executeRule(String ruleType, Map map) {


        DataInputDTO dataInputDTO = new DataInputDTO();
        dataInputDTO.setRuleType(ruleType);
        dataInputDTO.setInput(map);

        ImmutablePair<String, TxnRuleMatcher> matcherImmutablePair = RULE_MATCHER_MANAGER_MAP.get(dataInputDTO.getRuleType());

        if (matcherImmutablePair == null) {
            TxnRuleMatcher txnRuleMatcher = RuleMatcherManager.getMatcher(dataInputDTO.getRuleType(), OrgNumberUtils.getOrg());
            matcherImmutablePair = ImmutablePair.of("",txnRuleMatcher);
            RULE_MATCHER_MANAGER_MAP.put(dataInputDTO.getRuleType(), matcherImmutablePair);
        }


        if (matcherImmutablePair != null && matcherImmutablePair.getRight() != null) {
            Map<String, Object> result = matcherImmutablePair.getRight().execute(dataInputDTO);

            if (!result.isEmpty() && result.containsKey(AccountantConstants.TABLE_ID)) {
                return result.get(AccountantConstants.TABLE_ID) == null ? "" : result.get(AccountantConstants.TABLE_ID).toString();
            } else {
                return null;
            }
        }

        return null;
    }
}
