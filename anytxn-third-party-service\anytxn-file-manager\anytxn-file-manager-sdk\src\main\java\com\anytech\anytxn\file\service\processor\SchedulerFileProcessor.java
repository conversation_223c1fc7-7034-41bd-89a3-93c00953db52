package com.anytech.anytxn.file.service.processor;

import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.constants.ShardingConstant;
import com.anytech.anytxn.common.core.utils.BaseContextHandler;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.file.config.SchedulerClientConfig;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.domain.model.FileManagerScanProcess;
import com.anytech.anytxn.file.enums.FileResponseDetailEnum;
import com.anytech.anytxn.file.enums.ScanFileProcessStatusEnum;
import com.anytech.anytxn.file.mapper.FileManagerScanProcessSelfMapper;
import com.anytech.anytxn.file.service.FileCopyService;
import com.anytech.anytxn.file.service.FileManagerScanProcessService;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Map;

/**
 * @description: 调用调度
 * @author: zhangnan
 * @create: 2021-03-25
 **/
@Service
public class SchedulerFileProcessor implements IFileProcessor {

    private static final Logger logger = LoggerFactory.getLogger(SchedulerFileProcessor.class);

    @Autowired
    private FileManagerScanProcessService fileManagerScanProcessService;
    @Autowired
    private FileManagerScanProcessSelfMapper fileManagerScanProcessSelfMapper;
    @Autowired
    private SchedulerClientConfig schedulerClientConfig;

    @Autowired
    private FileCopyService fileCopyService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    public boolean process(FileManagerScanParam param, File file) {
        String tenantId = (String) BaseContextHandler.get(ShardingConstant.TENANT_ID);
        //查询文件是否在处理
        FileManagerScanProcess fileManagerScanProcess = null;
        if (param.getLastScanProcessId() != null) {
            fileManagerScanProcess = fileManagerScanProcessService.selectByPrimaryKey(param.getLastScanProcessId());
        }

        //case 1: 文件已经处理，被移走，但又有新文件被扫描到的情况

        //case 1-1: 调度任务未结束的情景，等待,do nothing
        if (fileManagerScanProcess != null && ScanFileProcessStatusEnum.RUNNING.getcode() == fileManagerScanProcess.getScanStatus()) {
            fileManagerScanProcessService.updateParamStatus(FileResponseDetailEnum.FILE_SACAN_STATUS_601, param);
            return false;
        }

        //case 1-2 调度已OK的情况
        if (fileManagerScanProcess != null && (ScanFileProcessStatusEnum.COMPLETE.getcode() == fileManagerScanProcess.getScanStatus() || ScanFileProcessStatusEnum.COMPLETE_MANUAL.getcode() == fileManagerScanProcess.getScanStatus())) {
            //可以继续执行新文件的处理了
            return callSchedulerFirst(tenantId, param, file);
        }

        //case 2 调用调度失败的情况，不删除文件，再次扫描到，再次执行调度
        if (fileManagerScanProcess != null && (ScanFileProcessStatusEnum.SCHEDULER_FAIL.getcode() == fileManagerScanProcess.getScanStatus() ||
                ScanFileProcessStatusEnum.SCHEDULER_ERROR.getcode() == fileManagerScanProcess.getScanStatus())) {
            return callSchedulerAgain(tenantId, param, file, fileManagerScanProcess);
        }

        //case 3 第一次运行的情况
        if (fileManagerScanProcess == null) {
            return callSchedulerFirst(tenantId, param, file);
        }
        logger.error("oops...");
        return false;
    }


    //第一次调用
    private boolean callSchedulerFirst(String tenantId, FileManagerScanParam param, File file) {

        String md5 = "";
        try {
            md5 = fileCopyService.getSha256(file);
        } catch (IOException e) {
            logger.error("MD5 generation failed", e);
            fileManagerScanProcessService.updateParamStatus(FileResponseDetailEnum.FILE_SACAN_STATUS_401, param);
            return false;
        }
        String scheduleSequenceNumber = sequenceIdGen.generateId(tenantId);
        if (!fileCopyService.doCopyFile(param, file)) {
            return false;
        }
        ScanFileProcessStatusEnum resultStatus = doCallScheduler(scheduleSequenceNumber, param.getScheduleTask());
        if (resultStatus == ScanFileProcessStatusEnum.RUNNING) {
            try {
                delFile(file);
            } catch (IOException e) {
                logger.error("File deletion failed, continuing", e);
            }
        }
        FileManagerScanProcess fileManagerScanProcess = fileManagerScanProcessService.insertOne(file, scheduleSequenceNumber, resultStatus, param, md5);
        param.setLastScanProcessId(fileManagerScanProcess.getId());
        fileManagerScanProcessService.updateParamStatus(FileResponseDetailEnum.FILE_SACAN_STATUS_304, param);
        return true;
    }


    //再次执行
    private boolean callSchedulerAgain(String tenantId, FileManagerScanParam param, File file, FileManagerScanProcess fileManagerScanProcess) {
        //根据上次执行状态决定是否重新生成sequence number
        if (ScanFileProcessStatusEnum.SCHEDULER_FAIL.getcode() == fileManagerScanProcess.getScanStatus()) {
            fileManagerScanProcess.setScheduleSequenceNumber(sequenceIdGen.generateId(tenantId));

        }

        if (!fileCopyService.doCopyFile(param, file)) {
            return false;
        }
        //再次执行调度
        ScanFileProcessStatusEnum resultStatus = doCallScheduler(fileManagerScanProcess.getScheduleSequenceNumber(), param.getScheduleTask());
        if (resultStatus == ScanFileProcessStatusEnum.RUNNING) {
            //完全成功的情况，删除文件不再触发，否则保留文件继续重试
            try {
                delFile(file);
            } catch (IOException e) {
                logger.error("File deletion failed, continuing", e);
            }

        }
        fileManagerScanProcessService.updateScanProcess(resultStatus, fileManagerScanProcess);
        fileManagerScanProcessService.updateParamStatus(FileResponseDetailEnum.FILE_SACAN_STATUS_304, param);
        return true;
    }


    private ScanFileProcessStatusEnum doCallScheduler(String scheduleSequenceNumber, String planKey) {
        AnyTxnHttpResponse<Map<String, Object>> res = schedulerClientConfig.launchPlan(scheduleSequenceNumber, planKey);
        if (res != null && res.getCode().equals(SchedulerClientConfig.REQ_SUCCESS)) {
            String reqState = (String) res.getData().get("reqState");
            if (SchedulerClientConfig.REQ_STATE_SUCCESS.equals(reqState)) {
                //调用成功
                return ScanFileProcessStatusEnum.RUNNING;
            } else if (SchedulerClientConfig.REQ_STATE_FAIL.equals(reqState)) {
                String failMessage = (String) res.getData().get("failMessage");
                //失败或者正在运行
                return ScanFileProcessStatusEnum.SCHEDULER_FAIL;
            }
        }
        //调用失败
        return ScanFileProcessStatusEnum.SCHEDULER_ERROR;

    }


    private void delFile(File file) throws IOException {
        Files.delete(file.toPath());
    }
}
