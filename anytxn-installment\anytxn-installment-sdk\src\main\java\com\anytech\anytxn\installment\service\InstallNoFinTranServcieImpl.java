package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.AuthMatchIndicatorEnum;
import com.anytech.anytxn.installment.base.enums.DebitCreditIndEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentOrderStatusEnum;
import com.anytech.anytxn.installment.base.enums.PostMethodEnum;
import com.anytech.anytxn.installment.base.enums.ReleaseAuthAmountEnum;
import com.anytech.anytxn.installment.base.enums.RepostFromSuspendEnum;
import com.anytech.anytxn.installment.base.enums.TransactionChannelEnum;
import com.anytech.anytxn.installment.base.enums.TransactionFlagEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.monetary.service.CustReconciliationControlServiceImpl;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanMapper;
import com.anytech.anytxn.business.dao.installment.mapper.SingleInstallMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.dao.installment.model.SingleInstall;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.domain.dto.SingleInstallDTO;
import com.anytech.anytxn.installment.base.domain.dto.SingleInstallSearchKeyDTO;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallNoFinTranServcie;
import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallEarlyTerminationParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmTransactionTypeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionType;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.mapper.PostedTranAccountRelationInfoSelfMapper;
import com.anytech.anytxn.transaction.base.domain.model.PostedTranAccountRelationInfo;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-06-11 10:44
 **/
@Service
public class InstallNoFinTranServcieImpl implements IInstallNoFinTranServcie {
    private Logger logger = LoggerFactory.getLogger(InstallNoFinTranServcieImpl.class);
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private SingleInstallMapper singleInstallMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInstallPlanService installPlanService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private InstallPlanMapper installPlanMapper;
    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private IInstallEarlyTerminationParmService installEarlyTerminationParmService;
    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private CustReconciliationControlServiceImpl custReconciliationControlService;
    @Autowired
    private PostedTranAccountRelationInfoSelfMapper postedTranAccountRelationInfoSelfMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private ParmTransactionTypeSelfMapper parmTransactionTypeSelfMapper;

    @Resource
    private InstallManager installManager;

    /**
     * 分期提前还款及退货、拒付
     *
     * @param installEntryDTO
     * @return InstallAutoSignDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reimburseOrRuturnOrNoPat(InstallEntryDTO installEntryDTO) {
        logger.info("Installment early repayment, return goods, or refusal payment started");
        //必输项校验
        checkInstallParm(installEntryDTO);
        String transactionFlag = installEntryDTO.getTransactionFlag();
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(installEntryDTO.getOrderId());

        if (installOrder == null) {
            logger.error("Order not found by ID: orderId={}", installEntryDTO.getOrderId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
        }

        if(!Objects.equals(installEntryDTO.getCardNumber(), installOrder.getCardNumber())){
            logger.error("Card number and order number do not match: cardNumber={}, orderCardNumber={}", installEntryDTO.getCardNumber(), installOrder.getCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_DATE_ERROR, InstallRepDetailEnum.CA_OR);
        }

        if (Objects.equals(TransactionFlagEnum.EARLY_SETTLEMENT.getCode(), transactionFlag)) {
            checkNormalStatus(installOrder.getStatus());
            installOrder.setStatus(InstallmentOrderStatusEnum.ACTIVE_ADVANCESETTLEMENT_STATUS.getCode());
        }
        if (Objects.equals(TransactionFlagEnum.RETURNGOODS.getCode(), transactionFlag)) {
            checkNormalStatus(installOrder.getStatus());
            installOrder.setStatus(InstallmentOrderStatusEnum.REFUNDS_STATUS.getCode());
        }
        if (Objects.equals(TransactionFlagEnum.REFUSE_PAYMENT.getCode(), transactionFlag)) {
            checkNormalStatus(installOrder.getStatus());
            installOrder.setStatus(InstallmentOrderStatusEnum.REFUSEPAYMENT_STATUS.getCode());
        }

        if (Objects.equals(TransactionFlagEnum.PT.getCode(), transactionFlag)
                || Objects.equals(TransactionFlagEnum.PP.getCode(), transactionFlag)
                || Objects.equals(TransactionFlagEnum.PR.getCode(), transactionFlag)) {
            checkNormalStatus(installOrder.getStatus());
        }

        String cardNumber = installOrder.getCardNumber();
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(cardNumber, OrgNumberUtils.getOrg());
        CustReconciliationControlDTO control = custReconciliationControlService.getControl(cardAuthorizationInfo.getPrimaryCustomerId(), cardAuthorizationInfo.getOrganizationNumber());

        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", cardAuthorizationInfo.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(cardAuthorizationInfo.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);
        LocalDate date = custReconciliationControlService.getBillingDate(control, organizationInfo.getAccruedThruDay(), organizationInfo.getToday(), organizationInfo.getNextProcessingDay());

        if (Objects.equals(TransactionFlagEnum.REVERT_EARLY_SETTLEMENT.getCode(), transactionFlag)) {
            checkActiveAdvanceStatus(installOrder.getStatus());
            if (installOrder.getUnpostedAmount().compareTo(BigDecimal.ZERO) != 0){
                installOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
            }else {
                logger.info("Calling installPlanService.planByOrderId: orderId={}", installEntryDTO.getOrderId());
                List<InstallPlanDTO> installPlanDtos = installPlanService.planByOrderId(installEntryDTO.getOrderId());
                logger.info("installPlanService.planByOrderId completed: planSize={}", installPlanDtos != null ? installPlanDtos.size() : 0);
                if (!CollectionUtils.isEmpty(installPlanDtos)){
                    Map<String, Object> map = updateInstallPlan(installPlanDtos, date);
                    String prepaymentFeeFlag = revertEarlySettlement(installPlanDtos, installOrder, map);
                    updateInstallOrder(installOrder, map, prepaymentFeeFlag);
                }
            }
        }
        if (Objects.equals(TransactionFlagEnum.REVERT_RETURNGOODS.getCode(), transactionFlag)) {
            checkRefundsStatus(installOrder.getStatus());
            if (installOrder.getUnpostedAmount().compareTo(BigDecimal.ZERO) != 0){
                installOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
            }else {
                logger.error("Return goods have been processed, cancellation is not allowed");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_PROCESSED_REFUNDS_NOT_SUPPORT_REFUNDS_CANCEL);
            }
        }
        if(TransactionFlagEnum.PT.getCode().equals(transactionFlag)){
            installOrder.setRemainTerm(installEntryDTO.getRemainTerm());
            installOrder.setStatus("A");
            installOrder.setEarlySettleAmount(installEntryDTO.getEarlySettleAmount());
        }
        if(TransactionFlagEnum.PP.getCode().equals(transactionFlag)){
            installOrder.setRemainTerm(installEntryDTO.getRemainTerm());
            installOrder.setStatus("B");
            installOrder.setEarlySettleAmount(installEntryDTO.getEarlySettleAmount());
        }
        if(TransactionFlagEnum.PR.getCode().equals(transactionFlag)){
            installOrder.setStatus("C");
            installOrder.setEarlySettleAmount(installEntryDTO.getEarlySettleAmount());
        }
//        String accountId = installEntryService.getAccountId(installEntryDTO.getCardNumber(), installOrder.getInstallmentCcy());
        installEntryDTO.setAccountManagementId(installOrder.getAccountManagementId());
        installOrder.setStatusUpdateDate(date);
        installOrder.setTransactionChannel(TransactionChannelEnum.TRANS_MODIFY.getCode());
        installOrder.setUpdateBy(LoginUserUtils.getLoginUserName());
        logger.info("Installment order status modified: orderId={}, operator={}", installOrder.getOrderId(), LoginUserUtils.getLoginUserName());
        try {
            int i = installOrderMapper.updateByPrimaryKeySelective(installOrder);
            logger.info("Installment early repayment, return goods, or refusal payment completed");
            return i > 0;
        } catch (Exception e) {
            logger.error("Failed to update database for installment early repayment, return goods, or refusal payment processing");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
        }
    }

    private void checkNormalStatus(String status){
        //订单状态是否正常
        if (!Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), status)) {
            logger.error("Order status is not normal: status={}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_STATUS_NOT_CORRECT_FAULT);
        }
    }

    private void checkActiveAdvanceStatus(String status){
        if (!Objects.equals(InstallmentOrderStatusEnum.ACTIVE_ADVANCESETTLEMENT_STATUS.getCode(), status)) {
            logger.error("Active advance settlement cancellation is not allowed: status={}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_NOT_SUPPORT_ACTIVE_ADVANCE_SETTLEMENT);
        }
    }

    private void checkRefundsStatus(String status){
        if (!Objects.equals(InstallmentOrderStatusEnum.REFUNDS_STATUS.getCode(), status)) {
            logger.error("Return goods cancellation is not allowed: status={}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_NOT_SUPPORT_REFUNDS_CANCEL);
        }
    }

    /**
     * 主动提前结清撤销处理
     */
    private String revertEarlySettlement(List<InstallPlanDTO> installPlanDtos,
                                       InstallOrder installOrder,
                                       Map<String, Object> map){
        String principalReversalTransCode = "";
        String feeReversalTransactionCode = "";
        String feeTransactionCode = "";
        String prepaymentFeeFlag = "";
        logger.info("Calling installProductInfoService.findByIndex: organizationNumber={}, productCode={}", installOrder.getOrganizationNumber(), installOrder.getProductCode());
        InstallProductInfoResDTO productInfoResDTO = installProductInfoService.findByIndex(installOrder.getOrganizationNumber(), installOrder.getProductCode());
        logger.info("installProductInfoService.findByIndex completed: productCode={}", productInfoResDTO != null ? productInfoResDTO.getProductCode() : null);
        if (!ObjectUtils.isEmpty(productInfoResDTO)){
            logger.info("Calling installAccountingTransParmService.selectByIndex: organizationNumber={}, postingTransactionParmId={}", installOrder.getOrganizationNumber(), productInfoResDTO.getPostingTransactionParmId());
            InstallAccountingTransParmResDTO accountingTransParmResDTO = installAccountingTransParmService.selectByIndex(installOrder.getOrganizationNumber(), productInfoResDTO.getPostingTransactionParmId());
            logger.info("installAccountingTransParmService.selectByIndex completed: result={}", accountingTransParmResDTO != null ? "found" : "null");
            if (!ObjectUtils.isEmpty(accountingTransParmResDTO)){
                principalReversalTransCode = accountingTransParmResDTO.getPrincipalReversalTransCode();
                feeReversalTransactionCode = accountingTransParmResDTO.getFeeReversalTransactionCode();
                feeTransactionCode = accountingTransParmResDTO.getFeeTransactionCode();
            }
        }
        logger.info("Calling installEarlyTerminationParmService.findByIndex: organizationNumber={}, advancedEndParmId={}", installOrder.getOrganizationNumber(), productInfoResDTO.getAdvancedEndParmId());
        InstallEarlyTerminationParmResDTO earlyTerminationParmResDTO = installEarlyTerminationParmService.findByIndex(installOrder.getOrganizationNumber(), productInfoResDTO.getAdvancedEndParmId());
        logger.info("installEarlyTerminationParmService.findByIndex completed: prepaymentFeeFlag={}", earlyTerminationParmResDTO != null ? earlyTerminationParmResDTO.getPrepaymentFeeFlag() : null);
        if (!ObjectUtils.isEmpty(earlyTerminationParmResDTO)){
            prepaymentFeeFlag = earlyTerminationParmResDTO.getPrepaymentFeeFlag();
        }
        //本金调整
        buildRecorded(installOrder, principalReversalTransCode, map, installPlanDtos, InstallmentConstant.INSTALL_PRINCIPAL_ADJUST);
        //费用调整（分期提前终止参数表提前结清手续费处理方式：全收）
        if ("1".equals(prepaymentFeeFlag)){
            buildRecorded(installOrder, feeReversalTransactionCode, map, installPlanDtos, InstallmentConstant.INSTALL_FEE_ALL_ADJUST);
        }
        //费用调整（分期提前终止参数表提前结清手续费处理方式：全不收）
        if ("2".equals(prepaymentFeeFlag)){
            buildRecorded(installOrder, feeTransactionCode, map, installPlanDtos, InstallmentConstant.INSTALL_FEE_NOT_ALL_ADJUST);
        }
        //违约金调整（判断分期订单的提前还款违约金收取金额如果大于零，进行下面处理）
        if (installOrder.getReceivedPenatlyAmount().compareTo(BigDecimal.ZERO) > 0){
            buildRecorded(installOrder, feeReversalTransactionCode, map, installPlanDtos, InstallmentConstant.INSTALL_PENALTY_ADJUST);
        }
        return prepaymentFeeFlag;
    }

    private void updateInstallOrder(InstallOrder installOrder,
                                    Map<String, Object> map,
                                    String prepaymentFeeFlag){
        installOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
        installOrder.setUnpostedAmount((BigDecimal) map.get("sumTermAmount"));
        installOrder.setPostedTerm((Integer) map.get("sumPostedTermCount"));
        installOrder.setUnpostedFeeAmount((BigDecimal) map.get("sumFeeAmount"));
        installOrder.setPostedFeeTerm((Integer) map.get("sumPostedFeeCount"));
        if (!"0".equals(installOrder.getFeeDerateFlag())){
            installOrder.setDerateTerm((Integer) map.get("sumDerateTermCount"));
            installOrder.setDerateFeeAmount((BigDecimal) map.get("sumDerateFeeAmount"));
        }
        installOrder.setTotalReceivedFee((BigDecimal) map.get("reductionAmount"));
        installOrder.setTotalDerateFee((BigDecimal) map.get("sumDerateFeeAmount"));
        installOrder.setReceivedPenatlyAmount(BigDecimal.ZERO);
    }

    /**
     * 更新分期计划记录并合计更改了的金额和记录数
     * @param installPlanDtos 分期计划
     * @param nextProcessingDay 机构表下一处理日
     */
    private Map<String, Object> updateInstallPlan(List<InstallPlanDTO> installPlanDtos,
                                                  LocalDate nextProcessingDay){
        Map<String, Object> map = new HashMap<>(12);
        BigDecimal sumTermAmount = BigDecimal.ZERO;
        BigDecimal sumFeeAmount = BigDecimal.ZERO;
        BigDecimal sumDerateAmount = BigDecimal.ZERO;
        BigDecimal reductionAmount = BigDecimal.ZERO;
        BigDecimal sumDerateFeeAmount = BigDecimal.ZERO;
        int sumTermAmountCount = 0;
        int sumFeeAmountCount = 0;
        int sumDerateAmountCount = 0;
        int sumPostedFeeCount = 0;
        int sumDerateTermCount = 0;
        int sumPostedTermCount = 0;
        for (InstallPlanDTO installPlanDTO : installPlanDtos){
            if (installPlanDTO.getTermPostDate().isAfter(nextProcessingDay) || installPlanDTO.getTermPostDate().equals(nextProcessingDay)){
                installPlanDTO.setTermStutus("N");
                try {
                    installPlanMapper.updateByPrimaryKeySelective(BeanMapping.copy(installPlanDTO, InstallPlan.class));
                } catch (Exception e) {
                    logger.error("Failed to update database for installment plan update");
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
                }
                sumTermAmount = sumTermAmount.add(installPlanDTO.getTermAmount());
                sumFeeAmount = sumFeeAmount.add(installPlanDTO.getFeeAmount());
                sumDerateAmount = sumDerateAmount.add(installPlanDTO.getDerateAmount());
                sumTermAmountCount  = sumTermAmountCount + 1;
                sumFeeAmountCount = sumFeeAmountCount + 1;
                sumDerateAmountCount = sumDerateAmountCount + 1;
            }else if (installPlanDTO.getTermPostDate().isBefore(nextProcessingDay)){
                reductionAmount = reductionAmount.add(installPlanDTO.getFeeAmount().subtract(installPlanDTO.getDerateAmount()));
                sumPostedFeeCount = sumPostedFeeCount + 1;
                sumDerateTermCount = sumDerateTermCount + 1;
                sumDerateFeeAmount = sumDerateFeeAmount.add(installPlanDTO.getDerateAmount());
                sumPostedTermCount = sumPostedTermCount + 1;
            }
        }
        map.put("sumTermAmount", sumTermAmount);
        map.put("sumFeeAmount", sumFeeAmount);
        map.put("sumDerateAmount", sumDerateAmount);
        map.put("sumTermAmountCount", sumTermAmountCount);
        map.put("sumFeeAmountCount", sumFeeAmountCount);
        map.put("sumDerateAmountCount", sumDerateAmountCount);
        map.put("reductionAmount", reductionAmount);
        map.put("sumDerateFeeAmount", sumDerateFeeAmount);
        map.put("sumPostedFeeCount", sumPostedFeeCount);
        map.put("sumDerateTermCount", sumDerateTermCount);
        map.put("sumPostedTermCount", sumPostedTermCount);
        return map;
    }

    /**
     * 构造入账接口数据
     */
    private void buildRecorded(InstallOrder installOrder,
                                   String transactionCode,
                                   Map<String, Object> map,
                                   List<InstallPlanDTO> installPlanDtos,
                                   String adjustMethod){
        RecordedBO recorded = new RecordedBO();
        recorded.setTxnCardNumber(installOrder.getCardNumber());
        recorded.setTxnTransactionCode(transactionCode);
        recorded.setTxnTransactionCurrency(installOrder.getInstallmentCcy());
        if (InstallmentConstant.INSTALL_PRINCIPAL_ADJUST.equals(adjustMethod)){
            recorded.setTxnTransactionAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumTermAmount").toString()))));
            recorded.setTxnBillingAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumTermAmount").toString()))));
            recorded.setTxnSettlementAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumTermAmount").toString()))));
            recorded.setTxnTransactionDescription(InstallmentConstant.ADVANCE_SETTLEMENT_PRINCIPAL_REDUCTION);
        }else if(InstallmentConstant.INSTALL_FEE_ALL_ADJUST.equals(adjustMethod)){
            recorded.setTxnTransactionAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumFeeAmount").toString()))));
            recorded.setTxnBillingAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumFeeAmount").toString()))));
            recorded.setTxnSettlementAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("sumFeeAmount").toString()))));
            recorded.setTxnTransactionDescription(InstallmentConstant.ADVANCE_SETTLEMENT_FEE_REDUCTION);
        }else if(InstallmentConstant.INSTALL_FEE_NOT_ALL_ADJUST.equals(adjustMethod)){
            recorded.setTxnTransactionAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("reductionAmount").toString()))));
            recorded.setTxnBillingAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("reductionAmount").toString()))));
            recorded.setTxnSettlementAmount(BigDecimal.valueOf(Math.abs(Double.parseDouble(map.get("reductionAmount").toString()))));
            recorded.setTxnTransactionDescription(InstallmentConstant.ADVANCE_SETTLEMENT_FEE_REDUCTION);
        }else if (InstallmentConstant.INSTALL_PENALTY_ADJUST.equals(adjustMethod)){
            recorded.setTxnTransactionAmount(BigDecimal.valueOf(Math.abs(installOrder.getReceivedPenatlyAmount().doubleValue())));
            recorded.setTxnBillingAmount(BigDecimal.valueOf(Math.abs(installOrder.getReceivedPenatlyAmount().doubleValue())));
            recorded.setTxnSettlementAmount(BigDecimal.valueOf(Math.abs(installOrder.getReceivedPenatlyAmount().doubleValue())));
            recorded.setTxnTransactionDescription(InstallmentConstant.ADVANCE_SETTLEMENT_PENALTY_REDUCTION);
        }
        recorded.setTxnMerchantCategoryCode(installOrder.getMcc());
        recorded.setTxnReferenceNumber(installOrder.getAcquireReferenceNo());
        recorded.setTxnInstallmentTerm(String.valueOf(installOrder.getPostedTerm() + 1));
        recorded.setTxnInstallmentOrderId(installPlanDtos.get(0).getOrderId());
        recorded.setTxnBillingCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnSettlementCurrency(installOrder.getInstallmentCcy());
        recorded.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        recorded.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        recorded.setTxnReverseFeeIndicator(InstallmentConstant.REVERSE_FEE_INDICATOR_NO);
        recorded.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        recorded.setTxnAuthorizationMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        recorded.setTxnReleaseAuthorizationAmount(ReleaseAuthAmountEnum.NOT_RECOVER.getCode());
        recorded.setTxnVisaChargeFlag("");
        recorded.setTxnReimbursementAttribute("");
        recorded.setTxnIfiIndicator("");
        recorded.setTxnPsvIndicator("");
        recorded.setTxnDccIndicator("");
        recorded.setTxnForcePostIndicator("");
        recorded.setTxnFallBackIndicator("");
        if (adjustMethod.equals(InstallmentConstant.INSTALL_PRINCIPAL_ADJUST)){
            recorded.setTxnInstallmentIndicator("2");
        }else {
            recorded.setTxnInstallmentIndicator("");
        }
        recorded.setTxnStateCode("");
        recorded.setTxnPosEntryMode("");
        recorded.setTxnCountryCode("");
        recorded.setTxnAuthorizationCode("");
        recorded.setTxnLimitNodeId("");
        recorded.setTxnInterestTableId("");
        recorded.setTxnFeeTableId("");
        recorded.setTxnMerchantId("");
        recorded.setTxnAccountManageId(installOrder.getAccountManagementId());
        recorded.setTxnParentTransactionAccountId("");
        recorded.setTxnOriginalTransactionBalanceId("");
        recorded.setTxnGlobalFlowNumber("");
        recorded.setTxnOriginalGlobalFlowNumber("");
        recorded.setTxnZipCode("");
        recorded.setTxnMerchantName("");
        recorded.setTxnOriginalTransactionDate(null);
        // 入账日期 and 交易日期由入账逻辑赋值
        recorded.setTxnOutstandingAmount(BigDecimal.ZERO);
        recorded.setTxnExchangeRate(BigDecimal.ZERO);
        recorded.setTxnSecondMerchantId("");
        recorded.setTxnOpponentAccountName("");
        recorded.setTxnOpponentAccountNumber("");
        recorded.setTxnCityCode("");
        recorded.setTxnOpponentBankNumber("");
        logger.info("Calling txnRecordedService.txnRecorded: cardNumber={}, transactionCode={}", recorded.getTxnCardNumber(), recorded.getTxnTransactionCode());
        txnRecordedService.txnRecorded(recorded);
        logger.info("txnRecordedService.txnRecorded completed");
    }

    /**
     * 单笔可分期交易查询
     *
     * @param singleInstallSearchKeyDTO {@link SingleInstallSearchKeyDTO}
     * @return InstallAutoSignDTO
     */
    @Override
    public List<SingleInstallDTO> singleInstallTranQuery(SingleInstallSearchKeyDTO singleInstallSearchKeyDTO) {
        //参数提取与校验
        if(StringUtils.isBlank(singleInstallSearchKeyDTO.getOrganizationNumber())){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ORG_NUM_NULL_FAULT);
        }


        //1. 获取单笔可分期的单子
        InstallRecord installRecord = new InstallRecord();
        installRecord.setOrganizationNumber(singleInstallSearchKeyDTO.getOrganizationNumber());
        if (StringUtils.isNotBlank(singleInstallSearchKeyDTO.getCardNumber())){
            installRecord.setCardNumberList(Collections.singletonList(singleInstallSearchKeyDTO.getCardNumber()));
        }else if (StringUtils.isNotBlank(singleInstallSearchKeyDTO.getAccountManagementId())){
            installRecord.setAccountManagementId(singleInstallSearchKeyDTO.getAccountManagementId());
            logger.info("Calling installManager.getCardNumberList: accountManagementId={}", installRecord.getAccountManagementId());
            installRecord.setCardNumberList(installManager.getCardNumberList(installRecord.getAccountManagementId()));
            logger.info("installManager.getCardNumberList completed: cardNumberListSize={}", installRecord.getCardNumberList() != null ? installRecord.getCardNumberList().size() : 0);
        }
        List<SingleInstall> singleInstalls = new ArrayList<>(8);

        singleInstalls = singleInstallMapper.selectSingleInstallNewlist(installRecord);
       /* installRecord.getCardNumberList().forEach(cardNumber -> {
            installRecord.setCardNumber(cardNumber);
            singleInstalls.addAll(singleInstallMapper.selectSingleInstallList(installRecord));
        });*/
        if (CollectionUtils.isEmpty(singleInstalls)){
            return  BeanMapping.copyList(singleInstalls, SingleInstallDTO.class);
        }
        List<String> postTransactionIds =null ;
        postTransactionIds = singleInstalls.stream().map(SingleInstall::getOriginTransactionId).collect(Collectors.toList());
        List<PostedTranAccountRelationInfo> tranRelationInfos = postedTranAccountRelationInfoSelfMapper.
                selectTransIdAndAmountByPostIds(postTransactionIds, singleInstallSearchKeyDTO.getOrganizationNumber());
        Map<String, List<PostedTranAccountRelationInfo>> transactionInfos = tranRelationInfos.stream().collect(Collectors.groupingBy(PostedTranAccountRelationInfo::getPostedTransactionId));
        //2. 计算每个可分期单子的可用余额
        for (SingleInstall singleInstall : singleInstalls) {
            List<PostedTranAccountRelationInfo> tranAccountRelationInfos = transactionInfos.get(singleInstall.getOriginTransactionId());
            if (CollectionUtils.isEmpty(tranAccountRelationInfos)){
                singleInstall.setInstallAmount(BigDecimal.ZERO);
                continue;
            }
            List<String> transactionBalanceId = tranAccountRelationInfos.stream().map(PostedTranAccountRelationInfo::getTransactionBalanceId).collect(Collectors.toList());
            List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectBalancesByTransIds(singleInstall.getOrganizationNumber(), transactionBalanceId);
            BigDecimal totalBalance = accountBalanceInfos
                    .stream()
                    .filter(t -> {
                        ParmTransactionType parmTransactionType = parmTransactionTypeSelfMapper.selectByTypeCode(
                        t.getTransactionTypeCode(), t.getOrganizationNumber());
                        return Objects.equals(DebitCreditIndEnum.DEBIT_INDICATOR.getCode(), parmTransactionType.getBalanceLoanDirection());})
                    .map(AccountBalanceInfo::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal restBalance = singleInstall.getInstallAmount()
                    .subtract(Optional.ofNullable(singleInstall.getAlreadyInstAmount()).orElse(BigDecimal.ZERO));

            singleInstall.setInstallAmount(restBalance.compareTo(totalBalance) > 0 ? totalBalance : restBalance);
        }
        return BeanMapping.copyList(singleInstalls, SingleInstallDTO.class);
    }



    @Override
    public List<SingleInstallDTO> resetInstallTranQuery(String retrievalReferenceNumber,String cardNumber, String accountManagementId) {
        List<SingleInstall> singleInstalls = singleInstallMapper.selectResetInstallList(retrievalReferenceNumber, cardNumber, accountManagementId);
        return BeanMapping.copyList(singleInstalls, SingleInstallDTO.class);
    }

    /**
     * 必输项校验
     * @param installEntryDTO {@link InstallEntryDTO}
     */
    private void checkInstallParm(InstallEntryDTO installEntryDTO){
        //必输项校验
        String organizationNumber = installEntryDTO.getOrganizationNumber();
        String cardNumber = installEntryDTO.getCardNumber();
        LocalDate transactionDate = installEntryDTO.getTransactionDate();
        String orderId = installEntryDTO.getOrderId();
        String transactionFlag = installEntryDTO.getTransactionFlag();
        if (StringUtils.isEmpty(organizationNumber)) {
            logger.error("Organization number cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.OR_E);
        }
        if (StringUtils.isEmpty(cardNumber)) {
            logger.error("Card number cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.CA_E);
        }
        if (transactionDate == null) {
            logger.error("Transaction date cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_D_E);
        }
        if (orderId == null) {
            logger.error("Installment order ID cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_OR_E);
        }
        if (StringUtils.isEmpty(transactionFlag)) {
            logger.error("Transaction flag cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_ID_E);
        }
    }


}
