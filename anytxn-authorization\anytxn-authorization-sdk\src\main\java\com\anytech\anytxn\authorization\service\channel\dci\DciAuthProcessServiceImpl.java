package com.anytech.anytxn.authorization.service.channel.dci;

import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.domain.dto.*;
import com.anytech.anytxn.authorization.base.enums.DciMTIEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.dci.IDciAuthProcessService;
import com.anytech.anytxn.authorization.base.service.dci.IDciHandlerAuthService;
import com.anytech.anytxn.authorization.service.manager.AuthDataUpdateManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.base.utils.DciFieldCheckUtil;
import com.anytech.anytxn.authorization.base.utils.DinersFieldVerifyUtil;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.jpos.iso.ISOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 授权处理入口 AuthProcessServiceImpl
 *
 *          * 1.放请求报文到ThreadLocal
 *          * 2.根据请求报文类型对报文域校验
 *          * 3.请求报文转换(RequestDTO转ISO8583DTO)
 *          * 4.调用JPOS封装8583报文，记录授权请求原始报文  ---暂时不要
 *          * 5.授权处理业务
 *          *      5.1 交易识别，组装AuthRecordedDTO
 *          *      5.2 根据报文MTI(和Processing Code)映射TXN交易种类
 *          *      5.3 路由交易(根据TXN交易种类)
 *          *          5.3.1 普通交易
 *          *              数据准备，授权检查，授权数据更新
 *          *          5.3.2 撤销/冲正交易
 *          *          5.3.3 撤销冲正交易
 *          *          5.3.4 退货交易
 *          *          5.3.5 确认交易
 *          *          5.3.6 余额查询
 *          *          5.3.7 管理类
 *          *      5.4 AuthRecordedDTO构建ISO8583DTO
 *          * 6.应答报文转换(ISO8583DTO转ResponseDTO)，并返回
 *          * 7.finally移除ThreadLocal中的报文
 *
 * <AUTHOR>
 * @date 2021/9/2718:18
 */
@Service
public class DciAuthProcessServiceImpl implements IDciAuthProcessService {

    private static final Logger logger = LoggerFactory.getLogger(DciAuthProcessServiceImpl.class);

    @Resource
    private AuthDataUpdateManager authDataUpdateManager;

    @Autowired
    private IDciHandlerAuthService dciHandlerAuthService;

    /**
     * DCI pos 授权业务处理
     * @param iso8583Req {@link ISO8583ReqDTO}
     * @return
     * @throws IOException
     * @throws ISOException
     */
    @Override
    public DCIPos8583RspDTO dealDciPosIso8583Analog(DCIPos8583ReqDTO iso8583Req) {
        try {
            // 1.放请求报文到ThreadLocal
            AuthThreadLocalManager.buildReqData(iso8583Req);
            // 2.根据请求报文类型对报文域校验
            checkDataFieldOfDciPos(iso8583Req);
            // 3.请求报文转换(RequestDTO转ISO8583DTO)
            ISO8583DTO iso8583BO = iso8583DtoSetFromDciPosReq(iso8583Req);
            // 4.授权业务处理
            logger.info("Calling dciHandlerAuthService.processAuth for DCI POS authorization");
            ISO8583DTO result = dciHandlerAuthService.processAuth(iso8583BO);
            logger.info("dciHandlerAuthService.processAuth completed for DCI POS authorization");
            // 5.ISO8583DTO封装ResponseDTO
            return iso8583ResDtoSetOfDciPos(result);
        } catch (Exception e) {
            logger.error("DCI POS authorization business processing exception: {}", e.getMessage(), e);
            return authCheckOfDciPosException(e, iso8583Req);
        } finally {
            AuthThreadLocalManager.remove();
        }
    }

    /**
     * DCI DCS 授权业务处理
     * @param iso8583Req {@link ISO8583ReqDTO}
     * @return
     * @throws IOException
     * @throws ISOException
     */
    @Override
    public DCIDcs8583RspDTO dealDciDcsIso8583Analog(DCIDcs8583ReqDTO iso8583Req) throws IOException, ISOException {
        try {
            // 1.放请求报文到ThreadLocal
            AuthThreadLocalManager.buildReqData(iso8583Req);
            // 2.根据请求报文类型对报文域校验
            checkDataFieldOfDciDcs(iso8583Req);
            // 3.请求报文转换(RequestDTO转ISO8583DTO)
            ISO8583DTO iso8583BO = iso8583DtoSetFromDciDcsReq(iso8583Req);
            // 4.授权业务处理
            logger.info("Calling dciHandlerAuthService.processAuth for DCI DCS authorization");
            ISO8583DTO result = dciHandlerAuthService.processAuth(iso8583BO);
            logger.info("dciHandlerAuthService.processAuth completed for DCI DCS authorization");
            // 5.ISO8583DTO封装ResponseDTO
            return iso8583ResDtoSetOfDciDcs(result);
        } catch (Exception e) {
            logger.error("DCI DCS authorization business processing exception: {}", e.getMessage(), e);
            return authCheckOfDciDcsException(e, iso8583Req);
        } finally {
            AuthThreadLocalManager.remove();
        }
    }


    /**
     * 根据请求报文类型对报文域校验  DCI pos
     * @param iso8583Req
     * @return
     */
    private void checkDataFieldOfDciPos(DCIPos8583ReqDTO iso8583Req) {
        String mti = iso8583Req.getMTI();
        if(!DinersFieldVerifyUtil.nonDigit(mti) || mti.length() != 4){
            logger.error("mti is not valid");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_MTI_CHECK_FAIL);
        }
        switch (mti){
            //Authorization Request
            case "0100":
                //DciFieldCheckUtil.checkPosField2(iso8583Req);
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField11(iso8583Req);
                DciFieldCheckUtil.checkPosField22(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                break;
            //Finacial Request
            case "0200":
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField4(iso8583Req);
                DciFieldCheckUtil.checkPosField11(iso8583Req);
                DciFieldCheckUtil.checkPosField22(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                break;
            //Finacial Notice
            case "0220":
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField4(iso8583Req);
                DciFieldCheckUtil.checkPosField11(iso8583Req);
                DciFieldCheckUtil.checkPosField12(iso8583Req);
                DciFieldCheckUtil.checkPosField13(iso8583Req);
                DciFieldCheckUtil.checkPosField22(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField38(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                DciFieldCheckUtil.checkPosField62(iso8583Req);
                break;
            //Reversal Trans
            case "0400":
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField4(iso8583Req);
                DciFieldCheckUtil.checkPosField11(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField25(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                DciFieldCheckUtil.checkPosField62(iso8583Req);
                break;
            //Logon/Key Exchange, Echo Test(管理类)
            case "0800":
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                break;
            //Key Exchange Acknowledgement（管理类）
            case "0820":
                DciFieldCheckUtil.checkPosField3(iso8583Req);
                DciFieldCheckUtil.checkPosField24(iso8583Req);
                DciFieldCheckUtil.checkPosField39(iso8583Req);
                DciFieldCheckUtil.checkPosField41(iso8583Req);
                DciFieldCheckUtil.checkPosField42(iso8583Req);
                break;
            default:
                break;
        }
    }

    /**
     * 请求报文转换(RequestDTO转ISO8583DTO) DCI pos请求对象转换
     * @param iso8583Req
     * @return
     */
    private ISO8583DTO iso8583DtoSetFromDciPosReq(DCIPos8583ReqDTO iso8583Req) {
        if (StringUtils.isBlank(iso8583Req.getSourceCode())) {
            logger.error("sourceCode is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL,AuthRepDetailEnum.SCE, iso8583Req.getSourceCode());
        }
        Map<Integer, String> fieldMap = new HashMap<>(32);
        //请求处理报文
        ISO8583DTO iso8583BO = new ISO8583DTO();
        iso8583BO.setMTI(iso8583Req.getMTI());
        iso8583BO.setHeaderHex(iso8583Req.getHeaderHex());
        iso8583BO.setSourceCode(iso8583Req.getSourceCode());
        if (!StringUtils.isEmpty(iso8583Req.getF002())) {
            fieldMap.put(2, iso8583Req.getF002());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF003())) {
            fieldMap.put(3, iso8583Req.getF003());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF004())) {
            fieldMap.put(4, iso8583Req.getF004());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF011())) {
            fieldMap.put(11, iso8583Req.getF011());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF012())) {
            fieldMap.put(12, iso8583Req.getF012());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF013())) {
            fieldMap.put(13, iso8583Req.getF013());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF014())) {
            fieldMap.put(14, iso8583Req.getF014());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF022())) {
            fieldMap.put(22, iso8583Req.getF022());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF023())) {
            fieldMap.put(23, iso8583Req.getF023());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF024())) {
            fieldMap.put(24, iso8583Req.getF024());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF025())) {
            fieldMap.put(25, iso8583Req.getF025());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF035())) {
            fieldMap.put(35, iso8583Req.getF035());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF037())) {
            fieldMap.put(37, iso8583Req.getF037());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF038())) {
            fieldMap.put(38, iso8583Req.getF038());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF039())) {
            fieldMap.put(39, iso8583Req.getF039());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF041())) {
            fieldMap.put(41, iso8583Req.getF041());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF042())) {
            fieldMap.put(42, iso8583Req.getF042());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF045())) {
            fieldMap.put(45, iso8583Req.getF045());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF048())) {
            fieldMap.put(48, iso8583Req.getF048());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF049())) {
            fieldMap.put(49, iso8583Req.getF049());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF052())) {
            fieldMap.put(52, iso8583Req.getF052());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF054())) {
            fieldMap.put(54, iso8583Req.getF054());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF055())){
            fieldMap.put(55, iso8583Req.getF055());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056())){
            fieldMap.put(56, iso8583Req.getF056());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF059())){
            fieldMap.put(59, iso8583Req.getF059());
        }
        // 60号域
        if (!StringUtils.isEmpty(iso8583Req.getF060())) {
            fieldMap.put(Constants8583Field.FIELD60, iso8583Req.getF060());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_1())) {
            fieldMap.put(Constants8583Field.FIELD60_1, iso8583Req.getF060_1());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_2())) {
            fieldMap.put(Constants8583Field.FIELD60_2, iso8583Req.getF060_2());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_3())) {
            fieldMap.put(Constants8583Field.FIELD60_3, iso8583Req.getF060_3());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_4())) {
            fieldMap.put(Constants8583Field.FIELD60_4, iso8583Req.getF060_4());
        }

        if (!StringUtils.isEmpty(iso8583Req.getF062())) {
            fieldMap.put(62, iso8583Req.getF062());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF063())) {
            fieldMap.put(63, iso8583Req.getF063());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF061())) {
            fieldMap.put(61, iso8583Req.getF061());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF064())) {
            fieldMap.put(64, iso8583Req.getF064());
        }
        iso8583BO.setFieldMap(fieldMap);
        return iso8583BO;
    }

    /**
     * 封装返回结果  DCI pos
     * @param result
     * @return
     */
    private DCIPos8583RspDTO iso8583ResDtoSetOfDciPos(ISO8583DTO result) {
        if (result == null) {
            logger.error("result is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.EE_8583);
        }
        DCIPos8583RspDTO iso8583Res = new DCIPos8583RspDTO();
        if (!result.getMTI().isEmpty()) {
            iso8583Res.setMTI(result.getMTI());
        }
        if (null != result.getHeaderHex() && !result.getHeaderHex().isEmpty()) {
            iso8583Res.setHeaderHex(result.getHeaderHex());
        }
        Map<Integer, String> map = result.getFieldMap();
        iso8583Res.setF002(map.get(2));
        iso8583Res.setF003(map.get(3));
        iso8583Res.setF011(map.get(11));
        iso8583Res.setF012(map.get(12));
        iso8583Res.setF013(map.get(13));
        iso8583Res.setF014(map.get(14));
        iso8583Res.setF022(map.get(22));
        iso8583Res.setF023(map.get(23));
        iso8583Res.setF024(map.get(24));
        iso8583Res.setF025(map.get(25));
        iso8583Res.setF035(map.get(35));
        iso8583Res.setF037(map.get(37));
        if (map.containsKey(38)) {
            iso8583Res.setF038(StringUtils.leftPad(map.get(38), 6, "0"));
        }
        iso8583Res.setF039(map.get(39));
        iso8583Res.setF041(map.get(41));
        iso8583Res.setF042(map.get(42));
        iso8583Res.setF045(map.get(45));
        iso8583Res.setF048(map.get(48));
        iso8583Res.setF049(map.get(49));
        iso8583Res.setF052(map.get(52));
        iso8583Res.setF054(map.get(54));
        iso8583Res.setF055(map.get(55));
        iso8583Res.setF056(map.get(56));
        iso8583Res.setF059(map.get(59));
        if(map.containsKey(Constants8583Field.FIELD60)){
            iso8583Res.setF060(map.get(Constants8583Field.FIELD60));
            iso8583Res.setF060_1(map.get(Constants8583Field.FIELD60_1));
            iso8583Res.setF060_2(map.get(Constants8583Field.FIELD60_2));
            iso8583Res.setF060_3(map.get(Constants8583Field.FIELD60_3));
            iso8583Res.setF060_4(map.get(Constants8583Field.FIELD60_4));
        }
        iso8583Res.setF062(map.get(62));
        iso8583Res.setF063(map.get(63));
        iso8583Res.setF061(map.get(61));
        iso8583Res.setF064(map.get(64));
        logger.warn("DCI POS return message object field: {}", DesensitizedUtils.getJson(iso8583Res));
        return iso8583Res;
    }

    /**
     * 授权业务处理异常返回
     * @param e
     * @param iso8583Req
     * @return
     */
    private DCIPos8583RspDTO authCheckOfDciPosException(Exception e, DCIPos8583ReqDTO iso8583Req) {
        logger.error("Card number {} authorization exception", DesensitizedUtils.bankCard(iso8583Req.getF002()),e);

        DCIPos8583RspDTO iso8583ResDTO = BeanMapping.copy(iso8583Req, DCIPos8583RspDTO.class);
        //null 00或空字符串，将39域设为96
        if(iso8583Req.getF039() == null ||"00".equals(iso8583Req.getF039()) || iso8583Req.getF039().isEmpty()){
            iso8583ResDTO.setF039(AuthResponseCodeEnum.EXCEPTION.getCode());
        }
        //记录异常授权流水
        authDataUpdateManager.buildAuthorizationLog(iso8583Req.getF002(),
                iso8583Req.getSourceCode(), e, iso8583ResDTO);
        return iso8583ResDTO;
    }



    /**
     * 根据请求报文类型对报文域校验  DCI Dcs
     * @param iso8583Req
     */
    private void checkDataFieldOfDciDcs(DCIDcs8583ReqDTO iso8583Req) {
        String mti = iso8583Req.getMTI();
        if(!DinersFieldVerifyUtil.nonDigit(mti) || mti.length() != 4){
            logger.error("mti is not valid");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_MTI_CHECK_FAIL);
        }
        switch (mti){
            //Authorization Request
            case "1100":
               // DciFieldCheckUtil.checkDCIField2(iso8583Req);
                DciFieldCheckUtil.checkDCIField3(iso8583Req);
                DciFieldCheckUtil.checkDCIField4(iso8583Req);
                //DciFieldCheckUtil.checkDCIField6(iso8583Req);
                DciFieldCheckUtil.checkDCIField7(iso8583Req);
                DciFieldCheckUtil.checkDCIField11(iso8583Req);
                DciFieldCheckUtil.checkDCIField12(iso8583Req);
                DciFieldCheckUtil.checkDCIField22(iso8583Req);
                DciFieldCheckUtil.checkDCIField24(iso8583Req);
                DciFieldCheckUtil.checkDCIField26(iso8583Req);
                DciFieldCheckUtil.checkDCIField32(iso8583Req);
                DciFieldCheckUtil.checkDCIField33(iso8583Req);
                DciFieldCheckUtil.checkDCIField42(iso8583Req);
//                DciFieldCheckUtil.checkDCIField48(iso8583Req);
                DciFieldCheckUtil.checkDCIField49(iso8583Req);
                //DciFieldCheckUtil.checkDCIField51(iso8583Req);
                DciFieldCheckUtil.checkDCIField55(iso8583Req);
                DciFieldCheckUtil.checkDCIField100(iso8583Req);
                DciFieldCheckUtil.checkDCIField123(iso8583Req);
                break;
            //Authorization advice
            case "1120":
                //DciFieldCheckUtil.checkDCIField2(iso8583Req);
                //应对1120+18类型的交易所做的修改
                if(StringUtils.isNotBlank(iso8583Req.getF003()) && !"180000".equals(iso8583Req.getF003())){
                    DciFieldCheckUtil.checkDCIField3(iso8583Req);
                    DciFieldCheckUtil.checkDCIField4(iso8583Req);
                    //DciFieldCheckUtil.checkDCIField6(iso8583Req);
                    DciFieldCheckUtil.checkDCIField7(iso8583Req);
                    DciFieldCheckUtil.checkDCIField11(iso8583Req);
                    DciFieldCheckUtil.checkDCIField12(iso8583Req);
                    DciFieldCheckUtil.checkDCIField22(iso8583Req);
                    DciFieldCheckUtil.checkDCIField24(iso8583Req);
                    DciFieldCheckUtil.checkDCIField26(iso8583Req);
                    DciFieldCheckUtil.checkDCIField32(iso8583Req);
                    DciFieldCheckUtil.checkDCIField33(iso8583Req);
//                DciFieldCheckUtil.checkDCIField39(iso8583Req);
                    DciFieldCheckUtil.checkDCIField42(iso8583Req);
                    DciFieldCheckUtil.checkDCIField43(iso8583Req);
                    DciFieldCheckUtil.checkDCIField49(iso8583Req);
                    //DciFieldCheckUtil.checkDCIField51(iso8583Req);
                    DciFieldCheckUtil.checkDCIField55(iso8583Req);
                    DciFieldCheckUtil.checkDCIField92(iso8583Req);
                    DciFieldCheckUtil.checkDCIField100(iso8583Req);
                    DciFieldCheckUtil.checkDCIField123(iso8583Req);
                }
                break;
            //Authorization Request and Response Messages for Issuers
            case "1200":
                //DciFieldCheckUtil.checkDCIField2(iso8583Req);
                DciFieldCheckUtil.checkDCIField3(iso8583Req);
                DciFieldCheckUtil.checkDCIField4(iso8583Req);
                //DciFieldCheckUtil.checkDCIField6(iso8583Req);
                DciFieldCheckUtil.checkDCIField7(iso8583Req);
                DciFieldCheckUtil.checkDCIField11(iso8583Req);
                DciFieldCheckUtil.checkDCIField12(iso8583Req);
                DciFieldCheckUtil.checkDCIField13(iso8583Req);
                DciFieldCheckUtil.checkDCIField14(iso8583Req);
                DciFieldCheckUtil.checkDCIField22(iso8583Req);
                DciFieldCheckUtil.checkDCIField24(iso8583Req);
                DciFieldCheckUtil.checkDCIField26(iso8583Req);
                DciFieldCheckUtil.checkDCIField32(iso8583Req);
                DciFieldCheckUtil.checkDCIField33(iso8583Req);
                DciFieldCheckUtil.checkDCIField42(iso8583Req);
                DciFieldCheckUtil.checkDCIField43(iso8583Req);
                DciFieldCheckUtil.checkDCIField49(iso8583Req);
                DciFieldCheckUtil.checkDCIField56(iso8583Req);
                DciFieldCheckUtil.checkDCIField92(iso8583Req);
                DciFieldCheckUtil.checkDCIField104(iso8583Req);
                break;
            //Authorization Reversal Request
            case "1420":
                //DciFieldCheckUtil.checkDCIField2(iso8583Req);
                DciFieldCheckUtil.checkDCIField3(iso8583Req);
                DciFieldCheckUtil.checkDCIField4(iso8583Req);
                //DciFieldCheckUtil.checkDCIField6(iso8583Req);
                DciFieldCheckUtil.checkDCIField7(iso8583Req);
                DciFieldCheckUtil.checkDCIField11(iso8583Req);
                DciFieldCheckUtil.checkDCIField12(iso8583Req);
                DciFieldCheckUtil.checkDCIField24(iso8583Req);
                DciFieldCheckUtil.checkDCIField26(iso8583Req);
                DciFieldCheckUtil.checkDCIField32(iso8583Req);
                DciFieldCheckUtil.checkDCIField33(iso8583Req);
                DciFieldCheckUtil.checkDCIField49(iso8583Req);
                //DciFieldCheckUtil.checkDCIField51(iso8583Req);
                DciFieldCheckUtil.checkDCIField56(iso8583Req);
                break;
            //Network Management Request
            case "1804":
                DciFieldCheckUtil.checkDCIField7(iso8583Req);
                DciFieldCheckUtil.checkDCIField11(iso8583Req);
                DciFieldCheckUtil.checkDCIField12(iso8583Req);
                DciFieldCheckUtil.checkDCIField24(iso8583Req);
                DciFieldCheckUtil.checkDCIField93(iso8583Req);
                DciFieldCheckUtil.checkDCIField94(iso8583Req);
            default:
                break;
        }
    }

    /**
     * 请求报文转换(RequestDTO转ISO8583DTO) DCI dcs请求对象转换
     * @param iso8583Req
     * @return
     */
    private ISO8583DTO iso8583DtoSetFromDciDcsReq(DCIDcs8583ReqDTO iso8583Req) {
        if (StringUtils.isBlank(iso8583Req.getSourceCode())) {
            logger.error("sourceCode is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL,AuthRepDetailEnum.SCE, iso8583Req.getSourceCode());
        }
        Map<Integer, String> fieldMap = new HashMap<>(64);
        //请求处理报文
        ISO8583DTO iso8583BO = new ISO8583DTO();
        iso8583BO.setMTI(iso8583Req.getMTI());
        iso8583BO.setHeaderHex(iso8583Req.getHeaderHex());
        iso8583BO.setSourceCode(iso8583Req.getSourceCode());
        if (!StringUtils.isEmpty(iso8583Req.getF002())) {
            fieldMap.put(2, iso8583Req.getF002());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF003())) {
            fieldMap.put(3, iso8583Req.getF003());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF004())) {
            fieldMap.put(4, iso8583Req.getF004());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF006())) {
            fieldMap.put(6, iso8583Req.getF006());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF007())) {
            fieldMap.put(7, iso8583Req.getF007());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF011())) {
            fieldMap.put(11, iso8583Req.getF011());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF012())) {
            fieldMap.put(12, iso8583Req.getF012());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF013())) {
            fieldMap.put(13, iso8583Req.getF013());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF014())) {
            fieldMap.put(14, iso8583Req.getF014());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF015())) {
            fieldMap.put(15, iso8583Req.getF015());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF022())) {
            fieldMap.put(22, iso8583Req.getF022());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF023())) {
            fieldMap.put(23, iso8583Req.getF023());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF024())) {
            fieldMap.put(24, iso8583Req.getF024());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF025())) {
            fieldMap.put(25, iso8583Req.getF025());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF026())) {
            fieldMap.put(26, iso8583Req.getF026());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF030())) {
            fieldMap.put(30, iso8583Req.getF030());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF032())) {
            fieldMap.put(32, iso8583Req.getF032());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF033())) {
            fieldMap.put(33, iso8583Req.getF033());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF035())) {
            fieldMap.put(35, iso8583Req.getF035());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF037())) {
            fieldMap.put(37, iso8583Req.getF037());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF038())) {
            fieldMap.put(38, iso8583Req.getF038());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF039())) {
            fieldMap.put(39, iso8583Req.getF039());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF040())) {
            fieldMap.put(40, iso8583Req.getF040());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF041())) {
            fieldMap.put(41, iso8583Req.getF041());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF042())) {
            fieldMap.put(42, iso8583Req.getF042());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF043())) {
            fieldMap.put(43, iso8583Req.getF043());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF044())) {
            fieldMap.put(44, iso8583Req.getF044());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF045())) {
            fieldMap.put(45, iso8583Req.getF045());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF046())) {
            fieldMap.put(46, iso8583Req.getF046());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF048())) {
            fieldMap.put(48, iso8583Req.getF048());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF049())) {
            fieldMap.put(49, iso8583Req.getF049());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF051())) {
            fieldMap.put(51, iso8583Req.getF051());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF052())) {
            fieldMap.put(52, iso8583Req.getF052());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF054())) {
            fieldMap.put(54, iso8583Req.getF054());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF055())){
            fieldMap.put(55, iso8583Req.getF055());
        }
        // 56原交易信息
        if (StringUtils.isNotEmpty(iso8583Req.getF056())) {
            fieldMap.put(Constants8583Field.FIELD56, iso8583Req.getF056());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056_1())) {
            fieldMap.put(Constants8583Field.FIELD56_1, iso8583Req.getF056_1());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056_2())) {
            fieldMap.put(Constants8583Field.FIELD56_2, iso8583Req.getF056_2());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056_3())) {
            fieldMap.put(Constants8583Field.FIELD56_3, iso8583Req.getF056_3());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056_4())) {
            fieldMap.put(Constants8583Field.FIELD56_4, iso8583Req.getF056_4());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF058())) {
            fieldMap.put(58, iso8583Req.getF058());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF059())) {
            fieldMap.put(59, iso8583Req.getF059());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF060())) {
            fieldMap.put(60, iso8583Req.getF060());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF062())) {
            fieldMap.put(62, iso8583Req.getF062());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF063())) {
            fieldMap.put(63, iso8583Req.getF063());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF064())) {
            fieldMap.put(64, iso8583Req.getF064());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF072())) {
            fieldMap.put(72, iso8583Req.getF072());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF092())) {
            fieldMap.put(92, iso8583Req.getF092());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF093())) {
            fieldMap.put(93, iso8583Req.getF093());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF094())) {
            fieldMap.put(94, iso8583Req.getF094());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF096())) {
            fieldMap.put(96, iso8583Req.getF096());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF100())) {
            fieldMap.put(100, iso8583Req.getF100());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF101())) {
            fieldMap.put(101, iso8583Req.getF101());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF104())) {
            fieldMap.put(104, iso8583Req.getF104());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF106())) {
            fieldMap.put(106, iso8583Req.getF106());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF122())) {
            fieldMap.put(122, iso8583Req.getF122());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF123())) {
            fieldMap.put(123, iso8583Req.getF123());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF124())) {
            fieldMap.put(124, iso8583Req.getF124());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF125())) {
            fieldMap.put(125, iso8583Req.getF125());
        }
        iso8583BO.setFieldMap(fieldMap);
        return iso8583BO;
    }

    /**
     * 封装返回结果  DCI dcs
     * @param result
     * @return
     */
    private DCIDcs8583RspDTO iso8583ResDtoSetOfDciDcs(ISO8583DTO result) {
        if (result == null) {
            logger.error("result is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.EE_8583);
        }
        DCIDcs8583RspDTO iso8583Res = new DCIDcs8583RspDTO();
        if (!result.getMTI().isEmpty()) {
            iso8583Res.setMTI(result.getMTI());
        }
        if (result.getHeaderHex() != null) {
            iso8583Res.setHeaderHex(result.getHeaderHex());
        }
        Map<Integer, String> map = result.getFieldMap();
        iso8583Res.setF002(map.get(2));
        iso8583Res.setF003(map.get(3));
        if (map.containsKey(4)) {
            iso8583Res.setF004(StringUtils.leftPad(map.get(4), 12, "0"));
        }
        iso8583Res.setF006(map.get(6));
        iso8583Res.setF007(map.get(7));
        iso8583Res.setF011(map.get(11));
        iso8583Res.setF012(map.get(12));
        iso8583Res.setF013(map.get(13));
        iso8583Res.setF014(map.get(14));
        iso8583Res.setF015(map.get(15));
        iso8583Res.setF022(map.get(22));
        iso8583Res.setF023(map.get(23));
        iso8583Res.setF024(map.get(24));
        iso8583Res.setF025(map.get(25));
        iso8583Res.setF026(map.get(26));
        iso8583Res.setF030(map.get(30));
        iso8583Res.setF032(map.get(32));
        String f033 = map.get(33);
        if (StringUtils.isNotEmpty(map.get(100))) {
            iso8583Res.setF033(map.get(100));
        } else {
            iso8583Res.setF033("00000361588");
        }
        iso8583Res.setF035(map.get(35));
        iso8583Res.setF037(map.get(37));
        if (map.containsKey(38)) {
            iso8583Res.setF038(StringUtils.leftPad(map.get(38), 6, "0"));
        }
        iso8583Res.setF039(map.get(39));
        iso8583Res.setF040(map.get(40));
        iso8583Res.setF041(map.get(41));
        iso8583Res.setF042(map.get(42));
        iso8583Res.setF043(map.get(43));
        iso8583Res.setF044(map.get(44));
        iso8583Res.setF045(map.get(45));
        iso8583Res.setF046(map.get(46));
//        iso8583Res.setF048(map.get(48));
        iso8583Res.setF049(map.get(49));
        iso8583Res.setF051(map.get(51));
        iso8583Res.setF052(map.get(52));
        iso8583Res.setF054(map.get(54));
        iso8583Res.setF055(map.get(55));
        iso8583Res.setF056(map.get(56));
        iso8583Res.setF058(map.get(58));
        iso8583Res.setF059(map.get(59));
        iso8583Res.setF062(map.get(62));
        iso8583Res.setF064(map.get(64));
        iso8583Res.setF072(map.get(72));
        iso8583Res.setF092(map.get(92));
        iso8583Res.setF093(map.get(93));
        iso8583Res.setF094(map.get(94));
        iso8583Res.setF096(map.get(96));
        //iso8583Res.setF100(map.get(100));
        if (StringUtils.isNotEmpty(f033)) {
            iso8583Res.setF100(f033);
        } else {
            iso8583Res.setF100("00000361589");
        }
        iso8583Res.setF101(map.get(101));
        iso8583Res.setF104(map.get(104));
        iso8583Res.setF106(map.get(106));
        iso8583Res.setF122(map.get(122));
        iso8583Res.setF123(map.get(123));
        iso8583Res.setF124(map.get(124));
        iso8583Res.setF125(map.get(125));
        logger.warn("DCI DCS return message object field: {}", DesensitizedUtils.getJson(iso8583Res));
        return iso8583Res;
    }

    /**
     * 授权业务处理异常返回
     * @param e
     * @param iso8583Req
     * @return
     */
    private DCIDcs8583RspDTO authCheckOfDciDcsException(Exception e, DCIDcs8583ReqDTO iso8583Req) {
        DCIDcs8583RspDTO iso8583ResDTO = BeanMapping.copy(iso8583Req, DCIDcs8583RspDTO.class);
        ////null 00或空字符串，将39域设为909(系统异常)
        if(iso8583Req.getF039() == null ||iso8583Req.getF039().equals("00") || iso8583Req.getF039().isEmpty()){
            iso8583ResDTO.setF039("909");
        }
        //记录异常授权流水
        authDataUpdateManager.buildAuthorizationLog(iso8583Req.getF002(),
                iso8583Req.getSourceCode(), e, iso8583ResDTO);
        // bug316
        iso8583ResDTO.setMTI(DciMTIEnum.getResponseMti(iso8583Req.getMTI()));
        return iso8583ResDTO;
    }
}
