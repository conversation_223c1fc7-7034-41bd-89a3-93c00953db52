<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.anytech</groupId>
        <artifactId>anytxn-third-party-service</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-file-manager</artifactId>
    <description>文件处理服务</description>
    <packaging>pom</packaging>

    <modules>
        <module>anytxn-file-manager-sdk</module>
        <module>anytxn-file-manager-server</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
