package com.anytech.anytxn.file;

import com.anytech.anytxn.file.utils.NacosValueProvider;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * @description: 为了统一文件路径，
 * 给其他工程提供的统一的文件路径管理
 * @author: zhangnan
 * @create: 2021-04-01
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(NacosValueProvider.class)
public @interface EnableFilePathConfig {
}
