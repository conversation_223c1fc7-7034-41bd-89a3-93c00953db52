package com.anytech.anytxn.file.mapper;

import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface FileManagerScanParamMapper {
    /**
     * 根据主键删除
     * @param id Long
     * @return int
     */
    @Delete({
        "delete from CM_FILE_MANAGER_SCAN_PARAM",
        "where ID = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(String id);


    /**
     * 根据字段是否为空保存
     * @param record FileManagerScanParam
     * @return int
     */
    @InsertProvider(type=FileManagerScanParamSqlProvider.class, method="insertSelective")
    int insertSelective(FileManagerScanParam record);

    /**
     * 根据主键查询
     * @param id Long
     * @return FileManagerScanParam
     */
    @Select({
        "select",
        "*",
        "from CM_FILE_MANAGER_SCAN_PARAM",
        "where ID = #{id,jdbcType=VARCHAR}"
    })
    @Results(id = "FileManagerScanParamResult", value = {
        @Result(column="ID", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="FILE_TYPE", property="fileType", jdbcType=JdbcType.VARCHAR),
        @Result(column="SCAN_PATH", property="scanPath", jdbcType=JdbcType.VARCHAR),
        @Result(column="COPY_PATH", property="copyPath", jdbcType=JdbcType.VARCHAR),
        @Result(column="FILE_NAME_MATCH_RULE", property="fileNameMatchRule", jdbcType=JdbcType.INTEGER),
        @Result(column="FILE_NAME_MATCH_CONTENT", property="fileNameMatchContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="DESCRIPTION", property="description", jdbcType=JdbcType.VARCHAR),
        @Result(column="ENABLE_STATUS", property="enableStatus", jdbcType=JdbcType.TINYINT),
        @Result(column="SCHEDULE_TASK", property="scheduleTask", jdbcType=JdbcType.VARCHAR),
        @Result(column="MD5_CHECK_DAYS", property="md5CheckDays", jdbcType=JdbcType.INTEGER),
        @Result(column="SCAN_CRON", property="scanCron", jdbcType=JdbcType.VARCHAR),
        @Result(column="FILE_PROCESS_TYPE", property="fileProcessType", jdbcType=JdbcType.INTEGER),
        @Result(column="TRIGGER_TYPE", property="triggerType", jdbcType=JdbcType.INTEGER),
        @Result(column="ORGANIZATION_NUMBER", property="organizationNumber", jdbcType=JdbcType.CHAR),
        @Result(column="CREATE_TIME", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="UPDATE_TIME", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="UPDATE_BY", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="VERSION_NUMBER", property="versionNumber", jdbcType=JdbcType.INTEGER),
        @Result(column="LAST_SCAN_TIME", property="lastScanTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="LAST_SCAN_RESULT_DES", property="lastScanResultDes", jdbcType=JdbcType.VARCHAR),
        @Result(column="LAST_SCAN_PROCESS_ID", property="lastScanProcessId", jdbcType=JdbcType.VARCHAR)
    })
    FileManagerScanParam selectByPrimaryKey(String id);

    /**
     * 根据字段是否为空更新
     * @param record FileManagerScanParam
     * @return int
     */
    @UpdateProvider(type=FileManagerScanParamSqlProvider.class, method="updateByPrimaryKeySelective")
    int updateByPrimaryKeySelective(FileManagerScanParam record);


    /**
     * 页面分页查询
     * @param organizationNumber
     * @return
     */
    /*@Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PARAM ",
            "where 1 = 1 ",
            "<if test=\"organizationNumber != '0000'\"> ",
            " and ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "</if>",
            "</script>",
    })

    @ResultMap("FileManagerScanParamResult")
    List<FileManagerScanParam> selectAll(String organizationNumber);*/

    /**
     * 页面分页查询
     * @param organizationNumber
     * @return
     */
    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PARAM ",
            "where 1 = 1 ",
            "<if test=\"organizationNumber != '0000'\"> ",
            " and ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "</if>",
            "</script>",
    })
    @ResultMap("FileManagerScanParamResult")
    Page<FileManagerScanParam> selectAll(Page<FileManagerScanParam> page, String organizationNumber);

    /**
     * 扫描进程查询需要扫描的配置
     * @return
     */
    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PARAM ",
            "where ENABLE_STATUS = 1",
            "</script>",
    })

    @ResultMap("FileManagerScanParamResult")
    List<FileManagerScanParam> selectScanList();

    /**
     * update时查重用
     * @param fileType
     * @return
     */
    @Select({
            "<script>",
            "select id ",
            "from CM_FILE_MANAGER_SCAN_PARAM ",
            "where FILE_TYPE = #{fileType,jdbcType=VARCHAR} limit 1",
            "</script>",
    })
    String countByFileType(String fileType);

    /**
     * 文件扫描过程过程中，更新必要字段
     * @param param
     * @return
     */
    @Update({
            "<script>",
            "update CM_FILE_MANAGER_SCAN_PARAM",
            "set LAST_SCAN_TIME = #{lastScanTime,jdbcType=TIMESTAMP},",
            "LAST_SCAN_RESULT_DES = #{lastScanResultDes,jdbcType=VARCHAR},",
            "<if test='lastScanProcessId != null '>",
            "LAST_SCAN_PROCESS_ID = #{lastScanProcessId},",
            "</if>",
            "VERSION_NUMBER = VERSION_NUMBER +1 ",
            "where ID = #{id,jdbcType=VARCHAR}",
            "</script>",
    })
    int updateScanStatus(FileManagerScanParam param);

}
