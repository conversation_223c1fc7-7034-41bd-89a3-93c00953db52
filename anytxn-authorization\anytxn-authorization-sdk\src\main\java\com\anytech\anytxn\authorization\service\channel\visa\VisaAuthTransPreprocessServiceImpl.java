package com.anytech.anytxn.authorization.service.channel.visa;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.domain.dto.VISAReqDTO;
import com.anytech.anytxn.authorization.base.enums.AuthProcessingCodeEnum;
import com.anytech.anytxn.authorization.base.enums.MTIEnum;
import com.anytech.anytxn.authorization.base.enums.VisaMTIEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.visa.IVisaAuthTransPreprocessService;
import com.anytech.anytxn.authorization.service.auth.checkfield.AuthCheckFieldHandler;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.base.service.rule.IRuleService;
import com.anytech.anytxn.authorization.utils.AuthAssert;
import com.anytech.anytxn.authorization.base.utils.VerifyDateUtil;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.utils.JsonUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @discription VISA授权交易识别接口
 * @date 2018--12--11--13:45
 **/
@Service
public class VisaAuthTransPreprocessServiceImpl implements IVisaAuthTransPreprocessService {
    private static final Logger logger = LoggerFactory.getLogger(VisaAuthTransPreprocessServiceImpl.class);
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    /**
     * 中国银联银行卡交换系统技术规范 第2部分 报文接口规范》域25服务点条件检查，定义范围
     */
    private static final List<String> SEVER_CODE_LIST = Arrays.asList("00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "17", "18", "28", "41", "42", "43", "44", "45", "60", "64", "65", "66", "67", "68", "69", "82", "83", "91");
    /**
     * 报文MTI取值前两位
     */
    private static final List<String> MTI_START_VALUE = Arrays.asList("01", "02", "03");

    /**
     * Visa Process   ---NEW
     *
     * @param visaReqDTO
     * @return
     */
    @Override
    public AuthRecordedDTO preProcessVisaAuthTrans(VISAReqDTO visaReqDTO) {
        specialTransactionHandle(visaReqDTO);
        // 字段校验
        checkAuthField(visaReqDTO);

        boolean bool = StringUtils.equalsAny(visaReqDTO.getMTI(), VisaMTIEnum.VISA_AUTH_REPEAT.getCode(), VisaMTIEnum.VISA_AUTH_REQUEST.getCode(), VisaMTIEnum.VISA_AUTH_ADVICES.getCode())
                && (!AuthConstans.MTI_FIRST_TWO_VALUE.equals(visaReqDTO.getF003_1()));
        // 交易识别
        Map<String, String> map = null;
        if (bool) {
            map = transIdentify(visaReqDTO, OrgNumberUtils.getOrg());
        }
        // 封装授权接口
        return buildAuthRecorded(visaReqDTO, map);
    }

    private void specialTransactionHandle(VISAReqDTO visaReqDTO) {
        if (Objects.equals("260000", visaReqDTO.getF003()) || StringUtils.equalsAny(visaReqDTO.getF003_1(), "11", "21", "34","40")
                || (Objects.equals("10", visaReqDTO.getF003_1()) && StringUtils.equalsAny(visaReqDTO.getF104_57_01(), "AA", "PP", "BI", "WT", "FT"))) {
            logger.error("specialTransactionHandle: visaReqDTO={}", visaReqDTO.getMTI());
            throw new AnyTxnAuthException(AuthResponseCodeEnum.TRANSACTION_NOT_ALLOWED);
        }
        // 针对ARQC校验：他们这套key和咱们UAT环境key不一致，导致只能使用某一套，等问问科友的人能不能把VISA给的key倒到UAT环境加密机。暂时关闭该处的校验。
//        AuthAssert.isTrue("1".equals(visaReqDTO.getF044_8()) , () -> log.error("visa group  The Authorization Request Cryptogram (ARQC) was checked but failed verification., field44_8:{}",  visaReqDTO.getF044_8()), new AnyTxnAuthException(AuthResponseCodeEnum.INVALID_TRANSACTION));
    }

    /**
     * Visa域值检查
     *
     * @param visaReqDTO
     */
    private void checkAuthField(VISAReqDTO visaReqDTO) {
        logger.info("checkAuthField|VISA fields check-start");
        String mti = visaReqDTO.getMTI();
        AuthCheckFieldHandler.builder().addCheck(() -> {
            //F003  check
            // Positions 1-2 must be 50, when U.S. acquirers wish to identify Bill Payment transactions. Bill Payment messages require B in field 62.4
            String f003_1 = visaReqDTO.getF003_1();
            String f062_4 = visaReqDTO.getF062_4();
            AuthAssert.isTrue("50".equals(f003_1) && !"B".equals(f062_4), () -> logger.error("checkField3|check exception Positions 1-2 must be 50, when U.S. acquirers wish to identify Bill Payment transactions. Bill Payment messages require B in field 62.4 , field3:{},field62_4:{}", f003_1, f062_4), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE3_FORMAT_ERROR));
        }).addCheck(() -> {
            //F004  check
            String field4 = visaReqDTO.getF004();
            String f003_1 = visaReqDTO.getF003_1();
            String f025 = visaReqDTO.getF025();
            //Field 25 = 51 (zero-amount account verification).
            //Field 3, positions 1-2, is 39 (eligibility message), 70 (PIN change/unblock), or 72 (PIN unblock
            //or activation) 30(Balance/Available Funds Inquiry).
            AuthAssert.isTrue((StringUtils.isNotEmpty(field4) && new BigDecimal(field4).compareTo(BigDecimal.ZERO) <= AuthConstans.ZERO) && (!("51".equals(f025) || Lists.newArrayList("39", "70", "72", "30").contains(f003_1))), () -> logger.error("checkField4|check exception only inquire transaction, account verification, eligibility message, PIN change/unblock, PIN unblock field4 is 0, field4:{}, field3SF1:{},", field4, f003_1), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE4_FORMAT_ERROR));
        }).addCheck(() -> {
            //F006 check
            //Partial Authorization and the issuer supports multicurrency ,If field 6 is missing or contains zeros
            BigDecimal f006 = StringUtils.isEmpty(visaReqDTO.getF006()) ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF006());
            AuthAssert.isTrue(("10".equals(visaReqDTO.getF039()) && (!visaReqDTO.getF049().equals(visaReqDTO.getF051())) && f006.compareTo(BigDecimal.ZERO) <= AuthConstans.ZERO), () -> logger.error("checkField4|check exception Partial Authorization and the issuer supports multicurrency ,If field 6 is missing or contains zeros , field006:{}, field39:{},field049:{},field051:{},", visaReqDTO.getF006(), visaReqDTO.getF039(), visaReqDTO.getF049(), visaReqDTO.getF051()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE6_FORMAT_ERROR));
        }).addCheck(() -> {
            //F018  check
            String f003_1 = visaReqDTO.getF003_1();
            String f018 = visaReqDTO.getF018();
            //For ATM cash withdrawal (MCC 6011) and manual cash disbursement (MCC 6010) the value in
            //postions 1-2 must be 01
            AuthAssert.isTrue(StringUtils.equalsAny(f018,"6011", "6010") && !StringUtils.equalsAny(f003_1, AuthConstans.ZERO_ONE, "30"), () -> logger.error("checkField4|check exception For ATM cash withdrawal (MCC 6011) and manual cash disbursement (MCC 6010) the value in Field3 1-2 must be 01 , field3:{}, field18:{}", f003_1, f018), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE3_FORMAT_ERROR));
            //For account verification requests field 18 cannot be 6011 (ATM).
            AuthAssert.isTrue("51".equals(visaReqDTO.getF025()) && "6011".equals(f018), () -> logger.error("checkField4|check exception For account verification requests field 18 cannot be 6011 (ATM) , field25:{}, field18:{}", visaReqDTO.getF025(), f018), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE3_FORMAT_ERROR));
        }).addCheck(() -> {
            //F022  check
            String f022_1 = visaReqDTO.getF022_1();
            String f003_1 = visaReqDTO.getF003_1();
            String f035 = visaReqDTO.getF035();
            String f045 = visaReqDTO.getF045();
            //For POS-acquired transactions, if positions 1-2 = 02, field 35 or field 45 must be present.
            AuthAssert.isTrue(AuthConstans.ZERO_TWO.equals(f022_1) && (StringUtils.isEmpty(f035) && StringUtils.isEmpty(f045)), () -> logger.error("checkField022|check exception POS-acquired transactions, if positions 1-2 = 02, field 35 or field 45 must be present ," + " f022_1:{}, f035:{}, f045:{}", f022_1, f035, f045), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE22_FORMAT_ERROR));
            //For Visa and PLUS ATM, if positions 1-2 = 02 field 35 must be present.
            AuthAssert.isTrue(AuthConstans.ZERO_TWO.equals(f022_1) && AuthConstans.ZERO_ONE.equals(f003_1) && StringUtils.isEmpty(f035), () -> logger.error("checkField022|check exception For Visa and PLUS ATM, if positions 1-2 = 02 field 35 must be present. ," + " f022_1:{}, f035:{}, f003_1:{}", f022_1, f035, f003_1), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE22_FORMAT_ERROR));
            //If positions 1-2 = 90 or 05, field 35 or field 45 must be present;
            AuthAssert.isTrue(Objects.equals(mti, "0100") && StringUtils.equalsAny(f022_1, "05", "90") && (StringUtils.isEmpty(f035) && StringUtils.isEmpty(f045)), () -> logger.error("checkField022|check exception If positions 1-2 = 90 or 05, field 35 or field 45 must be present ," + " f022_1:{}, f035:{}, f045:{}", f022_1, f035, f045), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE22_FORMAT_ERROR));
            // For ATM transactions if positions 1-2 = 05, 90 or 95 field 35 must be present
            AuthAssert.isTrue(Objects.equals(mti, "0100") && Lists.newArrayList("05", "90", "95").contains(f022_1) && AuthConstans.ZERO_ONE.equals(f003_1) && StringUtils.isEmpty(f035), () -> logger.error("checkField022|check exception For ATM transactions if positions 1-2 = 05, 90 or 95 field 35 must be present. ," + " f022_1:{}, f035:{}, f003_1:{}", f022_1, f035, f003_1), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE22_FORMAT_ERROR));
            //If position 3 = 2 (terminal cannot accept PINs), but Field 52-PIN Data is present
            AuthAssert.isTrue(AuthConstans.II.equals(visaReqDTO.getF022_2()) && StringUtils.isNotEmpty(visaReqDTO.getF052()), () -> logger.error("checkField022|check exception If position 3 = 2 (terminal cannot accept PINs), but Field 52-PIN Data is present ," + " f022_3:{}, f052:{}", visaReqDTO.getF022_2(), visaReqDTO.getF052()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE22_FORMAT_ERROR));
        }).addCheck(() -> {
            //Field 60 – Additional POS Information
            //ATM: If 60.1 = 2, field 52 must be present except in the case of reversals.
            AuthAssert.isTrue(Lists.newArrayList(AuthConstans.MTI_0100).contains(visaReqDTO.getMTI()) && "2".equals(visaReqDTO.getF060_1()) && StringUtils.isEmpty(visaReqDTO.getF052()), () -> logger.error("checkField060|check exception ATM: If 60.1 = 2, field 52 must be present except in the case of reversals ," + " f003_1:{}, f060_1:{}, f052:{},MTI:{}", visaReqDTO.getF003_1(), visaReqDTO.getF060_1(), visaReqDTO.getF052(), visaReqDTO.getMTI()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE60_SF1_FORMAT_ERROR));

            //E-Commerce: If field 25 contains 59 and subfield 60.8 is missing or invalid in an 01xx or 04xx
            //request, the message is rejected with reject code 0360 or 0185 respectively. The value in subfield
            //60.8 must be 05, 06, 07, or 08 for e-commerce authorization transactions
            AuthAssert.isTrue("59".equals(visaReqDTO.getF025()) && Lists.newArrayList(AuthConstans.MTI_0100, AuthConstans.MTI_0120, AuthConstans.MTI_0400, AuthConstans.MTI_0420).contains(visaReqDTO.getMTI()) && (StringUtils.isEmpty(visaReqDTO.getF060_8()) || !Lists.newArrayList("05", "06", "07", "08").contains(visaReqDTO.getF060_8())), () -> logger.error("checkField060|check exception E-Commerce: If field 25 contains 59 and subfield 60.8 is missing or invalid in an 01xx or 04xx ,60.8 must be 05, 06, 07, or 08 for e-commerce authorization transactions ," + " f025:{}, f060_8:{}, MTI:{}", visaReqDTO.getF025(), visaReqDTO.getF060_8(), visaReqDTO.getMTI()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE60_SF1_FORMAT_ERROR));

            //Bill Payment Transactions (U.S. Only): Authorization request messages submitted with a field 3
            //processing code of 50 and subfield 60.8 values other than 01, 02, 03, 05, 06, 07, or 08 is rejected
            AuthAssert.isTrue("50".equals(visaReqDTO.getF003_1()) && (StringUtils.isEmpty(visaReqDTO.getF060_8()) || !Lists.newArrayList("01", "02", "03", "05", "06", "07", "08").contains(visaReqDTO.getF060_8())), () -> logger.error("checkField060|check exception Bill Payment Transactions (U.S. Only): Authorization request messages submitted with a field 3 processing code of 50 and subfield 60.8 values other than 01, 02, 03, 05, 06, 07, or 08 is rejected," + " f003_1:{}, f060_8:{}", visaReqDTO.getF003_1(), visaReqDTO.getF060_8()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE60_SF1_FORMAT_ERROR));


            // Bill payment transactions can be conducted by mail, online, or in person. Positions 9-10 (field 60.8) However, ECI indicators 05 through 08 require the transaction to be electronic commerce. If the transaction is electronic commerce, field 25 must be 59
//            AuthAssert.isTrue(Lists.newArrayList("05", "08").contains(visaReqDTO.getF060_8()) && !"59".equals(visaReqDTO.getF025()), () -> logger.error("checkField060|check exception Positions 9-10 (field 60.8) However, ECI indicators 05 through 08 require the transaction to be electronic commerce. If the transaction is electronic commerce, field 25 must be 59," + " f060_8:{}, f025:{}", visaReqDTO.getF060_8(), visaReqDTO.getF025()), new AnyTxnAuthException(AuthResponseCodeEnum.FIELD_DE60_SF1_FORMAT_ERROR));

            //If Partial Authorization this field contains the purchase amount and field 60.10, position 12, contains a 0 or field 60.10 is not provided, the issuer may decline the message request with response code 51 (insufficient funds).
            AuthAssert.isTrue((StringUtils.isEmpty(visaReqDTO.getF060_10()) || "0".equals(visaReqDTO.getF060_10())) && "10".equals(visaReqDTO.getF039()), () -> logger.error("checkField060|check exception If Partial Authorization this field contains the purchase amount and field 60.10, position 12, contains a 0 or field 60.10 is not provided, the issuer may decline the message request with response code 51 (insufficient funds). ," + " f039:{}, f060_10:{}", visaReqDTO.getF039(), visaReqDTO.getF060_10()), new AnyTxnAuthException(AuthResponseCodeEnum.AWAILABLE_CREDIT));

        }).execute();

        logger.info("checkAuthField|VISA fields check-end");
    }

    /**
     * 识别规则因子
     * (1)MTI：Message Type Identifier                      --authMessageTypeId
     * (2)F3：Processing Code                               --authProcessingCode
     * (3)F3_01 transaction Type
     * (3)F18：Merchant Type                                --authMerchantType
     * (4)F22：Point-of-Service Entry Mode Code             --authServicePointPinCode
     * (5)F25：Point-of-Service Condition Code              --authServicePointConditionCode
     * (6)F41：Card Acceptor Terminal Identification        --acceptorTerminalCode
     * (10)F60_1   Terminal Type
     * (11)F60_2   Terminal Entry Capability
     * (12)F60_3   Chip Condition Code
     * (13)F60_6   Chip Transaction Indicator
     * (14)F60_8   Mail/Phone/Electronic Commerce and Payment Indicator
     * (15)F60_10  Additional Authorization Indicators
     * 交易识别
     */
    public Map<String, String> transIdentify(VISAReqDTO visaReqDTO, String organizationNumber) {
        String mti = visaReqDTO.getMTI();
        Map<String, Object> ruleParam = new HashMap<>(12);
        ruleParam.put("sourceCode", visaReqDTO.getSourceCode());
        ruleParam.put(AuthConstans.AUTH_MSG_TYPE_ID, mti);
        ruleParam.put(AuthConstans.AUTH_PROCESS_CODE, visaReqDTO.getF003());
        //交易类型 3号域的前二位
        if (StringUtils.isNotBlank(visaReqDTO.getF003())) {
            ruleParam.put(AuthConstans.AUTH_TRANS_TYPE_CODE, visaReqDTO.getF003().substring(AuthConstans.ZERO, AuthConstans.TWO));
        }

        ruleParam.put(AuthConstans.AUTH_MERCHANT_TYPE, visaReqDTO.getF018());
        ruleParam.put("authServicePointPinCode", visaReqDTO.getF022().substring(0, 2));
        ruleParam.put(AuthConstans.AUTH_SERVICE_POINT_CONDITION_CODE, visaReqDTO.getF025());
        ruleParam.put("terminalType", visaReqDTO.getF060_1());
        ruleParam.put("terminalEntryCapability", visaReqDTO.getF060_2());

        ruleParam.put("chipTransactionIndicator", visaReqDTO.getF060_6());
        ruleParam.put("electronicCommerceIndicator", visaReqDTO.getF060_8());
        ruleParam.put("additionalIndicators", visaReqDTO.getF060_10());
        if (null != visaReqDTO.getF104_1() && !visaReqDTO.getF104_1().isEmpty()) {
            Map<String, String> dataset57 = visaReqDTO.getF104_1().get("57");
            if (null != dataset57 && !dataset57.isEmpty()) {
                ruleParam.put("businessApplicationIdentifier", dataset57.get("01"));
            }
        }
        DataInputDTO dataInputDTO = new DataInputDTO();
        dataInputDTO.setRuleType(AuthConstans.TRANS_VISA_IDENTIFY_RULE);
        dataInputDTO.setInput(ruleParam);
        dataInputDTO.setOrganizationNumber(organizationNumber);
        logger.info("transaction identification rule params:{}", JsonUtils.mapToJson(ruleParam));
        logger.info("Calling ruleService.executeRule: ruleType={}", dataInputDTO.getRuleType());
        Map<String, String> result = ruleService.executeRule(dataInputDTO);
        logger.info("Called ruleService.executeRule: success");
        return result;
    }

    /**
     * build AuthRecordedDTO
     *
     * @param visaReqDTO
     * @param stringStringMap
     * @return
     */
    public AuthRecordedDTO buildAuthRecorded(VISAReqDTO visaReqDTO, Map<String, String> stringStringMap) {
        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        if (stringStringMap != null) {
            String authTransactionTypeTopCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE);
            authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionTypeTopCode);
            String authTransactionTypeDetailCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE);
            authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetailCode);
            String postingTransactionCode = stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE);
            authRecordedDTO.setPostingTransactionCode(postingTransactionCode);
            String postingTransactionCodeDev = stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV);
            authRecordedDTO.setPostingTransactionCodeRev(postingTransactionCodeDev);
        }
        authRecordedDTO.setSourceCode(AuthTransactionSourceCodeEnum.VISA.getCode());

        authRecordedDTO.setAuthGlobalFlowNumber(AuthThreadLocalManager.getTraceId());
        authRecordedDTO.setFirstGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());
        String mti = visaReqDTO.getMTI();
        authRecordedDTO.setAuthOriginalGlobalFlowNumber(null);
        authRecordedDTO.setCurrentAuthLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        authRecordedDTO.setAuthMessageTypeId(mti);
        authRecordedDTO.setAuthCardNumber(visaReqDTO.getF002());
        authRecordedDTO.setAuthProcessingCode(visaReqDTO.getF003());
        // 计算4号域中的交易金额
        boolean boolField = Objects.equals("000000000000", visaReqDTO.getF004()) || StringUtils.isBlank(visaReqDTO.getF004()) || !StringUtils.isNumeric(visaReqDTO.getF004());
        String currencyStr = StringUtils.isNotBlank(visaReqDTO.getF049()) ? visaReqDTO.getF049() : "702";
        authRecordedDTO.setAuthTransactionAmount(boolField ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF004()).divide(getDecimalPlaceByCurrencyCode(currencyStr)));
        // 原交易金额
        authRecordedDTO.setAuthAmountsOfOrigina(visaReqDTO.getF004());
        // 6域
        boolField = StringUtils.isBlank(visaReqDTO.getF006()) || !StringUtils.isNumeric(visaReqDTO.getF006());
        authRecordedDTO.setAuthCardholderBillingAmount(boolField ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF006()).divide(new BigDecimal(AuthConstans.DIVIDE_100)));
        authRecordedDTO.setAuthCardholderOriginalBillingAmount(boolField ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF006()).divide(new BigDecimal(AuthConstans.DIVIDE_100)));
        authRecordedDTO.setAuthTransmissionTime(visaReqDTO.getF007());
        boolField = StringUtils.isBlank(visaReqDTO.getF010()) || !StringUtils.isNumeric(visaReqDTO.getF010());
        authRecordedDTO.setAuthCardholderBillingRate(boolField ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF010()));
        authRecordedDTO.setAuthSystemTraceAuditNumber(visaReqDTO.getF011());
        authRecordedDTO.setAuthLocalTransactionTime(StringUtils.isNotBlank(visaReqDTO.getF012())? visaReqDTO.getF012() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
        authRecordedDTO.setAuthLocalTransactionDate(StringUtils.isNotBlank(visaReqDTO.getF013())? visaReqDTO.getF013() : LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMdd")));
        authRecordedDTO.setAuthCardExpirationDate(visaReqDTO.getF014());
        authRecordedDTO.setAuthSettlementDate(visaReqDTO.getF015());
        authRecordedDTO.setAuthMerchantType(visaReqDTO.getF018());
        authRecordedDTO.setAuthCardAcceptorBusinessCode(visaReqDTO.getF018());
        authRecordedDTO.setAuthMerchantCountryCode(visaReqDTO.getF019());
        authRecordedDTO.setAuthServicePointEntryModeCode(StringUtils.isBlank(visaReqDTO.getF022()) ? null : visaReqDTO.getF022());
        authRecordedDTO.setAuthServicePointCardCode(StringUtils.isBlank(visaReqDTO.getF022()) ? null : visaReqDTO.getF022().substring(0, 2));
        authRecordedDTO.setAuthCardSequenceNumber(visaReqDTO.getF023());
        authRecordedDTO.setAuthServicePointConditionCode(visaReqDTO.getF025());
        authRecordedDTO.setAuthServicePointPinCaptureCode(visaReqDTO.getF026());
        authRecordedDTO.setAuthTransactionFee(StringUtils.isBlank(visaReqDTO.getF028()) ? null : visaReqDTO.getF028());
        authRecordedDTO.setAuthAcquiringIdentificationCode(visaReqDTO.getF032());
        authRecordedDTO.setAuthForwardingIdentificationCode(visaReqDTO.getF033());
        authRecordedDTO.setAuthTrack2Data(visaReqDTO.getF035());
        authRecordedDTO.setAuthRetrievalReferenceNumber(visaReqDTO.getF037());
        authRecordedDTO.setAuthAuthIdentificationResponse(visaReqDTO.getF038());
        if (StringUtils.isBlank(visaReqDTO.getF039())) {
            authRecordedDTO.setAuthResponseCode(AuthConstans.AUTH_CHECK_RESPONSE_CODE);
        } else {
            authRecordedDTO.setAuthResponseCode(visaReqDTO.getF039());
        }
        authRecordedDTO.setAuthCardAcceptorTerminalCode(visaReqDTO.getF041().trim());
        authRecordedDTO.setAuthCardAcceptorIdentification(visaReqDTO.getF042().trim());
        authRecordedDTO.setAuthCardAcceptorNameLocation(visaReqDTO.getF043());
        authRecordedDTO.setAuthAdditionalResponseData(visaReqDTO.getF044());
        authRecordedDTO.setAuthCvvResultCode(visaReqDTO.getF044_5());
        authRecordedDTO.setArqcCheckResultCode(visaReqDTO.getF044_8());
        authRecordedDTO.setAuthCvv2ResultCode(visaReqDTO.getF044_10());
        authRecordedDTO.setCavvResultCode(visaReqDTO.getF044_13());
        authRecordedDTO.setAuthTrack1Data(visaReqDTO.getF045());
        authRecordedDTO.setAuthAdditionalDataPrivate(visaReqDTO.getF048());
        authRecordedDTO.setAuthTransactionCurrencyCode(visaReqDTO.getF049());
        authRecordedDTO.setAuthBillingCurrencyCode(visaReqDTO.getF051());
        authRecordedDTO.setAuthPinData(visaReqDTO.getF052());
        authRecordedDTO.setAuthSecurityRelatedControlInformation(visaReqDTO.getF053());
        authRecordedDTO.setAuthAdditionalAmounts(visaReqDTO.getF054());
        authRecordedDTO.setAmountType(visaReqDTO.getF054_2());
        authRecordedDTO.setCurrencyCode(visaReqDTO.getF054_3());
        authRecordedDTO.setAuthIccSystemRelatedData(visaReqDTO.getF055());
        authRecordedDTO.setAuthSystemRelatedData(visaReqDTO.getF055());
        authRecordedDTO.setAuthNationalPointOfServiceGeographicData1(visaReqDTO.getF059());
        authRecordedDTO.setAuthAdditionalPosInformation(StringUtils.isBlank(visaReqDTO.getF060()) ? null : visaReqDTO.getF060());
        authRecordedDTO.setAuthAdditionalPosInformationTerminalType(visaReqDTO.getF060_1());
        authRecordedDTO.setAuthTerminalEntryCap(visaReqDTO.getF060_2());
        authRecordedDTO.setElectronicCommerceIndicators(visaReqDTO.getF060_8());
        authRecordedDTO.setAuthAdditionalAuthorizationData(visaReqDTO.getF060_10());
        authRecordedDTO.setAuthOtherAmount(StringUtils.isBlank(visaReqDTO.getF061()) ? null : visaReqDTO.getF061());
        authRecordedDTO.setAuthReplacementBillingCurrency(visaReqDTO.getF061_3());
        authRecordedDTO.setAuthCustomerPaymentServiceFields(visaReqDTO.getF062());
        authRecordedDTO.setAuthorizationCharacteristicsIndicator(visaReqDTO.getF062_1());
        authRecordedDTO.setTransactionIdentifier(visaReqDTO.getF062_2());
        authRecordedDTO.setMultiClearSeqNbr(StringUtils.isBlank(visaReqDTO.getF062_11()) || !StringUtils.isNumeric(visaReqDTO.getF062_11()) ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF062_11()));
        authRecordedDTO.setMultiClearTotCnt(StringUtils.isBlank(visaReqDTO.getF062_12()) || !StringUtils.isNumeric(visaReqDTO.getF062_12()) ? BigDecimal.ZERO : new BigDecimal(visaReqDTO.getF062_12()));
        authRecordedDTO.setAuthVipPrivateUseField(visaReqDTO.getF063());
        authRecordedDTO.setAuthVipMessageReasonCode(visaReqDTO.getF063_3());
        authRecordedDTO.setAdviceReasonData(visaReqDTO.getF063_4());
        authRecordedDTO.setAuthNetworkIdentificationCode(visaReqDTO.getF063_1());
        authRecordedDTO.setAuthReceivingInstitutionCountryCode(visaReqDTO.getF068());
        authRecordedDTO.setAuthNetworkManagementInformationCode(visaReqDTO.getF070());
        authRecordedDTO.setAuthActionData(visaReqDTO.getF073());
        authRecordedDTO.setAuthOriginalMessageTypeId(visaReqDTO.getF090_1());
        authRecordedDTO.setAuthOriginalSystemTraceAuditNumber(visaReqDTO.getF090_2());
        authRecordedDTO.setAuthOriginalTransmissionTime(visaReqDTO.getF090_3());
        authRecordedDTO.setAuthOriginalAcquiringIdentificationCode(visaReqDTO.getF090_4());
        authRecordedDTO.setAuthOriginalForwardingIdentificationCode(visaReqDTO.getF090_5());
        authRecordedDTO.setAuthFileUpdateCode(visaReqDTO.getF091());
        authRecordedDTO.setAuthFileUpdateCode(visaReqDTO.getF092());
        // 95号域不为空
        // 判断95号域
        boolean field95Flag = StringUtils.isNotBlank(visaReqDTO.getF095_1()) && StringUtils.isNumeric(visaReqDTO.getF095_1());
        if (field95Flag) {
            authRecordedDTO.setAuthReplaceAmountActualVisa(new BigDecimal(visaReqDTO.getF095_1()).divide(getDecimalPlaceByCurrencyCode(currencyStr)));
        }
        authRecordedDTO.setAuthReceivingInstitutionIdentificationCode(visaReqDTO.getF100());
        authRecordedDTO.setAuthFileName(visaReqDTO.getF101());
        authRecordedDTO.setAuthAccountIdentification1(visaReqDTO.getF102());
        authRecordedDTO.setAuthAccountIdentification2(visaReqDTO.getF103());
        authRecordedDTO.setAuthTransactionDescription5(visaReqDTO.getF104());
        authRecordedDTO.setAuthAdditionalTraceData(visaReqDTO.getF115());
        authRecordedDTO.setAuthCardIssuerReferenceData(visaReqDTO.getF116());
        authRecordedDTO.setAuthNationalUse(visaReqDTO.getF117());
        authRecordedDTO.setAuthIntraCountryData(visaReqDTO.getF118());
        authRecordedDTO.setAuthIssuingInstitutionIdentificationCode(visaReqDTO.getF121());
        authRecordedDTO.setAuthVerificationData(visaReqDTO.getF123());
        authRecordedDTO.setAuthSupportingInformation(visaReqDTO.getF125());
        authRecordedDTO.setAuthVipPrivateUseField(visaReqDTO.getF126());
        authRecordedDTO.setDirectoryServerTransactionId(visaReqDTO.getF126_8());
        authRecordedDTO.setUniversalCardholderAuthenticationField(visaReqDTO.getF126_9());
        authRecordedDTO.setCardCVC2Value(visaReqDTO.getF126_10());
        authRecordedDTO.setAuthTerminalCapabilityProfile(visaReqDTO.getF130());
        authRecordedDTO.setAuthTerminalVerificationResults(visaReqDTO.getF131());
        authRecordedDTO.setAuthUnpredictableNumber(visaReqDTO.getF132());
        authRecordedDTO.setAuthTerminalSerialNumber(visaReqDTO.getF133());
        authRecordedDTO.setAuthVisaDiscretionaryData(visaReqDTO.getF134());
        authRecordedDTO.setAuthIssuerDiscretionaryData(visaReqDTO.getF135());
        authRecordedDTO.setAuthCryptogram(visaReqDTO.getF136());
        authRecordedDTO.setAuthApplicationTransactionCounter(visaReqDTO.getF137());
        authRecordedDTO.setAuthApplicationInterchangeProfile(visaReqDTO.getF138());
        authRecordedDTO.setAuthArpcResponseCryptogramAndCode(visaReqDTO.getF139());
        authRecordedDTO.setAuthIssuerAuthenticationData(visaReqDTO.getF140());
        authRecordedDTO.setAuthIssuerScript(visaReqDTO.getF142());
        authRecordedDTO.setAuthIssuerScriptResults(visaReqDTO.getF143());
        authRecordedDTO.setAuthCryptogramTransactionType(visaReqDTO.getF144());
        authRecordedDTO.setAuthTerminalCountryCode(visaReqDTO.getF145());
        authRecordedDTO.setAuthTerminalTransactionDate(visaReqDTO.getF146());
        authRecordedDTO.setAuthCryptogramAmount(visaReqDTO.getF147());
        authRecordedDTO.setAuthCryptogramCurrencyCode(visaReqDTO.getF148());
        authRecordedDTO.setAuthCryptogramCashbackAmount(visaReqDTO.getF149());
        authRecordedDTO.setAuthSecondaryPinBlock(visaReqDTO.getF152());
        //初始化金额0
        authRecordedDTO.setTransactionFee(String.valueOf(BigDecimal.ZERO));
        authRecordedDTO.setQueryTransFee(BigDecimal.ZERO);
        authRecordedDTO.setAuthMarkupFee(BigDecimal.ZERO);
        authRecordedDTO.setAuthDccFee(BigDecimal.ZERO);
        authRecordedDTO.setConsumeTranFee(BigDecimal.ZERO);

        // 授权类型代码
        if (VisaMTIEnum.VISA_AUTH_ADVICES.getCode().equals(mti)) {
            authRecordedDTO.setAuthAuthTypeCode(AuthTypeCodeEnum.GENERATION_AUTH.getCode());
        } else {
            authRecordedDTO.setAuthAuthTypeCode(AuthTypeCodeEnum.REGULAR_AUTH.getCode());
        }
        // 3号域前2位
        String firstTwoCode = visaReqDTO.getF003_1();
        // visa 消费
        if (VisaMTIEnum.VISA_AUTH_REQUEST.getCode().equals(mti) || VisaMTIEnum.VISA_AUTH_REPEAT.getCode().equals(mti) || VisaMTIEnum.VISA_AUTH_ADVICES.getCode().equals(mti)) {
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_THREE_ONE.getCode().equals(firstTwoCode) || "21,40,41".contains(firstTwoCode)) {
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            }
        }
        // visa 冲正
        if (VisaMTIEnum.VISA_REVERSAL_TRANS.getCode().equals(mti)) {
            if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode)) {
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());
            }
        }
        // visa 冲正通知
        if (VisaMTIEnum.VISA_REVERSAL_ADVICES_TRANS.getCode().equals(mti)) {
            if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode)) {
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());
                // 撤销冲正
            } else {
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
            }
        }
        // 退货
        if (Objects.equals(firstTwoCode, AuthProcessingCodeEnum.FIRST_TWO_TWO.getCode()) && Objects.equals(mti, "0100")) {
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REFUNDS_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REFUNDS_TRANS.getCode());
        }
        authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.VISA.getCode());
        return authRecordedDTO;
    }

    /**
     * 获取某个币种的小数位
     * @param currencyStr
     * @return
     */
    private BigDecimal getDecimalPlaceByCurrencyCode(String currencyStr) {
        ParmCurrencyCode currencyCode = parmCurrencyCodeSelfMapper.selectByCurrencyCode(currencyStr);
        Integer decimalPlace = currencyCode != null ? currencyCode.getDecimalPlace() : 2;
        return new BigDecimal(10).pow(decimalPlace);
    }


    // --------------------------old start---------------------
    @Override
    public AuthRecordedDTO preProcessVisaAuthTrans(ISO8583DTO iso8583Bo) {
        boolean bool = checkAuth(iso8583Bo.getFieldMap(), iso8583Bo.getMTI());
        if (!bool) {
            logger.error("Authorization processing field validation failed, field 39 assigned value 30");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL);
        }
        Map<String, String> map = transIdentify(iso8583Bo, OrgNumberUtils.getOrg());
        String authTransactionTypeTopCode = map == null ? null : map.get(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE);
        String authTransactionTypeDetailCode = map == null ? null : map.get(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE);
        return buildAuthRecorded(iso8583Bo, authTransactionTypeTopCode, authTransactionTypeDetailCode);
    }

    /**
     * 字段校验
     */
    private boolean checkAuth(Map<Integer, String> map, String mti) {
        /* 1、域2主账号检查不能为空，且为数字型，长度必须为16位或19位 ，否则域39应答码赋值“30”*/
        String field2 = map.get(Constants8583Field.FIELD2);
        boolean bool = StringUtils.isBlank(field2) || !StringUtils.isNumeric(field2) || (field2.length() != 15 && field2.length() != 16 && field2.length() != 19);
        if (bool) {
            logger.error("Authorization processing - field validation failed, field2:{}", field2);
            //throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D2, field2);
        }
        /*
         * 2、域3交易处理码检查不能为空,且为数字型,前2位须在规范范围内
         * 交易处理码前2位定义可参考《中国银联银行卡交换系统技术规范第2部分报文接口规范》6.4小节
         */
        String field3 = map.get(Constants8583Field.FIELD3);
        bool = StringUtils.isBlank(field3) || !StringUtils.isNumeric(field3) || !StringUtils.isNumeric(field3.substring(0, 2));
        if (bool) {
            logger.error("Authorization processing - field validation failed, field3:{}", field3);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D3, field3);
        }
        /*  3、域4交易金额检查不能为空,且为数字型,必须大于等于0,否则域39应答码赋值“30” */
        // 4号域可以不填
        String field4 = map.get(Constants8583Field.FIELD4);
        boolean field4Flag = StringUtils.isNotBlank(field4) && (!StringUtils.isNumeric(field4) || new BigDecimal(field4).compareTo(BigDecimal.ZERO) < 0);
        if (field4Flag) {
            logger.error("Authorization processing - field validation failed, field4:{}", field4);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D4, field4);
        }
        /*4、域6持卡人扣账金额检查可以为空,如果不为空,必须为数字型且大于等于0,否则域39应答码赋值“30” */
        String field6 = map.get(Constants8583Field.FIELD6);
        boolean field6Flag = StringUtils.isNotBlank(field6) && (!StringUtils.isNumeric(field6) || new BigDecimal(field6).compareTo(BigDecimal.ZERO) < 0);
        if (field6Flag) {
            logger.error("Authorization processing - field validation failed, field6:{}", field6);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D6, field6);
        }
        /* 5、域7交易传输时间检查格式:MMDDhhmmss可以为空,如果不为空,必须为日期时间格式,否则域39应答码赋值“30”*/
        String field7 = map.get(Constants8583Field.FIELD7);
        if (StringUtils.isNotBlank(field7) && !VerifyDateUtil.isDateMmddhhmmss(field7)) {
            logger.error("Authorization processing - Field validation failed, field7: {}", field7);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D7, field7);
        }
        /* 6、域11系统跟踪号可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30”*/
        String field11 = map.get(Constants8583Field.FIELD11);
        if (StringUtils.isNotBlank(field11) && !StringUtils.isNumeric(field11)) {
            logger.error("Authorization processing - Field validation failed, field11: {}", field11);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D11, field11);
        }
        /* 7、域14卡片有效期格式:YYMM不能为空,且为数字型,必须为日期格式,否则域39应答码赋值“30”*/
        String field14 = map.get(Constants8583Field.FIELD14);
        if (StringUtils.isBlank(field14) || !VerifyDateUtil.isDateYymm(field14)) {
            logger.error("Authorization processing - Field validation failed, field14: {}", field14);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D14, field14);
        }

        /* 8、域18商户类型检查如果域1为01xx、02xx、04xx，此域不能为空，且为数字型，否则域39应答码赋值“30”*/
        String field18 = map.get(Constants8583Field.FIELD18);
        bool = (MTI_START_VALUE.contains(mti.substring(0, 2))) && (StringUtils.isBlank(field18) || !StringUtils.isNumeric(field18));
        if (bool) {
            logger.error("Authorization processing - field validation failed, field18:{}", field18);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D18, field18);
        }

        /*
         * 9、域22服务点输入方式码检查格式:2位PAN + 1位PIN  不能为空,且为数字型,前2位须在PAN定义中,否则域39应答码赋值“30”
         * PAN定义可参考《中国银联银行卡交换系统技术规范 第2部分 报文接口规范》6.19小节
         */
        String field22 = map.get(Constants8583Field.FIELD22);
        if (StringUtils.isBlank(field22) || !StringUtils.isNumeric(field22) || !VerifyDateUtil.isTwoNumber(field22.substring(0, 2))) {
            logger.info("Authorization processing - field validation failed, field22:{}", field22);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D22, field22);
        }
        /*
         * 10、域25服务点条件检查不能为空,且为数字型,须在服务点条件码定义中,否则域39应答码赋值“30”
         *   服务点条件码定义可参考《中国银联银行卡交换系统技术规范 第2部分 报文接口规范》6.21小节
         */
        String field25 = map.get(Constants8583Field.FIELD25);
        if (StringUtils.isBlank(field25) || !StringUtils.isNumeric(field25) || !SEVER_CODE_LIST.contains(field25)) {
            logger.info("Authorization processing - field validation failed, field25:{}", field25);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D25, field25);
        }
        /* 11、域37检索参考号检查可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30”*/
        String field37 = map.get(Constants8583Field.FIELD37);
        if (StringUtils.isNotBlank(field37) && !StringUtils.isNumeric(field37)) {
            logger.error("Authorization processing - field validation failed, field37:{}", field37);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D37, field37);
        }
        /* 12、域49交易货币代码检查不能为空,且为数字型,否则域39应答码赋值“30”*/
        String field49 = map.get(Constants8583Field.FIELD49);
        if (StringUtils.isBlank(field49) || !StringUtils.isNumeric(field49)) {
            logger.error("Authorization processing - field validation failed, field49:{}", field49);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D49, field49);
        }
        /* 13、域51持卡人账户货币代码可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30”*/
        String field51 = map.get(Constants8583Field.FIELD51);
        if (StringUtils.isNotBlank(field51) && !StringUtils.isNumeric(field51)) {
            logger.error("Authorization processing - field validation failed, field51:{}", field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D51, field51);
        }
        /**
         * 如果6号域即授权接口 authCardholderBillingAmount 不为空， 则51号域即授权接口中 authBillingCurrencyCode 不能为空，否则应答码赋值为96
         */
        if (StringUtils.isNotBlank(field6) && StringUtils.isBlank(field51)) {
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D6E_D51);
        }
        /**
         * 如果51号域即授权接口 authBillingCurrencyCode 不为空， 则6号域即授权接口中 authCardholderBillingAmount 不能为空，否则应答码赋值为96
         */
        if (StringUtils.isBlank(field6) && StringUtils.isNotBlank(field51)) {
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D51E_D6);
        }
        // 95号域 部分撤销金额
        String field95 = map.get(Constants8583Field.FIELD95);
        if (!StringUtils.isBlank(field95) && !StringUtils.isNumeric(field95) && StringUtils.isNotBlank(field4) && new BigDecimal(field4).compareTo(new BigDecimal(field95)) < 0) {
            logger.error("Partial cancellation amount - field validation failed");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D95, field95);
        }
        return true;
    }

    /**
     * 2.3 交易识别方式
     *
     * @param iso8583Bo iso8583Bo
     * @return Map
     */
    private Map<String, String> transIdentify(ISO8583DTO iso8583Bo, String organizationNumber) {
        /*
         * 1、满足以下条件时，执行《交易识别》处理。条件如下：
         * 1）报文类型标识符（MTI） = 0100 or 0120，并且，
         * 2）交易处理码（F3）的前2位 not = 20，并且，
         * 3）应答码（F39）= 00 or 空
         */
        String mti = iso8583Bo.getMTI();
        Map<Integer, String> isoMap = iso8583Bo.getFieldMap();

        boolean bool = (MTIEnum.AUTH_REQUEST.getCode().equals(mti) || MTIEnum.AUTH_NOTICE.getCode().equals(mti)) && (!AuthConstans.MTI_FIRST_TWO_VALUE.equals(isoMap.get(Constants8583Field.FIELD3).substring(0, 2))) && (StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD39)) || AuthResponseCodeEnum.CHECK_RESPONSE.getCode().equals(isoMap.get(Constants8583Field.FIELD39)));
        Map<String, String> resMap = null;

        if (bool) {
            Map<String, Object> ruleParam = new HashMap<>(12);
            ruleParam.put(AuthConstans.AUTH_SERVICE_POINT_CONDITION_CODE, isoMap.get(Constants8583Field.FIELD25));
            ruleParam.put(AuthConstans.AUTH_MSG_TYPE_ID, mti);
            ruleParam.put(AuthConstans.AUTH_PROCESS_CODE, isoMap.get(Constants8583Field.FIELD3));
            ruleParam.put("authMerchantType", isoMap.get(Constants8583Field.FIELD18));
            ruleParam.put("authServicePointPinCode", isoMap.get(Constants8583Field.FIELD22));
            if (!StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD48))) {
                ruleParam.put("privateDomainInfoOne", isoMap.get(Constants8583Field.FIELD48).substring(0, 3));
                ruleParam.put("privateDomainInfoTwo", isoMap.get(Constants8583Field.FIELD48).substring(3, 6));
                ruleParam.put("privateDomainInfoThree", isoMap.get(Constants8583Field.FIELD48).substring(6, 9));
            }

            ruleParam.put("sourceCode", iso8583Bo.getSourceCode());
            ruleParam.put("acceptorTerminalCode", isoMap.get(Constants8583Field.FIELD41));

            DataInputDTO dataInputDTO = new DataInputDTO();
            dataInputDTO.setRuleType(AuthConstans.TRANS_VISA_IDENTIFY_RULE);
            dataInputDTO.setInput(ruleParam);
            dataInputDTO.setOrganizationNumber(organizationNumber);
            logger.info("Calling ruleService.executeRule: ruleType={}", dataInputDTO.getRuleType());
            resMap = ruleService.executeRule(dataInputDTO);
            logger.info("Called ruleService.executeRule: success");
            if (resMap == null) {
                logger.error("Authorization rule matching failed!");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_AUTH_RULE_MATCH_FAILE);
            }
        }
        return resMap;
    }

    /**
     * 2.3 组建授权接口
     *
     * @return {@link AuthRecordedDTO}
     */
    public AuthRecordedDTO buildAuthRecorded(ISO8583DTO iso8583Bo, String authTransactionTypeTopCode, String authTransactionTypeDetailCode) {

        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        //authRecordedDTO.setAuthGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        //String mti = iso8583Bo.getMTI();
        //Map<Integer, String> map = iso8583Bo.getFieldMap();
        //authRecordedDTO.setAuthOriginalGlobalFlowNumber(null);
        //authRecordedDTO.setAuthMessageTypeId(mti);
        //authRecordedDTO.setAuthCardNumber(map.get(Constants8583Field.FIELD2));
        //authRecordedDTO.setAuthProcessingCode(map.get(Constants8583Field.FIELD3));
        //
        //// 计算4号域中的交易金额
        //boolean boolField = StringUtils.isNotBlank(map.get(Constants8583Field.FIELD4)) && StringUtils.isNumeric(map.get(Constants8583Field.FIELD4));
        //// 判断4号域
        //if (boolField) {
        //    BigDecimal authAmount = new BigDecimal(map.get(Constants8583Field.FIELD4)).divide(new BigDecimal(AuthConstans.DIVIDE_100));
        //    authRecordedDTO.setAuthTransactionAmount(authAmount);
        //} else {
        //    authRecordedDTO.setAuthTransactionAmount(BigDecimal.ZERO);
        //}
        //
        //boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD6)) || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD6));
        //authRecordedDTO.setAuthCardholderBillingAmount(boolField ? BigDecimal.ZERO : new BigDecimal(map.get(Constants8583Field.FIELD6)).divide(new BigDecimal(AuthConstans.DIVIDE_100)));
        //authRecordedDTO.setAuthTransmissionTime(map.get(Constants8583Field.FIELD7));
        //boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD10)) || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD10));
        //authRecordedDTO.setAuthCardholderBillingRate(boolField ? BigDecimal.ZERO : new BigDecimal(map.get(Constants8583Field.FIELD10)));
        //authRecordedDTO.setAuthSystemTraceAuditNumber(map.get(Constants8583Field.FIELD11));
        //authRecordedDTO.setAuthLocalTransactionTime(map.get(Constants8583Field.FIELD12));
        //authRecordedDTO.setAuthLocalTransactionDate(map.get(Constants8583Field.FIELD13));
        //authRecordedDTO.setAuthCardExpirationDate(map.get(Constants8583Field.FIELD14));
        //// 清算日期 visa 17号域 （原银联为15号域）
        //authRecordedDTO.setAuthSettlementDate(map.get(Constants8583Field.FIELD17));
        //authRecordedDTO.setAuthMerchantType(map.get(Constants8583Field.FIELD18));
        //authRecordedDTO.setAuthMerchantCountryCode(map.get(Constants8583Field.FIELD19));
        //authRecordedDTO.setAuthServicePointEntryModeCode(StringUtils.isBlank(map.get(Constants8583Field.FIELD22)) ? null : map.get(Constants8583Field.FIELD22));
        //authRecordedDTO.setAuthCardSequenceNumber(map.get(Constants8583Field.FIELD23));
        //authRecordedDTO.setAuthServicePointConditionCode(map.get(Constants8583Field.FIELD25));
        //authRecordedDTO.setAuthServicePointPinCaptureCode(map.get(Constants8583Field.FIELD26));
        //authRecordedDTO.setAuthTransactionFee(StringUtils.isBlank(map.get(Constants8583Field.FIELD28)) ? null : map.get(Constants8583Field.FIELD28));
        //authRecordedDTO.setAuthAcquiringIdentificationCode(map.get(Constants8583Field.FIELD32));
        //authRecordedDTO.setAuthForwardingIdentificationCode(map.get(Constants8583Field.FIELD33));
        //authRecordedDTO.setAuthTrack2Data(map.get(Constants8583Field.FIELD35));
        //authRecordedDTO.setAuthRetrievalReferenceNumber(map.get(Constants8583Field.FIELD37));
        //authRecordedDTO.setAuthAuthIdentificationResponse(map.get(Constants8583Field.FIELD38));
        //
        //// 初始化为 "00" authRecordedDTO.setAuthResponseCode(map.get(Constants8583Field.FIELD39))
        //authRecordedDTO.setAuthResponseCode(AuthConstans.AUTH_CHECK_RESPONSE_CODE);
        //
        //authRecordedDTO.setAuthCardAcceptorTerminalCode(map.get(Constants8583Field.FIELD41));
        //authRecordedDTO.setAuthCardAcceptorIdentification(map.get(Constants8583Field.FIELD42));
        //authRecordedDTO.setAuthCardAcceptorNameLocation(map.get(Constants8583Field.FIELD43));
        //authRecordedDTO.setAuthAdditionalResponseData(map.get(Constants8583Field.FIELD44));
        //authRecordedDTO.setAuthTrack1Data(map.get(Constants8583Field.FIELD45));
        //authRecordedDTO.setAuthAdditionalDataPrivate(map.get(Constants8583Field.FIELD48));
        //authRecordedDTO.setAuthTransactionCurrencyCode(map.get(Constants8583Field.FIELD49));
        //authRecordedDTO.setAuthBillingCurrencyCode(map.get(Constants8583Field.FIELD51));
        //authRecordedDTO.setAuthPinData(map.get(Constants8583Field.FIELD52));
        //authRecordedDTO.setAuthSecurityRelatedControlInformation(map.get(Constants8583Field.FIELD53));
        //authRecordedDTO.setAuthAdditionalAmounts(map.get(Constants8583Field.FIELD54));
        //authRecordedDTO.setAuthIccSystemRelatedData(map.get(Constants8583Field.FIELD55));
        //authRecordedDTO.setAuthNationalPointOfServiceGeographicData1(map.get(Constants8583Field.FIELD59));
        //authRecordedDTO.setAuthAdditionalPosInformation(StringUtils.isBlank(map.get(Constants8583Field.FIELD60)) ? null : map.get(Constants8583Field.FIELD60));
        //authRecordedDTO.setAuthOtherAmount(StringUtils.isBlank(map.get(Constants8583Field.FIELD61)) ? null : map.get(Constants8583Field.FIELD61));
        //
        //authRecordedDTO.setAuthCustomerPaymentServiceFields(map.get(Constants8583Field.FIELD62));
        //authRecordedDTO.setAuthVipPrivateUseField(map.get(Constants8583Field.FIELD63));
        //authRecordedDTO.setAuthReceivingInstitutionCountryCode(map.get(Constants8583Field.FIELD68));
        //authRecordedDTO.setAuthNetworkManagementInformationCode(map.get(Constants8583Field.FIELD70));
        //authRecordedDTO.setAuthActionData(map.get(Constants8583Field.FIELD73));
        //
        //if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD90))) {
        //    authRecordedDTO.setAuthOriginalMessageTypeId(map.get(Constants8583Field.FIELD90).substring(0, 4));
        //    authRecordedDTO.setAuthOriginalSystemTraceAuditNumber(map.get(Constants8583Field.FIELD90).substring(4, 10));
        //    authRecordedDTO.setAuthOriginalTransmissionTime(map.get(Constants8583Field.FIELD90).substring(10, 20));
        //    authRecordedDTO.setAuthOriginalAcquiringIdentificationCode(map.get(Constants8583Field.FIELD90).substring(20, 31));
        //    authRecordedDTO.setAuthOriginalForwardingIdentificationCode(map.get(Constants8583Field.FIELD90).substring(31, 42));
        //}
        //
        //authRecordedDTO.setAuthFileUpdateCode(map.get(Constants8583Field.FIELD91));
        //authRecordedDTO.setAuthFileUpdateCode(map.get(Constants8583Field.FIELD92));
        //
        //// 95号域不为空
        //// 判断95号域
        //boolean field95Flag = StringUtils.isNotBlank(map.get(Constants8583Field.FIELD95)) && StringUtils.isNumeric(map.get(Constants8583Field.FIELD95));
        //if (field95Flag) {
        //    BigDecimal partAmount = new BigDecimal(map.get(Constants8583Field.FIELD95)).divide(new BigDecimal(AuthConstans.DIVIDE_100));
        //    authRecordedDTO.setAuthReplaceAmountActualVisa(partAmount);
        //}
        //
        //authRecordedDTO.setAuthReceivingInstitutionIdentificationCode(map.get(Constants8583Field.FIELD100));
        //authRecordedDTO.setAuthFileName(map.get(Constants8583Field.FIELD101));
        //authRecordedDTO.setAuthAccountIdentification1(map.get(Constants8583Field.FIELD102));
        //authRecordedDTO.setAuthAccountIdentification2(map.get(Constants8583Field.FIELD103));
        //authRecordedDTO.setAuthTransactionDescription5(map.get(Constants8583Field.FIELD104));
        //authRecordedDTO.setAuthAdditionalTraceData(map.get(Constants8583Field.FIELD115));
        //authRecordedDTO.setAuthCardIssuerReferenceData(map.get(Constants8583Field.FIELD116));
        //authRecordedDTO.setAuthNationalUse(map.get(Constants8583Field.FIELD117));
        //authRecordedDTO.setAuthIntraCountryData(map.get(Constants8583Field.FIELD118));
        //authRecordedDTO.setAuthIssuingInstitutionIdentificationCode(map.get(Constants8583Field.FIELD121));
        //authRecordedDTO.setAuthVerificationData(map.get(Constants8583Field.FIELD123));
        //authRecordedDTO.setAuthSupportingInformation(map.get(Constants8583Field.FIELD125));
        //authRecordedDTO.setAuthVipPrivateUseField(map.get(Constants8583Field.FIELD126));
        //authRecordedDTO.setAuthTerminalCapabilityProfile(map.get(Constants8583Field.FIELD130));
        //authRecordedDTO.setAuthTerminalVerificationResults(map.get(Constants8583Field.FIELD131));
        //authRecordedDTO.setAuthUnpredictableNumber(map.get(Constants8583Field.FIELD132));
        //authRecordedDTO.setAuthTerminalSerialNumber(map.get(Constants8583Field.FIELD133));
        //authRecordedDTO.setAuthVisaDiscretionaryData(map.get(Constants8583Field.FIELD134));
        //authRecordedDTO.setAuthIssuerDiscretionaryData(map.get(Constants8583Field.FIELD135));
        //authRecordedDTO.setAuthCryptogram(map.get(Constants8583Field.FIELD136));
        //authRecordedDTO.setAuthApplicationTransactionCounter(map.get(Constants8583Field.FIELD137));
        //authRecordedDTO.setAuthApplicationInterchangeProfile(map.get(Constants8583Field.FIELD138));
        //authRecordedDTO.setAuthArpcResponseCryptogramAndCode(map.get(Constants8583Field.FIELD139));
        //authRecordedDTO.setAuthIssuerAuthenticationData(map.get(Constants8583Field.FIELD140));
        //authRecordedDTO.setAuthIssuerScript(map.get(Constants8583Field.FIELD142));
        //authRecordedDTO.setAuthIssuerScriptResults(map.get(Constants8583Field.FIELD143));
        //authRecordedDTO.setAuthCryptogramTransactionType(map.get(Constants8583Field.FIELD144));
        //authRecordedDTO.setAuthTerminalCountryCode(map.get(Constants8583Field.FIELD145));
        //authRecordedDTO.setAuthTerminalTransactionDate(map.get(Constants8583Field.FIELD146));
        //authRecordedDTO.setAuthCryptogramAmount(map.get(Constants8583Field.FIELD147));
        //authRecordedDTO.setAuthCryptogramCurrencyCode(map.get(Constants8583Field.FIELD148));
        //authRecordedDTO.setAuthCryptogramCashbackAmount(map.get(Constants8583Field.FIELD149));
        //authRecordedDTO.setAuthSecondaryPinBlock(map.get(Constants8583Field.FIELD152));
        //
        //// 授权类型代码
        //if (MTIEnum.AUTH_NOTICE.getCode().equals(mti)) {
        //    authRecordedDTO.setAuthAuthTypeCode(AuthTypeCodeEnum.GENERATION_AUTH.getCode());
        //} else {
        //    authRecordedDTO.setAuthAuthTypeCode(AuthTypeCodeEnum.REGULAR_AUTH.getCode());
        //}
        //boolean bool = (MTIEnum.AUTH_REQUEST.getCode().equals(mti) || MTIEnum.AUTH_NOTICE.getCode().equals(mti)) && !AuthConstans.MTI_FIRST_TWO_VALUE.equals(map.get(Constants8583Field.FIELD3).substring(0, 2));
        //if (bool) {
        //    authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionTypeTopCode);
        //    authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetailCode);
        //}
        //// 3号域前2位
        //String firstTwoCode = map.get(Constants8583Field.FIELD3).substring(0, 2);
        //// visa 消费
        //if (MTIEnum.AUTH_REQUEST.getCode().equals(mti) || MTIEnum.AUTH_NOTICE.getCode().equals(mti)) {
        //    if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_THREE_ONE.getCode().equals(firstTwoCode) || "21,40,41".contains(firstTwoCode)) {
        //        authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
        //        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        //    }
        //}
        //// visa 撤销
        //if (MTIEnum.REVERSAL_TRANS.getCode().equals(mti)) {
        //    if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode)) {
        //        authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_TRANS.getCode());
        //        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_TRANS.getCode());
        //    }
        //}
        //// visa 冲正
        //if (MTIEnum.REVERSAL_NOTICE.getCode().equals(mti)) {
        //    if (AuthProcessingCodeEnum.FIRST_TWO_ONE.getCode().equals(firstTwoCode) || AuthProcessingCodeEnum.FIRST_TWO_ZERO.getCode().equals(firstTwoCode)) {
        //        authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVERSAL_TRANS.getCode());
        //        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());
        //        // 撤销冲正
        //    } else {
        //        authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
        //        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
        //    }
        //}
        //authRecordedDTO.setAuthTransactionSourceCode(AuthTransactionSourceCodeEnum.VISA.getCode());
        return authRecordedDTO;
    }

    private boolean isBlankForF095(String f095) {

        if (StringUtils.isBlank(f095)) {
            return true;
        }

        return Objects.equals(StringUtils.leftPad("", 12, "0"), f095);
    }

    /**
     * 根据币种转换金额，接口涉及的金额和币种，在域值校验阶段已校验，此处不再校验
     *
     * @param currencyCode
     * @param amount
     * @return
     */
    public BigDecimal convertAmountByCurrency(String currencyCode, String amount) {
        ParmCurrencyCode parmCurrencyCode = parmCurrencyCodeSelfMapper.selectByCurrencyCode(currencyCode);
        if (Objects.isNull(parmCurrencyCode) || Objects.isNull(parmCurrencyCode.getDecimalPlace())) {
            logger.error("convertAmountByCurrency| query parm_currency_code is null, currencyCode:{}", currencyCode);
            throw new AnyTxnAuthException(AuthResponseCodeEnum.TABLE_CURRENCY_CODE_ERROR);
        }
        BigDecimal amountDecimal = new BigDecimal(amount);
        BigDecimal resultAmount = amountDecimal.divide(prepareDecimalPlace(parmCurrencyCode.getDecimalPlace()));
        logger.info("convertAmountByCurrency|currencyCode:{}, amount:{}, resultAmount:{}", currencyCode, amount, resultAmount);
        return resultAmount.setScale(2, BigDecimal.ROUND_DOWN);
    }

    /**
     * 换算金额时，根据小数位确定除数，比如2位小数，返回100
     *
     * @param decimalPlace
     * @return
     */
    private BigDecimal prepareDecimalPlace(int decimalPlace) {
        if (decimalPlace == 0) {
            return BigDecimal.ONE;
        }
        StringBuffer buffer = new StringBuffer("1");
        buffer.append(String.format("%0" + decimalPlace + "d", 0));
        return new BigDecimal(buffer.toString());
    }
}
