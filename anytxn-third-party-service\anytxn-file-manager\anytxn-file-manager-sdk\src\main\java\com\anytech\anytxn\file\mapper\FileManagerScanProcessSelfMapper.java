package com.anytech.anytxn.file.mapper;

import com.anytech.anytxn.file.domain.model.FileManagerScanProcess;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


public interface FileManagerScanProcessSelfMapper {

    /**
     * 这个方法数据量很小，不介意使用一下select * 吧
     *  because of small amount of data，forgive me for using select *
     */

    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PROCESS ",
            "where  ",
            "ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "and LAST_SCAN_PROCESS_ID = #{id,jdbcType=VARCHAR}",
            "</script>",
    })

    @Results(id = "FileManagerScanProcessResult", value = {
            @Result(column = "ID", property = "id", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "SCAN_PARAM_ID", property = "scanParamId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "FILE_TYPE", property = "fileType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "FILE_PATH", property = "filePath", jdbcType = JdbcType.VARCHAR),
            @Result(column = "FILE_NAME", property = "fileName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "TRIGGER_TIME", property = "triggerTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "SCHEDULE_SEQUENCE_NUMBER", property = "scheduleSequenceNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "SCHEDULE_EXECUTE_STATE", property = "scheduleExecuteState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "SCAN_STATUS", property = "scanStatus", jdbcType = JdbcType.INTEGER),
            @Result(column = "ORGANIZATION_NUMBER", property = "organizationNumber", jdbcType = JdbcType.CHAR),
            @Result(column = "CREATE_TIME", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "UPDATE_TIME", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "UPDATE_BY", property = "updateBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "VERSION_NUMBER", property = "versionNumber", jdbcType = JdbcType.INTEGER)
    })
    FileManagerScanProcess selectByParamId(String id, String organizationNumber);

    /*@Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PROCESS ",
            "where 1 = 1 ",
            "<if test=\"organizationNumber != '0000'\"> ",
            " and ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "</if>",
            "<if test='fileType != null'>",
            " and FILE_TYPE like  concat('%',#{fileType},'%')  ",
            "</if>",
            "<if test='triggerTimeDateStart != null'>",
            " and TRIGGER_TIME &gt;=  #{triggerTimeDateStart,jdbcType=TIMESTAMP} ",
            "</if>",
            "<if test='triggerTimeDateEnd != null'>",
            " and TRIGGER_TIME &lt;  #{triggerTimeDateEnd,jdbcType=TIMESTAMP} ",
            "</if>",
            "order by TRIGGER_TIME desc",
            "</script>",
    })

    @ResultMap("FileManagerScanProcessResult")
    List<FileManagerScanProcess> selectByCondition(String organizationNumber, String fileType,LocalDateTime triggerTimeDateStart,LocalDateTime triggerTimeDateEnd);*/

    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PROCESS ",
            "where 1 = 1 ",
            "<if test=\"organizationNumber != '0000'\"> ",
            " and ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "</if>",
            "<if test='fileType != null'>",
            " and FILE_TYPE like  concat('%',#{fileType},'%')  ",
            "</if>",
            "<if test='triggerTimeDateStart != null'>",
            " and TRIGGER_TIME &gt;=  #{triggerTimeDateStart,jdbcType=TIMESTAMP} ",
            "</if>",
            "<if test='triggerTimeDateEnd != null'>",
            " and TRIGGER_TIME &lt;  #{triggerTimeDateEnd,jdbcType=TIMESTAMP} ",
            "</if>",
            "order by TRIGGER_TIME desc",
            "</script>",
    })
    @ResultMap("FileManagerScanProcessResult")
    Page<FileManagerScanProcess> selectByCondition(Page<FileManagerScanProcess> page, String organizationNumber, String fileType, LocalDateTime triggerTimeDateStart, LocalDateTime triggerTimeDateEnd);

    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PROCESS",
            "where SCAN_STATUS in (200) ",
            "</script>",
    })

    @ResultMap("FileManagerScanProcessResult")
    List<FileManagerScanProcess> selectToQueryScheduleStatus();


    @Select({
            "<script>",
            "select ",
            "*",
            "from CM_FILE_MANAGER_SCAN_PROCESS",
            "where MD5 = #{md5} ",
            "and ORGANIZATION_NUMBER = #{organizationNumber,jdbcType=CHAR}",
            "<if test='id != null '>",
            "and  ID &lt;&gt; #{id}",
            "</if>",
//            "and ( FILE_PATH &lt;&gt; #{path}",
//            "or FILE_NAME &lt;&gt; #{fileName} )",
            "AND TRIGGER_TIME &gt; #{startDate,jdbcType=TIMESTAMP}",
            "AND FILE_TYPE  = #{fileType} limit 1",
            "</script>",
    })

    @ResultMap("FileManagerScanProcessResult")
    FileManagerScanProcess selectByMD5(String md5, LocalDate startDate,String fileType,String id, String organizationNumber);
}
