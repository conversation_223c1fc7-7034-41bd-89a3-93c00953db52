package com.anytech.anytxn.installment.service;


import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.common.core.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
//import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.domain.dto.AccountManagementRecordedDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryResDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallTradingSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallEntryFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.enums.InstallTransactionIndEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentDerateMethodEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallRecordSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallBillEntryService;
import com.anytech.anytxn.installment.base.service.IInstallEntryService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListService;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单分期业务逻辑实现
 *
 * <AUTHOR>
 * @date 2019/8/19
 */
@Service
public class InstallBillEntryServiceImpl implements IInstallBillEntryService {

    private static final Logger logger = LoggerFactory.getLogger(InstallBillEntryServiceImpl.class);

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private IInstallStagingListService installStagingListService;

    @Autowired
    private IInstallEntryService installEntryService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private InstallRecordSelfMapper installRecordSelfMapper;

    @Autowired
    ParmCardProductInfoSelfMapper cardProductInfoSelfMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private IInstallTypeParmService installTypeParmService;

    @Resource
    private InstallManager installManager;

    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;

    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;


    @Override
    public BigDecimal getAvailableStatementAmount(String accountManagementId,AccountStatementInfoDTO accountStatementInfoDTO){

        BigDecimal statementAmount = accountStatementInfoDTO.getCloseBalance();

        //根据交易码 过滤部分交易
        logger.info("Calling installTypeSupportTxnService.getByTypeAndOrgNum: type={}, organizationNumber={}",
                InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), accountStatementInfoDTO.getOrganizationNumber());
        List<String> transCodes = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(),
                accountStatementInfoDTO.getOrganizationNumber()).stream()
                .map(InstallTypeSupportTxnResDTO::getTransactionCode).collect(Collectors.toList());
        logger.info("installTypeSupportTxnService.getByTypeAndOrgNum completed: transCodesSize={}", transCodes.size());

        List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByAccStaId(accountStatementInfoDTO.getStatementId(), accountStatementInfoDTO.getOrganizationNumber());

        for (PostedTransaction e : postedTransactions) {
            if (transCodes.contains(e.getPostingTransactionCode()) && Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), e.getDebitCreditIndcator())){
                statementAmount = statementAmount.subtract(e.getPostingAmount());
            }
        }

        statementAmount = statementAmount.max(BigDecimal.ZERO);

        logger.info("Calling installTypeParmService.findByOrgNumAndType: organizationNumber={}, type={}",
                accountStatementInfoDTO.getOrganizationNumber(), InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
        InstallTypeParmResDTO installTypeParmRes = installTypeParmService.findByOrgNumAndType(
                accountStatementInfoDTO.getOrganizationNumber(), InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
        logger.info("installTypeParmService.findByOrgNumAndType completed: statementInstallPer={}",
                installTypeParmRes != null ? installTypeParmRes.getStatementInstallPer() : null);
        //账单分期可分期金额百分比
        BigDecimal statementInstallPer = installTypeParmRes.getStatementInstallPer();

        statementAmount = statementAmount.multiply(statementInstallPer.divide(BigDecimal.valueOf(100)))
                .setScale(2, RoundingMode.HALF_UP);


        logger.info("statementAmount{},statementInstallPer:{}", statementAmount, statementInstallPer);

        return statementAmount;



       /* //期末余额
        BigDecimal closeBalance = accountStatementInfoDTO.getCloseBalance();
        //最小还款额
        BigDecimal totalDueAmount = accountStatementInfoDTO.getTotalDueAmount();

        //中行POC改造 amt1 = 期末余额-宽限期内还款金额,amt2 =（期末余额-最小还款额）*百分比，min(amt1,amt2)
        BigDecimal totalGracePaymentAmount = accountManagementInfo.getTotalGracePaymentAmount();
        BigDecimal amt1 = closeBalance.subtract(totalGracePaymentAmount);

        if (amt1.compareTo(BigDecimal.ZERO) <= 0){
            return BigDecimal.ZERO;
        }


        //可分期金额(期末余额-最小还款金额)
        BigDecimal amt2 = closeBalance.subtract(totalDueAmount);
        InstallTypeParmResDTO installTypeParmRes = installTypeParmService.findByOrgNumAndType(accountManagementInfo.getOrganizationNumber(), InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
        //账单分期可分期金额百分比
        BigDecimal statementInstallPer = installTypeParmRes.getStatementInstallPer();
        amt2 = amt2.multiply(statementInstallPer.divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);


        logger.info("amt1:{},amt2:{}, totalGracePaymentAmount:{},statementInstallPer:{}", amt1, amt2, totalGracePaymentAmount, statementInstallPer);
        //取两者最小值
        return  amt1.min(amt2);*/


    }


    /**
     * 账单分期查询
     *
     * @param accountManagementId 管理账户id
     * @return {@link InstallTradingDTO}
     */
    @Override
    public List<InstallTradingDTO> findBillInstall(String accountManagementId) {
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.error("Bill installment query failed: account management ID cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AC_BI);
        }
        List<InstallTradingDTO> installTradingDtos = new ArrayList<>();
        List<AccountStatementInfoDTO> lastedStatementInfo = findLastedStatementInfo(accountManagementId);
        if (!lastedStatementInfo.isEmpty()) {
            //获取账单日最大的一条记录
            AccountStatementInfoDTO accountStatementInfoDTO = lastedStatementInfo.get(0);
            logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accountStatementInfoDTO.getOrganizationNumber());
            OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accountStatementInfoDTO.getOrganizationNumber());
            logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);

            LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();



            /*//期末余额
            BigDecimal closeBalance = accountStatementInfoDTO.getCloseBalance();
            //最小还款额
            BigDecimal totalDueAmount = accountStatementInfoDTO.getTotalDueAmount();
            //可分期金额(期末余额-最小还款金额)
            BigDecimal amt2 = closeBalance.subtract(totalDueAmount);
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
            //宽限期内还款金额
            BigDecimal totalGracePaymentAmount = accountManagementInfo.getTotalGracePaymentAmount();
            //中行POC改造 amt1 = 期末余额-宽限期内还款金额,amt2 =（期末余额-最小还款额）*百分比，min(amt1,amt2)
            BigDecimal amt1 = closeBalance.subtract(totalGracePaymentAmount);
            //可分期金额为0的不让分期
            if (BigDecimal.ZERO.compareTo(amt1) >= 0) {
                return installTradingDtos;
            }
            InstallTypeParmResDTO installTypeParmRes = installTypeParmService.findByOrgNumAndType(accountManagementInfo.getOrganizationNumber(), InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
            //账单分期可分期金额百分比
            BigDecimal statementInstallPer = installTypeParmRes.getStatementInstallPer();
            amt2 = amt2.multiply(statementInstallPer.divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);
            logger.info("amt1:{},amt2:{}, totalGracePaymentAmount:{},statementInstallPer:{}", amt1, amt2, totalGracePaymentAmount, statementInstallPer);
            //确定可分期金额
            installAmount = amt1.compareTo(amt2) < 0 ? amt1 : amt2;*/
            //可分期金额为0的不让分期
            /*if (BigDecimal.ZERO.compareTo(installAmount) >= 0) {
                return installTradingDtos;
            }*/
            //2021-07-07注掉原来amt2的逻辑 变为（期末余额-最小还款额）*百分比
            //根据账号、账单账户id查询已入帐交易信息表
            /*if (accountStatementInfoDTO.getStatementId() == null || "".equals(accountStatementInfoDTO.getStatementId())) {
                logger.error("账户账单id不能为空,accountStatementId");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ACCT_STATEMENT_ID_NOT_NULL_FAULT);
            }
            int partition = TransPartitionKeyHelper.getPartitionKeyInt(accountManagementInfo.getCustomerId());
            //版本合并方法出错 去掉分区partition
            List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByMidAndSid(accountManagementId, accountStatementInfoDTO.getStatementId());
            List<PostedTransactionResDTO> postedTransactionResDtos = BeanMapping.copyList(postedTransactions, PostedTransactionResDTO.class);
            //根据分期类型、机构号查询分期交易类型表
            List<InstallTypeSupportTxnResDTO> installTypeSupportDTOList = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), accountManagementInfo.getOrganizationNumber());
            //不支持分期的MCC
            List<InstallTypeSupportMcc> installTypeMccs = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), accountManagementInfo.getOrganizationNumber());
            List<String> mccList = new ArrayList<>();
            if (installTypeMccs != null && !installTypeMccs.isEmpty()) {
                mccList = installTypeMccs.stream().map(InstallTypeSupportMcc::getMcc).collect(Collectors.toList());
            }
            //入账金额
            BigDecimal postingAmount = BigDecimal.ZERO;
            //筛选可分期的交易账户
            Map<String, AccountBalanceInfo> availStmtInstAccts = new HashMap<>(16);
            //如果列表为空说明业务没有设置所以所有交易码都支持账单分期
            if (installTypeSupportDTOList != null && !installTypeSupportDTOList.isEmpty()) {
                //如果列表不为空,筛选列表中支持的交易
                for (InstallTypeSupportTxnResDTO i : installTypeSupportDTOList) {
                    for (PostedTransactionResDTO p : postedTransactionResDtos) {
                        //如果入账交易码在分期交易类型表中存在
                        if (Objects.equals(p.getPostingTransactionCode(), i.getTransactionCode())
                                && !mccList.contains(p.getMcc())) {
                            //增加判断：DEBIT_CREDIT_INDCATOR = 'D' && （INSTALLMENT_INDICATOR IS NULL||INSTALLMENT_INDICATOR ！= 'Y'） &&
                            //(INSTALLMENT_INDICATOR != 'Y'||INSTALLMENT_INDICATOR IS NULL) && (INSTALLMENT_ORDER_ID = ''||INSTALLMENT_ORDER_ID IS NULL)
                            logger.info("交易是否可分期判断：DebitCreditIndcator{},ReversalIndicator:{},InstallmentIndicatorEnum:{},InstallmentOrderId:{}",p.getDebitCreditIndcator(),p.getReversalIndicator(),p.getInstallmentIndicator(),p.getInstallmentOrderId());
                            if ("D".equals(p.getDebitCreditIndcator())
                                    && (!"Y".equals(p.getReversalIndicator())|| StringUtils.isEmpty(p.getReversalIndicator()))
                                    &&(!"Y".equals(p.getInstallmentIndicator()) || StringUtils.isEmpty(p.getInstallmentIndicator()))
                                    && StringUtils.isEmpty(p.getInstallmentOrderId())
                            ) {
                                //累加入账金额
                                //根据posted_transaction找到对应的交易账户，取交易账户的余额做加总，如果涉及多笔posted_transation对应同一交易账户需要去重
                                List<PostedTranAccountRelationInfo> tranAccountRelationInfos = postedTranAccountRelationInfoSelfMapper.selectTransIdAndAmountByPostId(p.getPostedTransactionId(), p.getOrganizationNumber());
                                if (CollectionUtils.isEmpty(tranAccountRelationInfos)){
                                    continue;
                                }
                                List<String> transList = tranAccountRelationInfos.stream().map(PostedTranAccountRelationInfo::getTransactionBalanceId).collect(Collectors.toList());
                                List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectBalancesByTransIds(p.getOrganizationNumber(), transList);
                                for (AccountBalanceInfo accountBalanceInfo : accountBalanceInfos) {
                                    if (accountBalanceInfo != null ){
                                        ParmTransactionType parmTransactionType = parmTransactionTypeSelfMapper.selectByTypeCode(accountBalanceInfo.getTransactionTypeCode(), accountBalanceInfo.getOrganizationNumber());
                                        if(DebitCreditIndEnum.DEBIT_INDICATOR.getCode().equals(parmTransactionType.getBalanceLoanDirection())){
                                            availStmtInstAccts.put(accountBalanceInfo.getTransactionBalanceId(), accountBalanceInfo);
                                        }
                                    }
                                }
//                                postingAmount = postingAmount.add(p.getPostingAmount());
                            }

                        }
                    }
                }
            }

            //交易账户余额汇总可分期金额
            logger.info("筛选出的可分期交易账户size:{}", availStmtInstAccts.size());
            for (AccountBalanceInfo accountBalanceInfo : availStmtInstAccts.values()) {
                if (accountBalanceInfo!=null){
                    logger.info("可分期交易账户号:{},balacen:{}", accountBalanceInfo.getTransactionBalanceId(),availStmtInstAccts.size());
                    postingAmount = postingAmount.add(accountBalanceInfo.getBalance()==null ? BigDecimal.ZERO : accountBalanceInfo.getBalance());
                }
            }*/

            BigDecimal availableStatementAmount = getAvailableStatementAmount(accountManagementId, accountStatementInfoDTO);

            if (availableStatementAmount.compareTo(BigDecimal.ZERO) <= 0){
                return installTradingDtos;
            }

            InstallTradingSearchKeyDTO installTradingSearchKeyDTO = new InstallTradingSearchKeyDTO();
            installTradingSearchKeyDTO.setOrganizationNumber(organizationInfo.getOrganizationNumber());
            installTradingSearchKeyDTO.setInstallAmount(availableStatementAmount);
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
            installTradingSearchKeyDTO.setTerm(0);
            //交易日期
            installTradingSearchKeyDTO.setTransDate(nextProcessingDay);
            //还款日
            installTradingSearchKeyDTO.setPaymentDate(accountStatementInfoDTO.getPaymentDueDate());
            // 账单分期生成订单时，app账单分期和TXN账单分期互斥
            List<String> installTypes = Arrays.asList(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode());
            List<InstallRecord> installRecords = installRecordSelfMapper.selectByOrgManageIdAndInstallTypes(organizationInfo.getOrganizationNumber(), accountManagementId,installTypes);
            if (!installRecords.isEmpty()) {
                List<String> transactionInds = new ArrayList<>();
                List<LocalDate> statementDates = new ArrayList<>();
                for (InstallRecord installRecord : installRecords) {
                    String transactionInd = installRecord.getTransactionInd();
                    LocalDate statementDate = installRecord.getStatementDate();
                    transactionInds.add(transactionInd);
                    statementDates.add(statementDate);
                }
                if (transactionInds.contains(InstallTransactionIndEnum.SUCCESS.getCode()) && statementDates.contains(accountStatementInfoDTO.getStatementDate())) {
                    //当前账单已经分期,返回列表为空
                    return installTradingDtos;
                } else {
                    logger.info("Calling installStagingListService.findInstallTradingByOptions: accountManagementId={}", accountManagementId);
                    List<InstallTradingDTO> result = installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
                    logger.info("installStagingListService.findInstallTradingByOptions completed: resultSize={}", result.size());
                    return result;
                }
            } else {
                logger.info("Calling installStagingListService.findInstallTradingByOptions: accountManagementId={}", accountManagementId);
                List<InstallTradingDTO> result = installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
                logger.info("installStagingListService.findInstallTradingByOptions completed: resultSize={}", result.size());
                return result;
            }
        }
        return installTradingDtos;
    }











    private List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId) {
        try {
            List<AccountStatementInfo> infos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManageInfoId);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(infos)){
                return Collections.emptyList();
            }

            return BeanMapping.copyList(infos, AccountStatementInfoDTO.class);
        } catch (Exception e) {
            logger.error("accountManageInfoId is {} select data error ", accountManageInfoId, e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
    }

    /**
     * 账单分期交易赋值及录入
     *
     * @param installEntryDTO {@link InstallEntryDTO}
     * @return Long
     */
    @Override
    public InstallEntryResDTO billInInstallment(InstallEntryDTO installEntryDTO) {
        //必输项检查
        if (StringUtils.isBlank(installEntryDTO.getAccountManagementId())) {
            logger.error("Bill installment entry failed: account management ID cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_TR);
        }

        if (StringUtils.isBlank(installEntryDTO.getProductCode())) {
            logger.error("Bill installment entry failed: product code cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_PR);
        }

        if (installEntryDTO.getInstallAmount() == null || installEntryDTO.getInstallAmount().compareTo(BigDecimal.ZERO) <= 0) {
            logger.error("Bill installment entry failed: install amount must be greater than zero");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_AM);
        }

        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", installEntryDTO.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);
        if (organizationInfo == null) {
            logger.error("Organization info not found: organizationNumber={}", installEntryDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }

        logger.info("Calling installManager.getCardNumberByAccountManagementId: accountManagementId={}", installEntryDTO.getAccountManagementId());
        String cardNumber = installManager.getCardNumberByAccountManagementId(installEntryDTO);
        logger.info("installManager.getCardNumberByAccountManagementId completed: cardNumber={}", cardNumber);

        installEntryDTO.setFunctionCode(InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode());
        installEntryDTO.setOrganizationNumber(organizationInfo.getOrganizationNumber());
        installEntryDTO.setCardNumber(cardNumber);
        installEntryDTO.setInstallType(InstallmentTypeEnum.STATEMENT_INSTALL.getCode());
        installEntryDTO.setTransactionDate(organizationInfo.getNextProcessingDay());
        installEntryDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        installEntryDTO.setInstallTotalFee(BigDecimal.ZERO);
        installEntryDTO.setInstallFeeRate(BigDecimal.ZERO);
        installEntryDTO.setInstallDerateMethod(InstallmentDerateMethodEnum.REDUCTION_NO.getCode());
        installEntryDTO.setInstallDerateValue(BigDecimal.ZERO);

        logger.info("Calling installManager.getInstallmentProductDesc: accountManagementId={}", installEntryDTO.getAccountManagementId());
        installManager.getInstallmentProductDesc(installEntryDTO);
        logger.info("installManager.getInstallmentProductDesc completed");

        //分期录入
        logger.info("Calling installEntryService.entry: accountManagementId={}", installEntryDTO.getAccountManagementId());
        String orderId = installEntryService.entry(installEntryDTO).getOrderId();
        logger.info("installEntryService.entry completed: orderId={}", orderId);

        if (StringUtils.isBlank(orderId)){
            logger.error("account manager id {}  statement installment failure ",
                    installEntryDTO.getAccountManagementId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BILL_INST_ENTRY_FAIL_FAULT);
        }

        return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                    .withCardNumber(cardNumber)
                    .withOrderId(orderId).build();

    }


    @Override
    public List<AccountManagementRecordedDTO> findCardNumberByAccountManagementId(String accountManagementId) {
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.error("Find card number by account management ID failed: account management ID cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.AC_BI);
        }
        List<AccountManagementRecordedDTO> accountManagementRecordedDtos = new ArrayList<>();
        List<AccountStatementInfoDTO> lastedStatementInfoList = findLastedStatementInfo(accountManagementId);
        if (!CollectionUtils.isEmpty(lastedStatementInfoList)) {
            //获取账单日最大的一条记录
            AccountStatementInfoDTO accountStatementInfoDTO = lastedStatementInfoList.get(0);
            //根据账号、账单账户id查询已入帐交易信息表
            if (accountStatementInfoDTO.getStatementId() == null || "".equals(accountStatementInfoDTO.getStatementId())) {
                logger.error("Account statement ID cannot be empty: accountStatementId");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ACCT_STATEMENT_ID_NOT_NULL_FAULT);
            }
//            List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByMidAndSid(accountManagementId, accountStatementInfoDTO.getStatementId());
//            List<PostedTransactionResDTO> postedTransactionResDtos = BeanMapping.copyList(postedTransactions, PostedTransactionResDTO.class);
            //根据分期类型、机构号查询分期交易类型表
//            List<InstallTypeSupportTxnResDTO> installTypeSupportDTOList = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), accountStatementInfoDTO.getOrganizationNumber());
            //如果列表为空说明业务没有设置所以所有交易码都支持账单分期
//            if (installTypeSupportDTOList != null && !installTypeSupportDTOList.isEmpty()) {
            //如果列表不为空,筛选列表中支持的交易
//                for (InstallTypeSupportTxnResDTO i : installTypeSupportDTOList) {
//                    for (PostedTransactionResDTO p : postedTransactionResDtos) {
            //如果入账交易码在分期交易类型表中存在
//                        if (Objects.equals(p.getPostingTransactionCode(), i.getTransactionCode())) {
            AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByAccountManagementIdAndOrganizationNumber(accountManagementId, accountStatementInfoDTO.getOrganizationNumber());
            List<CardAuthorizationInfo> cardAuthorizationList = null;
            if (accountManagementInfo != null) {
                cardAuthorizationList = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerIdAndOrganizationNumber(accountStatementInfoDTO.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                //primary cusID 查询不到卡片信息时，使用CorpcustomerId进行查询
                if(CollectionUtils.isEmpty(cardAuthorizationList)){
                    cardAuthorizationList = cardAuthorizationInfoSelfMapper.selectByOrgCorpCusId(accountStatementInfoDTO.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                }
            }

            List<String> cardProductNumbers = cardProductInfoSelfMapper.selectByOrgAndAccountProductNum(OrgNumberUtils.getOrg(),
                    accountManagementInfo.getProductNumber()).stream().map(cardProductInfo -> cardProductInfo.getProductNumber())
                    .collect(Collectors.toList());

            if (cardAuthorizationList == null || cardAuthorizationList.isEmpty()) {
                logger.error("Bill installment query card authorization table failed: accountManagementId={}", accountManagementId);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_NOT_EXIST_FAULT);
            }
            for (CardAuthorizationInfo cardAuthorizationInfo : cardAuthorizationList) {
                if (cardProductNumbers.contains(cardAuthorizationInfo.getProductNumber())) {
                    AccountManagementRecordedDTO accountManagementRecordedDTO = new AccountManagementRecordedDTO();
                    accountManagementRecordedDTO.setOrganizationNumber(cardAuthorizationInfo.getOrganizationNumber());
                    accountManagementRecordedDTO.setProductNumber(cardAuthorizationInfo.getProductNumber());
                    accountManagementRecordedDTO.setCardNumber(cardAuthorizationInfo.getCardNumber());
                    accountManagementRecordedDTO.setCurrency(accountManagementInfo.getCurrency());
                    accountManagementRecordedDTO.setAccountManagementId(accountManagementInfo.getAccountManagementId());
                    accountManagementRecordedDtos.add(accountManagementRecordedDTO);
                }
            }
        }
        return accountManagementRecordedDtos.stream().distinct().collect(Collectors.toList());
    }

}
