package com.anytech.anytxn.authorization.service.channel.upi;

import com.anytech.anytxn.authorization.base.enums.PreAuthTrancactionStatusEnum;
import com.anytech.anytxn.authorization.base.enums.PreAuthTransTypeEnum;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.base.service.upi.IUpiAuthCheckProcessService;
import com.anytech.anytxn.authorization.base.service.upi.IUpiForwardTransactionService;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.ReversalTypeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * UPI
 * @Author: sukang
 * @Date: 2021/6/28 15:54
 */
@Service
public class UpiForwardTransactionServiceImpl implements IUpiForwardTransactionService {
    
    private static final Logger logger = LoggerFactory.getLogger(UpiForwardTransactionServiceImpl.class);
    
    @Autowired
    private IUpiAuthCheckProcessService authCheckProcessService;
    @Autowired
    private IPreAuthorizationLogService preAuthorizationLogService;
    @Resource
    private UpiAuthDetailDataModifyServiceImpl upiAuthDetailDataModifyService;

    @Override
    public int normalTrans(AuthRecordedDTO authRecordedDTO) throws IOException {
        if (authRecordedDTO.getPreAuthComplete()){
            return preAuthComplete(authRecordedDTO);
        }else {
            return authCheckProcessService.authCheck(authRecordedDTO);
        }
    }

    /**
     * 1. 预授权完成 先获取原预授权交易信息
     */
    private int preAuthComplete(AuthRecordedDTO authRecordedDTO) throws IOException {
        logger.info("Calling preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType");
        PreAuthorizationLogDTO preAuthorizationLogDTO = preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType(
                authRecordedDTO.getAuthCardNumber(),authRecordedDTO.getAuthAuthIdentificationResponse()
                        , PreAuthTransTypeEnum.PRE_REQUEST.getCode());
        logger.info("preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType completed");
        //如果预授权完成金额大于预授权金额的115%，则预授权完成交易需要拒绝。
        BigDecimal maxLimitAmount = preAuthorizationLogDTO == null ? BigDecimal.ZERO
                : preAuthorizationLogDTO.getTransactionAmount().multiply(new BigDecimal("1.15"));
        if(preAuthorizationLogDTO == null || (authRecordedDTO.getAuthTransactionAmount().compareTo(maxLimitAmount) > 0 )){
            logger.info("No matching original pre-authorization request found or amount exceeds limit, max limit: {}", maxLimitAmount);
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_AMOUNT_LIMIT_ERROR);
            //记录预授权完成交易
            logger.info("Calling upiAuthDetailDataModifyService.addAuthorizationLog");
            upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            logger.info("upiAuthDetailDataModifyService.addAuthorizationLog completed");
            return AuthItemCheckResCodeEnum.ERROR_CODE.getCode();
        }
        //原交易不为初始状态
        if(!PreAuthTrancactionStatusEnum.INIT.getCode().equals(preAuthorizationLogDTO.getPreauthStatusCurr())){
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_STATUS_ERROR);
            authRecordedDTO.setAuthResponseReasonCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
            logger.info("Calling upiAuthDetailDataModifyService.addAuthorizationLog");
            upiAuthDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            logger.info("upiAuthDetailDataModifyService.addAuthorizationLog completed");
            return AuthItemCheckResCodeEnum.ERROR_CODE.getCode();
        }
        /*
          如果匹配到原交易 则将原交易额度恢复，包括额度流水 用信额度 等 内存恢复
          需要满足发生异常回退 以及检查项不通过 恢复
          由于额度恢复需要卡户人等信息 ，所以额度恢复阶段放在数据准备阶段后执行
        */
        authRecordedDTO.setAuthAuthCode(preAuthorizationLogDTO.getPreauthCode());
        authRecordedDTO.setAuthAuthIdentificationResponse(preAuthorizationLogDTO.getPreauthCode());
        authRecordedDTO.setAuthOriginalGlobalFlowNumber(preAuthorizationLogDTO.getGlobalFlowNumber());
        return authCheckProcessService.authCheck(authRecordedDTO);
    }

}
