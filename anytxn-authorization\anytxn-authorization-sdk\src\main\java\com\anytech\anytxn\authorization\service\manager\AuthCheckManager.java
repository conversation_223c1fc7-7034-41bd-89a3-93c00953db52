package com.anytech.anytxn.authorization.service.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.AnyCloudFraudRespDTO;
import com.anytech.anytxn.authorization.base.domain.dto.TransactionFeeDTO;
import com.anytech.anytxn.authorization.base.enums.AuthKeyIdEnum;
import com.anytech.anytxn.authorization.base.enums.DciMTIEnum;
import com.anytech.anytxn.authorization.service.fegin.anycloud.AnyCloudFraudFeignClient;
import com.anytech.anytxn.authorization.service.fegin.encryption.AuthEncryptionFeignClient;
import com.anytech.anytxn.authorization.service.auth.AuthTransactionFeeServiceImpl;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.domain.dto.AnyCloudFraudDTO;
import com.anytech.anytxn.authorization.base.domain.dto.DataResponse;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.authorization.base.utils.JposUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.encryption.dto.arqc.ArqcDTO;
import com.anytech.anytxn.business.base.encryption.dto.arqc.ArqcDTOBuilder;
import com.anytech.anytxn.parameter.authorization.service.AuthCheckControlServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlResDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTypeDetailCodeDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmKeysInfo;
import com.anytech.anytxn.parameter.base.common.enums.ServerTypeEnum;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmSysClass;

import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmKeysInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.system.ParmSysClassSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.common.core.enums.RuleTypeEnum;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Future;

/**
 * 授权 整个流程管理类
 * @Author: sukang
 * @Date: 2020/8/29 9:09
 */
@Service
public class AuthCheckManager {

    private static final Logger logger = LoggerFactory.getLogger(AuthCheckManager.class);
    
    private static final List<String> CANCEL_ACCOUNT_TRANSACTION_CODE = Arrays.asList("C504","C505","T502","T503");
    public static final List<String> APPROVE_RESPONSE_CODE_MC = Arrays.asList("00","08","10","87");
    private static final List<String> VISA_APPROVE_RESPONSE_CODE = Arrays.asList("00","10","11");
    @Resource
    private AuthTransactionFeeServiceImpl authTransactionFeeService;
    @Value("${anycloud.fraud.actkey:cjvytveravqvogow}")
    private String actKey;
    @Resource
    private AnyCloudFraudFeignClient anyCloudFraudFeignClient;
    @Resource
    private AuthEncryptionFeignClient authEncryptionFeignClient;
    @Resource
    private RuleTransferImpl ruleTransfer;
    @Resource
    private AuthCheckControlServiceImpl authCheckControlService;
    @Autowired
    private ParmKeysInfoSelfMapper keysInfoSelfMapper;
    @Autowired
    private ParmSysClassSelfMapper sysClassSelfMapper;

    /**
     * 是否是销户类授权交易
     * @param authTransactionTypeDetailCode 交易细类
     * @return true 是销户类交易
     */
    public static boolean isCancelAccount(String authTransactionTypeDetailCode, String authTransactionTypeCode){
        return CANCEL_ACCOUNT_TRANSACTION_CODE.contains(authTransactionTypeDetailCode)
                && (!Objects.equals(AuthTransTypeEnum.REVOCATION_TRANS.getCode(),authTransactionTypeCode)
                && !Objects.equals(AuthTransTypeEnum.REVERSAL_TRANS.getCode(),authTransactionTypeCode) );
    }

    /**
     * 计算MarkUp、DCC,ATM交易费、查询费，消费手续费
     * 规则类型不同调用规则引擎
     *
     * @param authorizationCheckProcessingPayload
     * @param authRecordedDTO
     */
    public void buildTransFeeCom(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AuthRecordedDTO authRecordedDTO) {

        List<String> AuthTransTypeEnumDebit = Arrays.asList(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        List<String> PreAuthTransTypeEnumDebit = Arrays.asList(PreAuthTransTypeEnum.PRE_REQUEST.getCode(), PreAuthTransTypeEnum.PRE_COMPLETED.getCode());
        List<String> AuthTransTypeEnumCredit = Arrays.asList(AuthTransTypeEnum.REVOCATION_TRANS.getCode(), AuthTransTypeEnum.REVERSAL_TRANS.getCode(), AuthTransTypeEnum.REFUNDS_TRANS.getCode());
        List<String> PreAuthTransTypeEnumCredit = Arrays.asList( PreAuthTransTypeEnum.PRE_REVOCATION.getCode(), PreAuthTransTypeEnum.PRE_COMPLETED_REVOCATION.getCode());
        List<String> ruleTypeList = new ArrayList<>();
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        ParmTypeDetailCodeDTO parmTypeDetailCodeDTO = authorizationCheckProcessingPayload.getParmTypeDetailCodeDTO();
        TransactionCodeResDTO transactionCodeResDTO = authorizationCheckProcessingPayload.getTransactionCodeResDTO();
        String authTransactionTypeTopCode = authRecordedDTO.getAuthTransactionTypeTopCode();
        String authTransactionTypeDetailCode = authRecordedDTO.getAuthTransactionTypeDetailCode();
        TransactionFeeDTO transactionFeeDTO = new TransactionFeeDTO();
        //当笔交易费金额 初始化为0
        transactionFeeDTO.initAmount();
        //规则因子
        transactionFeeDTO.setAuthTxnTypeCode(authRecordedDTO.getAuthTransactionTypeTopCode());
        transactionFeeDTO.setAuthTxnDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
        transactionFeeDTO.setAmountType(authRecordedDTO.getAmountType());
        transactionFeeDTO.setAuthPOSCountryCode(authRecordedDTO.getAuthMerchantCountryCode());
        transactionFeeDTO.setTransactionCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        transactionFeeDTO.setTransAmount(String.valueOf(authRecordedDTO.getAuthTransactionAmount()));
        transactionFeeDTO.setCurrencyCode(authRecordedDTO.getCurrencyCode());
        transactionFeeDTO.setParentId(cardAuthorizationDTO.getPartnerId());
        transactionFeeDTO.setAccountProductNum(cardAuthorizationDTO.getAccountProductNumber());
        String authTransactionTypeCode = authRecordedDTO.getAuthTransactionTypeCode();
        String preauthTxnType = authRecordedDTO.getPreauthTxnType();
        if (StringUtils.isNotBlank(authTransactionTypeCode)){
            if (AuthTransTypeEnumDebit.contains(authTransactionTypeCode) && !AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode())) {
                transactionFeeDTO.setDebitCreditIndicator("D");
            }
        }
        if (StringUtils.isNotBlank(preauthTxnType)){
            if (PreAuthTransTypeEnumDebit.contains(preauthTxnType)) {
                transactionFeeDTO.setDebitCreditIndicator("D");
            }
        }
        logger.info("authTransactionTypeCode：{}，preauthTxnType:{},debitCreditIndicator：{}", authTransactionTypeCode,preauthTxnType, transactionFeeDTO.getDebitCreditIndicator());
        transactionFeeDTO.setTransactionAttribute(ObjectUtils.isEmpty(transactionCodeResDTO) ? null :transactionCodeResDTO.getTransactionAttribute());
        transactionFeeDTO.setTransactionCode(authRecordedDTO.getPostingTransactionCode());
        transactionFeeDTO.setCardScheme(cardAuthorizationDTO.getCardScheme());
        transactionFeeDTO.setTransactionSource(authRecordedDTO.getAuthTransactionSourceCode());
        transactionFeeDTO.setCardProductCode(cardAuthorizationDTO.getProductNumber());
        transactionFeeDTO.setMcc(authRecordedDTO.getAuthMerchantType());
        transactionFeeDTO.setTxnBillSameCur(authRecordedDTO.getAuthTransactionCurrencyCode().equals(authRecordedDTO.getAuthBillingCurrencyCode()) ? "Y" : "N");
        logger.info("TranFee transactionFeeDTO: authTxnTypeCode={}, transAmount={}, cardNumber={}", 
            transactionFeeDTO.getAuthTxnTypeCode(), 
            transactionFeeDTO.getTransAmount(), 
            transactionFeeDTO.getCardNumber());
        if (StringUtils.equalsAny(authRecordedDTO.getServerType(), ServerTypeEnum.CARD_ACCT_CUST_SERVER.getCode())){
            CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = authorizationCheckProcessingPayload.getCustomerAuthorizationInfoDTO();
            if (ObjectUtils.isNotEmpty(customerAuthorizationInfoDTO)){
                logger.info("Calling sysClassSelfMapper.selectBySysClass: groupType={}", customerAuthorizationInfoDTO.getGroupType());
                ParmSysClass sysClass = sysClassSelfMapper.selectBySysClass(customerAuthorizationInfoDTO.getGroupType());
                logger.info("SysClassSelfMapper call completed: groupType={}", customerAuthorizationInfoDTO.getGroupType());
                String sysInd = "N";
                if (sysClass != null) {
                    sysInd = sysClass.getSysClassStaffInd() == null ? "N" : sysClass.getSysClassStaffInd();
                }
                transactionFeeDTO.setGroupType(sysInd);
            }
        }
        
        if (Objects.equals("C", authTransactionTypeTopCode) && StringUtils.equalsAny(authTransactionTypeDetailCode, "C001", "C002")) {
            ruleTypeList.add(RuleTypeEnum.TRANSACTION_COST_PARAMETER_RULES.getRuleType());
        } else if (Objects.equals("Q", authTransactionTypeTopCode) && StringUtils.equalsAny(authTransactionTypeDetailCode, "Q001", "Q002")) {
            ruleTypeList.add(RuleTypeEnum.TRANSACTION_QUERY_PARAMETER_RULES.getRuleType());
        } else if (Objects.equals("R", authTransactionTypeTopCode)) {
            ruleTypeList.add(RuleTypeEnum.CONSUME_FEE_PARAMETER_RULES.getRuleType());
        }
        ruleTypeList.add(RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType());
        ruleTypeList.add(RuleTypeEnum.DCC_FEE_RULE.getRuleType());

        logger.info("Calling authTransactionFeeService.transFeeCalculateNew with ruleTypeList: {}", ruleTypeList);
        TransactionFeeDTO feeCalculate = authTransactionFeeService.transFeeCalculateNew(ruleTypeList,transactionFeeDTO, authorizationCheckProcessingPayload,authRecordedDTO);
        logger.info("AuthTransactionFeeService call completed: transFeeCalculateNew");
        BigDecimal feeAmount = BigDecimal.ZERO;
        if (null != feeCalculate && StringUtils.isNotEmpty(feeCalculate.getFeeAmount())) {
            feeAmount = new BigDecimal(feeCalculate.getFeeAmount()).stripTrailingZeros();
        }
        logger.info("Additional fees for transactions: {}, AuthGlobalFlowNumber: {}", feeAmount.toPlainString(),authRecordedDTO.getAuthGlobalFlowNumber());
        authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthCardholderBillingAmount().add(feeAmount));
    }

    public void buildTransFee(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AuthRecordedDTO authRecordedDTO) {
        try {
            TransactionFeeDTO transactionFeeDTO = new TransactionFeeDTO();
            transactionFeeDTO.setCardNumber(authRecordedDTO.getAuthCardNumber());
            transactionFeeDTO.setTransAmount(String.valueOf(authRecordedDTO.getAuthCardholderBillingAmount()));
            transactionFeeDTO.setCheckCurrency(authRecordedDTO.getAuthBillingCurrencyCode());
            transactionFeeDTO.setAuthTxnTypeCode(authRecordedDTO.getAuthTransactionTypeTopCode());
            transactionFeeDTO.setAuthTxnDetailCode(authRecordedDTO.getAuthTransactionTypeDetailCode());
            logger.info("Transaction fee calculation parameters: authTxnTypeCode={}, authTxnDetailCode={}, transAmount={}, AuthGlobalFlowNumber: {}", 
                    transactionFeeDTO.getAuthTxnTypeCode(), transactionFeeDTO.getAuthTxnDetailCode(), transactionFeeDTO.getTransAmount(), authRecordedDTO.getAuthGlobalFlowNumber());
            logger.info("Calling authTransactionFeeService.transFeeCalculate");
            TransactionFeeDTO feeCalculate = authTransactionFeeService.transFeeCalculate(transactionFeeDTO, authorizationCheckProcessingPayload);
            logger.info("AuthTransactionFeeService call completed: transFeeCalculate");
            String feeAmount = new BigDecimal(feeCalculate.getFeeAmount()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString();
            String formatAmount = String.format("%08d", Integer.parseInt(feeAmount));
            authRecordedDTO.setTransactionFee("D".concat(formatAmount));
        } catch (Exception e) {
            logger.error("Transaction fee calculation exception",e);
        }
    }

    /**
     * DCI相关 --------------------------------------------------
     */
    public static boolean isSuccess(String responseCode) {
        return Objects.equals(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode(),responseCode);
    }

    /**
     * 校验结果是否成功
     * @param authRecordedDto {@link AuthRecordedDTO}
     * @return true 成功
     */
    public static boolean isSuccess(AuthRecordedDTO authRecordedDto){
        return Objects.equals(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode(),authRecordedDto.getAuthResponseCode());
    }

    public static boolean isFailure(AuthRecordedDTO authRecordedDto){
        return !isSuccess(authRecordedDto);
    }

    public static boolean isFailure(AuthResponseCodeEnum authResponseCodeEnum){
        return !Objects.equals(authResponseCodeEnum.getCode(), AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
    }



    /**
     * mc 相关 ----------------------------------------------
     */
    public static boolean mcIsFailure(AuthRecordedDTO authRecordedDto){
        return !mcIsSuccess(authRecordedDto);
    }

    public static boolean mcIsSuccess(AuthRecordedDTO authRecordedDto){
        return APPROVE_RESPONSE_CODE_MC.contains(authRecordedDto.getAuthResponseCode());
    }


    /**
     * UPI相关 ---------------------------------------------------
     */
    public static boolean upiIsSuccess(AuthRecordedDTO authRecordedDto){
        return Objects.equals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(),authRecordedDto.getAuthResponseCode());
    }

    public static boolean upiIsSuccess(String responseCode ){
        return Objects.equals(AuthResponseCodeEnum.CHECK_RESPONSE.getCode(), responseCode);
    }

    public static boolean UpiIsFailure(AuthRecordedDTO authRecordedDto){
        return !upiIsSuccess(authRecordedDto);
    }

    public static boolean UpiIsFailure(AuthResponseCodeEnum authResponseCodeEnum){
        return !Objects.equals(authResponseCodeEnum.getCode(), AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
    }


    public static boolean visaIsFailure(AuthRecordedDTO authRecordedDto){
        return !visaIsSuccess(authRecordedDto);
    }

    public static boolean visaIsSuccess(AuthRecordedDTO authRecordedDto){
        return VISA_APPROVE_RESPONSE_CODE.contains(authRecordedDto.getAuthResponseCode());
    }

    /**
     * 金融云反欺诈是否是拒绝
     * @param mapDataResponse {@link AnyCloudFraudRespDTO}
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @return true 拒绝
     */
    public static boolean anyCloudFraudReject(DataResponse<Map<String, Object>> mapDataResponse, AuthRecordedDTO authRecordedDTO){
        if (mapDataResponse == null){
            return false;
        }
        final Object strategyResult = mapDataResponse.getData().get("strategy_result");
        logger.warn("Getting financial cloud fraud result: {}", strategyResult);
        if (Objects.equals(AuthConstans.ANYCLOUD_FRAUD_REJECT_RESULT, strategyResult)){
            authRecordedDTO.setErrorDetailCode(AuthResponseCodeEnum.buildErrorDetailCode(
                    AuthResponseCodeEnum.BLOCK_CODE.getCode(), AuthResponseCodeEnum.CHECK_ING.getCode(),
                    String.valueOf(strategyResult)));
            return true;
        }
        return false;
    }

    /**
     *
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param isReturnAprc 是否返回生成的aprc
     */
    public void getArpcValue(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, boolean isReturnAprc) {
        apqcCheck(authorizationCheckProcessingPayload, isReturnAprc);
    }

    public void getArpcValueDiners(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, boolean isReturnAprc) {
        arqcCheckDiners(authorizationCheckProcessingPayload, isReturnAprc);
    }


    /**
     *
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param isReturnAprc 是否返回生成的aprc
     */
    public AuthRecordedDTO apqcCheck(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                     boolean isReturnAprc) {
        final int length = 8;
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        //非银联则不校验, 55号域没有值也不校验
        if ((Objects.equals(AuthTransactionSourceCodeEnum.DCI.getCode(), authRecordedDTO.getAuthTransactionSourceCode())
            || Objects.equals(AuthTransactionSourceCodeEnum.DCS.getCode(), authRecordedDTO.getAuthTransactionSourceCode())
            || Objects.equals(AuthTransactionSourceCodeEnum.DCS_FEP.getCode(), authRecordedDTO.getAuthTransactionSourceCode()))
            && StringUtils.isNotBlank(authRecordedDTO.getAuthSystemRelatedData())){
            String response = arqcCheck(authRecordedDTO);
            logger.warn("ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length
                    && Objects.equals("00",response.substring(length - 2,length))){
                if (isReturnAprc){
                    String arpc = response.substring(length);
                    String check = JposUtils.hexBytesToStr(authRecordedDTO.getAuthResponseCode().getBytes());
                    authRecordedDTO.setAuthSystemRelatedData(authRecordedDTO.getAuthSystemRelatedData()
                            + JposUtils.packageTvlValue(arpc + check,"91"));
                }
            }else {
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE);
            }
        }
        return authRecordedDTO;
    }

    private String arqcCheck(AuthRecordedDTO authRecordedDTO) {
        Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData());
        String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
        String atc = systemRelatedMap.getOrDefault("9F36","");
        String transData = getTransData(systemRelatedMap);
        String arqc = systemRelatedMap.getOrDefault("9F26","");
        if (StringUtils.isEmpty(arqc)) {
            return "";
        }
        String arc = authRecordedDTO.getAuthResponseCode();

        ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArc(arc).withArqc(arqc)
                .withAtc(atc).withPinNo(pinNo).withTransData(transData).build();

//        String f10 = systemRelatedMap.getOrDefault("9F10", "");
//        String encryptionMethod = f10.substring(14, 16);
//        if("01".equals(encryptionMethod)){
            //3DES算法
            logger.info("3des algorithm arqc request: arc={}, arqc={}, atc={}", arqcDTO.getArc(), arqcDTO.getArqc(), arqcDTO.getAtc());
            return authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
//        }else if ("04".equals(encryptionMethod)){
//            //sm4
//            logger.info("sm4 algorithm arqc request message: {}", JSON.toJSONString(arqcDTO));
//            return authEncryptionFeignClient.arqcSm4(arqcDTO).getData();
//        }
        //ignore
//        return "";
    }

    public String getTransData(Map<String, String> systemRelatedMap) {
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        //从第4个字节起取4个字节
        String substring = f10.substring(6, 14);
        return systemRelatedMap.getOrDefault("9F02","") + systemRelatedMap.getOrDefault("9F03","")
                + systemRelatedMap.getOrDefault("9F1A","") + systemRelatedMap.getOrDefault("95","")
                + systemRelatedMap.getOrDefault("5F2A","") + systemRelatedMap.getOrDefault("9A","")
                + systemRelatedMap.getOrDefault("9C","") + systemRelatedMap.getOrDefault("9F37","")
                + systemRelatedMap.getOrDefault("82","") + systemRelatedMap.getOrDefault("9F36","")
                + substring;
    }

    public String getTransDataMc(Map<String, String> systemRelatedMap) {
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        //从第4个字节起取4个字节
        String substring = f10.substring(4, 16);
        return systemRelatedMap.getOrDefault("9F02","") + systemRelatedMap.getOrDefault("9F03","000000000000")
                + systemRelatedMap.getOrDefault("9F1A","") + systemRelatedMap.getOrDefault("95","")
                + systemRelatedMap.getOrDefault("5F2A","") + systemRelatedMap.getOrDefault("9A","")
                + systemRelatedMap.getOrDefault("9C","") + systemRelatedMap.getOrDefault("9F37","")
                + systemRelatedMap.getOrDefault("82","") + systemRelatedMap.getOrDefault("9F36","")
                + substring;
    }

    //-------------------------------UPI------------start---------------------
    /**
     * UPI
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param isReturnArpc 是否返回生成的aprc
     */
    public AuthRecordedDTO upiArqcCheckAndArpcGenerate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                     boolean isReturnArpc) {
        final int length = 8+4;
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        //非银联则不校验, 55号域没有值也不校验
        if (StringUtils.isNotBlank(authRecordedDTO.getAuthSystemRelatedData())){
            String response = arqcCheckUpi(authRecordedDTO);
            logger.warn("UPI ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length
                    && Objects.equals("00",response.substring(length - 2,length))){
                if (isReturnArpc){
                    String arpc = response.substring(length);
                    String check = JposUtils.hexBytesToStr(authRecordedDTO.getAuthResponseCode().getBytes());
                    authRecordedDTO.setAuthSystemRelatedData(authRecordedDTO.getAuthSystemRelatedData()
                            + JposUtils.packageTvlValue(arpc + check,"91"));
                }
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
            }else {
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ARQC_CHECK_FAILURE);
            }
        }
        return authRecordedDTO;
    }

    /**
     * UPI arqc校验
     * @param authRecordedDTO
     * @return
     */
    private String arqcCheckUpi(AuthRecordedDTO authRecordedDTO) {
        Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData());
        String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
        String atc = systemRelatedMap.getOrDefault("9F36","");
        String transData = getTransData(systemRelatedMap);
        String arqc = systemRelatedMap.getOrDefault("9F26","");
        if (StringUtils.isEmpty(arqc)) {
            return "";
        }
        String arc = authRecordedDTO.getAuthResponseCode();
        String arqcMdk = "";
        ParmKeysInfo keysInfo = keysInfoSelfMapper.selectByPrimaryKey(AuthTransactionSourceCodeEnum.UPI.getCode(), AuthKeyIdEnum.MDK.getKeyId(), authRecordedDTO.getOrganizationNumber());
        if (null != keysInfo) {
            arqcMdk = keysInfo.getIkKeyValue();
        }
        ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArc(arc).withArqc(arqc)
                .withAtc(atc).withPinNo(pinNo).withTransData(transData).withArqcMdk(arqcMdk).withSchemeId("9").build();
        //3DES算法
        logger.info("UPI 3des algorithm arqc request: arc={}, arqc={}, atc={}", arqcDTO.getArc(), arqcDTO.getArqc(), arqcDTO.getAtc());
        return authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
    }
    //-------------------------------UPI------------end---------------------


    //-------------------------------MC------------start---------------------
    /**
     * mc
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param isReturnArpc 是否返回生成的arqc
     */
    public AuthRecordedDTO mcArqcCheckAndArpcGenerate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                                      boolean isReturnArpc) {
        final int length = 8+4;
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        //非银联则不校验, 55号域没有值也不校验
        if (StringUtils.isNotBlank(authRecordedDTO.getAuthSystemRelatedData())){
            String response = arqcCheckMc(authRecordedDTO);
            logger.warn("ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length
                    && Objects.equals("00",response.substring(length - 2,length))){
                if (isReturnArpc){
                    String arpc = response.substring(length);
                    String check = JposUtils.hexBytesToStr(authRecordedDTO.getAuthResponseCode().getBytes());
                    authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue(arpc + "0010","91"));
                }
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                authRecordedDTO.setAuthAdditionalTxnData("50C");
            }else {
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.MC_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.MC_ARQC_CHECK_FAILURE.getCode());
                authRecordedDTO.setAuthSystemRelatedData("");
                authRecordedDTO.setAuthAdditionalTxnData("50I");
            }
        }
        return authRecordedDTO;
    }

    /**
     * MC arqc校验
     * @param authRecordedDTO
     * @return
     */
    private String arqcCheckMc(AuthRecordedDTO authRecordedDTO) {
        Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData());
        String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
        String atc = systemRelatedMap.getOrDefault("9F36","");
        String transData = getTransDataMc(systemRelatedMap);
        String arqc = systemRelatedMap.getOrDefault("9F26","");
        if (StringUtils.isEmpty(arqc)) {
            return "";
        }
        String arc = authRecordedDTO.getAuthResponseCode();
        String arqcMdk = "";
        ParmKeysInfo keysInfo = keysInfoSelfMapper.selectByPrimaryKey(AuthTransactionSourceCodeEnum.MASTERCARD.getCode(), AuthKeyIdEnum.MDK.getKeyId(), authRecordedDTO.getOrganizationNumber());
        if (null != keysInfo) {
            arqcMdk = keysInfo.getIkKeyValue();
        }
        ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArc(arc).withArqc(arqc)
                .withAtc(atc).withPinNo(pinNo).withTransData(transData).withArqcMdk(arqcMdk).build();
        arqcDTO.setCsu("0010");
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        if (StringUtils.isNotEmpty(f10) && f10.length() >= 16) {
            if (Objects.equals("10", f10.substring(2,4))) {
                arqcDTO.setSchemeId("5");
                arqcDTO.setUnpredictableNumber(systemRelatedMap.getOrDefault("9F37",""));
            } else if (Objects.equals("14", f10.substring(2,4))) {
                // schemeId 使用默认2
            }
        }
        //3DES算法
        logger.info("MC 3des algorithm arqc request: arc={}, arqc={}, atc={}", arqcDTO.getArc(), arqcDTO.getArqc(), arqcDTO.getAtc());
        return authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
    }
    //-------------------------------MC------------end---------------------

    //-------------------------------Visa------------start---------------------
    /**
     * visa
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @param isReturnArpc 是否返回生成的arqc
     */
    public AuthRecordedDTO visaArqcCheckAndArpcGenerate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                                      boolean isReturnArpc) {
        final int length = 8+4;
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        if (Objects.equals("1", authRecordedDTO.getArqcCheckResultCode())) {
            authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
            if (StringUtils.isEmpty(authRecordedDTO.getAuthSystemRelatedData())) {
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.INVALID_TRANSACTION);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.INVALID_TRANSACTION.getCode());
            } else {
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.CARD_GROUP_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CARD_GROUP_ARQC_CHECK_FAILURE.getCode());
            }
            return authRecordedDTO;
        }
        //55号域没有值也不校验
        if (StringUtils.isNotBlank(authRecordedDTO.getAuthSystemRelatedData())){
            logger.info("authGlobalFlowNumber {}, Visa ARQC check start",authRecordedDTO.getAuthGlobalFlowNumber());
            Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData().substring(6));
            if (null == systemRelatedMap || systemRelatedMap.isEmpty()) {
                return authRecordedDTO;
            }
            String csu = "00820000";
            String response = arqcCheckVisa(authRecordedDTO, systemRelatedMap, csu);
            logger.warn("Visa ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length
                    && Objects.equals("00",response.substring(length - 2,length))) {
                if (isReturnArpc) {
                    String datasetId = authRecordedDTO.getAuthSystemRelatedData().substring(0, 2);
                    String atc = systemRelatedMap.getOrDefault("9F36","");
                    String atcNew = JposUtils.packageTvlValue(atc,"9F36");
                    String arpc = response.substring(length);
                    String tag91 = "";
                    if (!Objects.equals("07", authRecordedDTO.getAuthServicePointCardCode())) {
                        String f10 = systemRelatedMap.getOrDefault("9F10", "");
                        if (StringUtils.isNotEmpty(f10) && f10.length() >= 6) {
                            if (Objects.equals("06", f10.substring(0, 2)) && Objects.equals("01", f10.substring(2, 4)) && Objects.equals("12", f10.substring(4, 6))) {
                                arpc = response.substring(length, length+8) + csu;
                            }
                        }
                        tag91 = JposUtils.packageTvlValue(arpc,"91");
                    }
                    authRecordedDTO.setAuthSystemRelatedData(datasetId + StringUtils.leftPad(Integer.toHexString((tag91.length()+atcNew.length())/2).toUpperCase(), 4, "0") + tag91+atcNew);
                }
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                authRecordedDTO.setAuthAdditionalTxnData("");
            } else {
                logger.warn("authGlobalFlowNumber {}, Visa ARQC check error", authRecordedDTO.getAuthGlobalFlowNumber());
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.VISA_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.VISA_ARQC_CHECK_FAILURE.getCode());
            }
        }
        return authRecordedDTO;
    }

    /**
     * Visa arqc校验
     * @param authRecordedDTO
     * @return
     */
    private String arqcCheckVisa(AuthRecordedDTO authRecordedDTO, Map<String, String> systemRelatedMap, String csu) {
        String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
        String atc = systemRelatedMap.getOrDefault("9F36","");
        String transData = getTransDataVisa(systemRelatedMap);
        String arqc = systemRelatedMap.getOrDefault("9F26","");
        String arqcMdk = "";
        ParmKeysInfo keysInfo = keysInfoSelfMapper.selectByPrimaryKey(AuthTransactionSourceCodeEnum.VISA.getCode(), AuthKeyIdEnum.MDK.getKeyId(), authRecordedDTO.getOrganizationNumber());
        if (null != keysInfo) {
            arqcMdk = keysInfo.getIkKeyValue();
        }
        if (StringUtils.isEmpty(arqc) || StringUtils.isEmpty(arqcMdk) || StringUtils.isEmpty(atc)) {
            logger.warn("authGlobalFlowNumber {}, Visa ARQC isEmpty",authRecordedDTO.getAuthGlobalFlowNumber());
            return "";
        }
        ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArqc(arqc)
                .withAtc(atc).withPinNo(pinNo).withTransData(transData).withArqcMdk(arqcMdk).build();
        arqcDTO.setCsu(csu);
        arqcDTO.setTransactionSourceCode(AuthTransactionSourceCodeEnum.VISA.getCode());
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        if (StringUtils.isNotEmpty(f10) && f10.length() >= 16) {
            if (Objects.equals("06", f10.substring(0, 2)) && StringUtils.equalsAny(f10.substring(2,4),"01", "02")) {
                arqcDTO.setSchemeId("5");
            } else {
                // schemeId 使用默认2
            }
        }
        //3DES算法
        logger.info("Visa 3des algorithm arqc request: arqc={}, atc={}, transactionSourceCode={}", arqcDTO.getArqc(), arqcDTO.getAtc(), arqcDTO.getTransactionSourceCode());
        return authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
    }

    public String getTransDataVisa(Map<String, String> systemRelatedMap) {
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        String substring = "";
        if (StringUtils.isNotEmpty(f10) && f10.length() >= 6) {
            if (Objects.equals("06", f10.substring(0, 2)) && StringUtils.equalsAny(f10.substring(2, 4),"01", "02") && Objects.equals("12", f10.substring(4, 6))) {
                substring = f10.substring(0, 14);
            } else if (Objects.equals("1F", f10.substring(0, 2)) && Objects.equals("22", f10.substring(2, 4)) && StringUtils.equalsAny(f10.substring(4, 6),"01", "02")) {
                substring = f10.substring(0, 62);
            }
        }
        return systemRelatedMap.getOrDefault("9F02","") + systemRelatedMap.getOrDefault("9F03","000000000000")
                + systemRelatedMap.getOrDefault("9F1A","") + systemRelatedMap.getOrDefault("95","")
                + systemRelatedMap.getOrDefault("5F2A","") + systemRelatedMap.getOrDefault("9A","")
                + systemRelatedMap.getOrDefault("9C","") + systemRelatedMap.getOrDefault("9F37","")
                + systemRelatedMap.getOrDefault("82","") + systemRelatedMap.getOrDefault("9F36","")
                + substring;
    }

    /**
     * 生成arpc  visa
     * @param authRecordedDTO
     * @param isReturnArpc
     * @param csuOther
     * @return
     */
    public AuthRecordedDTO visaArqcGenerate(AuthRecordedDTO authRecordedDTO, boolean isReturnArpc, String csuOther) {
        final int length = 8+4;
        //55号域没有值也不校验
        if (StringUtils.isNotBlank(authRecordedDTO.getAuthIccSystemRelatedData())){
            logger.info("authGlobalFlowNumber {}, Visa ARQC check start",authRecordedDTO.getAuthGlobalFlowNumber());
            Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthIccSystemRelatedData().substring(6));
            if (null == systemRelatedMap || systemRelatedMap.isEmpty()) {
                return authRecordedDTO;
            }
            String csu = "00820000";
            if (StringUtils.isNotEmpty(csuOther)) {
                csu = csuOther;
            }
            String response = arqcCheckVisa(authRecordedDTO, systemRelatedMap, csu);
            logger.warn("Visa ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length
                    && Objects.equals("00",response.substring(length - 2,length))) {
                if (isReturnArpc) {
                    String datasetId = authRecordedDTO.getAuthSystemRelatedData().substring(0, 2);
                    String atc = systemRelatedMap.getOrDefault("9F36","");
                    String atcNew = JposUtils.packageTvlValue(atc,"9F36");
                    String arpc = response.substring(length);
                    String tag91 = "";
                    if (!Objects.equals("07", authRecordedDTO.getAuthServicePointCardCode())) {
                        String f10 = systemRelatedMap.getOrDefault("9F10", "");
                        if (StringUtils.isNotEmpty(f10) && f10.length() >= 6) {
                            if (Objects.equals("06", f10.substring(0, 2)) && Objects.equals("01", f10.substring(2, 4)) && Objects.equals("12", f10.substring(4, 6))) {
                                arpc = response.substring(length, length+8) + csu;
                            }
                        }
                        tag91 = JposUtils.packageTvlValue(arpc,"91");
                    }
                    authRecordedDTO.setAuthSystemRelatedData(datasetId + StringUtils.leftPad(Integer.toHexString((tag91.length()+atcNew.length())/2).toUpperCase(), 4, "0") + tag91+atcNew);
                }
            } else {
                logger.warn("authGlobalFlowNumber {}, Visa ARQC check error", authRecordedDTO.getAuthGlobalFlowNumber());
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.VISA_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.VISA_ARQC_CHECK_FAILURE.getCode());
            }
        }
        return authRecordedDTO;
    }

    //-------------------------------Visa------------end---------------------

    @Async
    public Future<DataResponse<Map<String, Object>>> checkAnyCloudFraud(Serializable reqData, String organizationNumber, AuthRecordedDTO authRecordedDTO){
        if (!containsCloudFraudCheckItem(organizationNumber,authRecordedDTO)){
            return new AsyncResult<>(null);
        }
        AnyCloudFraudDTO anyCloudFraudDTO = AnyCloudFraudDTO.build(reqData);
        anyCloudFraudDTO.setTenantId(organizationNumber);
        anyCloudFraudDTO.setUuid(UUID.randomUUID().toString());
        anyCloudFraudDTO.setStgKey(actKey);
        logger.info("Asynchronous thread calls fraud check");
        DataResponse<Map<String, Object>> mapDataResponse = anyCloudFraudFeignClient.cloudFraud(anyCloudFraudDTO);
        logger.info("Call financial cloud fraud interface response parameters: {}",mapDataResponse.getData());
        return new AsyncResult<>(mapDataResponse);
    }

    public boolean containsCloudFraudCheckItem(String organizationNumber, AuthRecordedDTO authRecordedDTO){
        Map<String, String> authCheckRule = ruleTransfer.getAuthCheckRule(organizationNumber,
                authRecordedDTO.getAuthTransactionTypeTopCode(), authRecordedDTO.getAuthTransactionTypeDetailCode());
        if (MapUtils.isEmpty(authCheckRule)){
            return false;
        }
        String tableId = authCheckRule.get(AuthConstans.TABLE_ID);
        logger.info("Calling authCheckControlService.findByOrgAndTableId: organizationNumber={}, tableId={}", organizationNumber, tableId);
        List<AuthCheckControlResDTO> authCheckControlResList = authCheckControlService.findByOrgAndTableId(organizationNumber, tableId);
        logger.info("AuthCheckControlService call completed: findByOrgAndTableId");
        return authCheckControlResList.stream()
                .filter(t -> Objects.equals(t.getCheckItem(), AuthCheckItemEnum.ANY_CLOUD_FRAUD.getCheckItem()))
                .findFirst()
                .orElse(null) != null;

    }

    // -----------------diners----------------
    public AuthRecordedDTO arqcCheckDiners(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                     boolean isReturnArpc) {
        final int length = 12;
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        String mti = authRecordedDTO.getAuthMessageTypeId();
        String f22 = authRecordedDTO.getAuthServicePointEntryModeCode();
        Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData());
        String f22_01 = "";
        String f22_07 = "";
        if (StringUtils.isNotBlank(f22)) {
            f22_01 = f22.substring(0,1);
            f22_07 = f22.substring(6,7);
        }
        boolean f22Bol = ((Objects.equals(f22_01, "5") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "5"))
                || ((Objects.equals(f22_01, "8") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "S"));
        if (Objects.equals(mti, DciMTIEnum.DCI_AUTH_REQUEST.getCode()) && f22Bol && !systemRelatedMap.isEmpty()) {
            String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                    + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
            String atc = systemRelatedMap.getOrDefault("9F36","");
            String arqc = systemRelatedMap.getOrDefault("9F26","");
            String arc = "00"; //authRecordedDTO.getAuthResponseCode();
            String transData = getTransDataDiners(systemRelatedMap, f22_01, f22_07);
            ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArc(arc).withArqc(arqc)
                    .withAtc(atc).withPinNo(pinNo).withTransData(transData).build();
            arqcDTO.setCsu("0010");
            //3DES算法(目前暂时只支持该算法，其他还有AES)
            logger.info("DC 3des algorithm arqc request: arc={}, arqc={}, atc={}", arqcDTO.getArc(), arqcDTO.getArqc(), arqcDTO.getAtc());
            logger.info("Calling authEncryptionFeignClient.arqc3Des for DC");
            String response = authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
            logger.info("AuthEncryptionFeignClient call completed: arqc3Des for DC");
            logger.warn("DC ARQC check response:{}", response);
//            String ptc = "A0";
//            String f10 = systemRelatedMap.getOrDefault("9F10", "");
//            if (StringUtils.isNotEmpty(f10)) {
//                ptc = f10.substring(8,10);
//            } else {
//                ptc = "00";
//            }
            String cvrc = "02";
            if (StringUtils.isNotBlank(response) && response.length() >= length){
                String arpc = response.substring(length);
                if (Objects.equals("00",response.substring(length - 2, length))) {
                    if (isReturnArpc){
                        authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue(arpc+"0010","91"));
                    }
                    authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                    cvrc = "01";
                } else {
                    authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                    authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE.getCode());
                    authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE);
                    authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue(arpc+"0000","91"));
                }
            }else {
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE.getCode());
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue("00000000000000000000","91"));
            }
            // 更新arpc验证结果到DE44
//            StringBuilder repF44 = new StringBuilder();
//            String f441 = authRecordedDTO.getAuthAdditionalResponseData();
//            StringBuilder f44 = new StringBuilder().append(f441);
//            if (StringUtils.isEmpty(f441) || Objects.equals(f441, "null")) {
//                repF44.append("                  ").append(cvrc);
//            } else {
//                if (f44.length() >= 20) {
//                    repF44.append(f44.replace(18,20, cvrc));
//                } else if (f44.length() <= 18) {
//                    repF44.append(f44).append(StringUtils.leftPad(cvrc, 20 - f44.length(), " "));
//                } else {
//                    repF44.append(f44.substring(0,18)).append(cvrc);
//                }
//            }
//            authRecordedDTO.setAuthAdditionalResponseData(repF44.toString());
        }
        return authRecordedDTO;
    }

    public AuthRecordedDTO arqcCheckAndGenDiners(AuthRecordedDTO authRecordedDTO, boolean isReturnArpc, String csu) {
        final int length = 12;
        String mti = authRecordedDTO.getAuthMessageTypeId();
        String f22 = authRecordedDTO.getAuthServicePointEntryModeCode();
        Map<String, String> systemRelatedMap = JposUtils.parseTvlValue(authRecordedDTO.getAuthSystemRelatedData());
        String f22_01 = "";
        String f22_07 = "";
        if (StringUtils.isNotBlank(f22)) {
            f22_01 = f22.substring(0,1);
            f22_07 = f22.substring(6,7);
        }
        boolean f22Bol = ((Objects.equals(f22_01, "5") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "5"))
                || ((Objects.equals(f22_01, "8") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "S"));
        if (Objects.equals(mti, DciMTIEnum.DCI_AUTH_REQUEST.getCode()) && f22Bol && !systemRelatedMap.isEmpty()) {
            String pinNo = authRecordedDTO.getAuthCardNumber().substring(authRecordedDTO.getAuthCardNumber().length() - 14)
                    + authRecordedDTO.getAuthCardSequenceNumber().substring(authRecordedDTO.getAuthCardSequenceNumber().length() - 2);
            String atc = systemRelatedMap.getOrDefault("9F36","");
            String arqc = systemRelatedMap.getOrDefault("9F26","");
            String arc = "00";
            String transData = getTransDataDiners(systemRelatedMap, f22_01, f22_07);
            ArqcDTO arqcDTO = ArqcDTOBuilder.anArqcDTO().withArc(arc).withArqc(arqc)
                    .withAtc(atc).withPinNo(pinNo).withTransData(transData).build();
            if (isReturnArpc && StringUtils.isNotEmpty(csu)) {
                arqcDTO.setCsu(csu);
            }
            //3DES算法(目前暂时只支持该算法，其他还有AES)
            logger.info("DC 3des algorithm arqc request: arc={}, arqc={}, atc={}", arqcDTO.getArc(), arqcDTO.getArqc(), arqcDTO.getAtc());
            String response = authEncryptionFeignClient.arqc3Des(arqcDTO).getData();
            logger.warn("DC ARQC check response:{}", response);
            if (StringUtils.isNotBlank(response) && response.length() >= length){
                String arpc = response.substring(length);
                if (Objects.equals("00",response.substring(length - 2, length))) {
                    if (isReturnArpc){
                        authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                        authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue(arpc + csu,"91"));
                    }
                    authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                } else {
                    authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                    authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE.getCode());
                    authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE);
                    authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue(arpc + csu,"91"));
                }
            }else {
                authRecordedDTO.setArqcCheckResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE.getCode());
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.DCI_ARQC_CHECK_FAILURE);
                authRecordedDTO.setAuthSystemRelatedData(JposUtils.packageTvlValue("00000000000000000000","91"));
            }
        }
        return authRecordedDTO;
    }


    public String getTransDataDiners(Map<String, String> systemRelatedMap, String f22_01, String f22_07) {
        String f10 = systemRelatedMap.getOrDefault("9F10", "");
        String cvn = f10.substring(2, 4);
        StringBuilder sb = new StringBuilder();
        // contact
        if ((Objects.equals(f22_01, "5") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "5")) {
            sb.append(systemRelatedMap.getOrDefault("9F02","")).append(systemRelatedMap.getOrDefault("9F03","000000000000"))
              .append(systemRelatedMap.getOrDefault("9F1A","")).append(systemRelatedMap.getOrDefault("95",""))
              .append(systemRelatedMap.getOrDefault("5F2A","")).append(systemRelatedMap.getOrDefault("9A",""))
              .append(systemRelatedMap.getOrDefault("9C","")).append(systemRelatedMap.getOrDefault("9F37",""))
              .append(systemRelatedMap.getOrDefault("82","") + systemRelatedMap.getOrDefault("9F36",""));
            if (Objects.equals(cvn, "05")) {
                sb.append(f10, 0, 16);
            } else if (Objects.equals(cvn, "06")) {
                sb.append(f10, 4, 16);
            }
        } else if ((Objects.equals(f22_01, "8") || Objects.equals(f22_01, "9")) && Objects.equals(f22_07, "S")) {
            // contactless
            sb.append(systemRelatedMap.getOrDefault("9F02","")).append(systemRelatedMap.getOrDefault("5F2A",""))
                    .append(systemRelatedMap.getOrDefault("9F37","")).append(systemRelatedMap.getOrDefault("9F36",""));
            if (Objects.equals(cvn, "15")) {
                sb.append(f10);
            } else if (Objects.equals(cvn, "16")) {
                sb.append(f10, 4, 20);
            }
        }
//        sb.append("80");
//        int len = 16 - sb.length() % 16;
//        log.info("sb:{}, Right fill zero len:{}", sb, len);
//        StringBuilder sbd = new StringBuilder();
//        for (int i = 0; i < len; i++) {
//            sbd.append(0);
//        }
        return sb.toString();
    }



    public static Boolean isDoubleInformation(AuthRecordedDTO authRecordedDTO){
        return StringUtils.isBlank(authRecordedDTO.getPostingTransactionCodeRev()) ||
                String.valueOf(AuthConstans.ZERO).equals(authRecordedDTO.getPostingTransactionCodeRev());
    }



}
