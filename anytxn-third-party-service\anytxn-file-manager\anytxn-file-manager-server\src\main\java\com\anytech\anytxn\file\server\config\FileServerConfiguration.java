package com.anytech.anytxn.file.server.config;

import com.anytech.anytxn.common.core.handler.GlobalExceptionHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * @description: 文件服务配置类
 * @author: z<PERSON><PERSON>
 * @create: 2021-03-18
 **/
@Configuration
@Import({GlobalExceptionHandler.class})
public class FileServerConfiguration {
}
