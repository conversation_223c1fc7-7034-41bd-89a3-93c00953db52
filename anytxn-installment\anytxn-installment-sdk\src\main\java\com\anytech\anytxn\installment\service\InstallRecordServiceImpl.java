package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallRecordSearchKeyDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentOrderStatusEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallNormalAccountingService;
import com.anytech.anytxn.installment.base.service.IInstallOrderEarlierSettleService;
import com.anytech.anytxn.installment.base.service.IInstallOrderTerminatedService;
import com.anytech.anytxn.installment.base.service.IInstallRecordService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallRecordMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallRecordSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallRecord;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallRecordDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-24 15:10
 **/
@Service
public class InstallRecordServiceImpl implements IInstallRecordService {
    private static final Logger logger = LoggerFactory.getLogger(InstallRecordServiceImpl.class);

    @Autowired
    private IInstallOrderEarlierSettleService installOrderEarlierSettleService;
    @Autowired
    private IInstallNormalAccountingService installNormalAccountingService;
    @Autowired
    private IInstallOrderTerminatedService installOrderTerminatedService;
    @Autowired
    private InstallRecordMapper installRecordMapper;
    @Autowired
    private InstallRecordSelfMapper installRecordSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    /**
     *  新建分期流水表
     * @date 2019/6/24 15:06
     * @param installRecordDTO {@link InstallRecordDTO}
     * @return int
     **/
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public int add(InstallRecordDTO installRecordDTO) {
        if (installRecordDTO == null){
            logger.error("Installment record parameter is null: installRecordDTO={}", installRecordDTO);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ST_FL);
        }
        try {
            // 批次缓存
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getInstallBO().insertInstallRecord(installRecordDTO);
                return 1;
            } else {
                InstallRecord installRecord = BeanMapping.copy(installRecordDTO, InstallRecord.class);
                int result = installRecordMapper.insertSelective(installRecord);
                if (result <= 0){
                    logger.error("Failed to create installment record: result={}", result);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
                }
                return result;
            }
        }catch (Exception exce){
            logger.error("Exception occurred while creating installment record", exce);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
        }
    }

    /**
     * 分期订单批量处理Writer
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchInstallOrderWriter(List<? extends InstallOrderDTO> list) {
        for (InstallOrderDTO installOrderDTO : list) {

            logger.info("installment order id is {}. installment amount is {}, total term is {}",
                    installOrderDTO.getOrderId(),installOrderDTO.getInstallmentAmount(), installOrderDTO.getTerm());



            logger.info("Calling organizationInfoService.findOrganizationInfo with organizationNumber={}", installOrderDTO.getOrganizationNumber());
            OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
            if(organizationInfo != null){
                LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
                LocalDate lastProcessingDay = organizationInfo.getLastProcessingDay();
                if(Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), installOrderDTO.getStatus())){
                    //调用订单被动提前结清处理
                    logger.info("Calling installOrderEarlierSettleService.passiveEarlierSettlement with orderId={}", installOrderDTO.getOrderId());
                    String repaymentFlag = installOrderEarlierSettleService.passiveEarlierSettlement(installOrderDTO);
                    if(InstallmentConstant.PASSIVE_EARY_SETTLE_YES.equals(repaymentFlag)){
                        //2）	如果被动提前结清已经执行，则调用分期订单终止处理
                        //InstallOrder installOrder = new InstallOrder()
                        //BeanMapping.copy(installOrderDTO,installOrder)
                        logger.info("Calling installOrderTerminatedService.installmentOrderTerminated with functionCode=3, orderId={}", installOrderDTO.getOrderId());
                        int i = installOrderTerminatedService.installmentOrderTerminated("3",installOrderDTO);
                        if(i<1){
                            logger.error("Batch call to installment order termination processing interface failed");
                            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESS_INST_ORDER_TRMT_FAULT);
                        }
                    }else if(InstallmentConstant.PASSIVE_EARY_SETTLE_NO.equals(repaymentFlag)){
                        if(!"Y".equals(installOrderDTO.getType())){
                            //3）	如果过被动提前结清不需要执行,调用分期正常下账处理
                            logger.info("Calling installNormalAccountingService.installNormalAccounting with orderId={}", installOrderDTO.getOrderId());
                            Boolean resultFlag = installNormalAccountingService.installNormalAccounting(installOrderDTO);
                            if(!resultFlag){
                                logger.error("Batch call to normal accounting failed");
                                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESSINST_NORMAL_ACCT_FAULT);
                            }
                        }else {
                            boolean normalRecord = true;
                            //分期类型为Y-轻松财务的订单，并且订单未结束的记录，读取对应的管理账户记录，判断当天是否为还款日，如果当天为还款日，则判断账单剩余未还款金额是否为0，如果不是，调用订单提前终止处理，将此笔订单提前结清
                            if(!Objects.equals(InstallmentOrderStatusEnum.NORMALEND_STATUS.getCode(), installOrderDTO.getStatus())){
                                List<AccountStatementInfo> lastedStatementInfoList = accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(installOrderDTO.getAccountManagementId());
                                if (!lastedStatementInfoList.isEmpty()) {
                                    //获取账单日最大的一条记录
                                    AccountStatementInfo accountStatementInfo = lastedStatementInfoList.get(0);
                                    //还款日
                                    LocalDate paymentDueDate = accountStatementInfo.getPaymentDueDate();
                                    AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.getByOrgNumAndMid(installOrderDTO.getOrganizationNumber(), installOrderDTO.getAccountManagementId());
                                    if(nextProcessingDay.compareTo(paymentDueDate)==0
                                            && accountManagementInfo.getStatementDueAmount().compareTo(BigDecimal.ZERO)>0){
                                        normalRecord = false;
                                    }
                                }
                            }
                            if(normalRecord){
                                //3）	如果过被动提前结清不需要执行,调用分期正常下账处理
                                logger.info("Calling installNormalAccountingService.installNormalAccounting with orderId={}", installOrderDTO.getOrderId());
                                Boolean resultFlag = installNormalAccountingService.installNormalAccounting(installOrderDTO);
                                if(!resultFlag){
                                    logger.error("Batch call to normal accounting failed");
                                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESSINST_NORMAL_ACCT_FAULT);
                                }
                            }else {
                                installOrderDTO.setStatus(InstallmentOrderStatusEnum.PASSIVE_ADVANCESETTLEMENT_STATUS.getCode());
                                logger.info("Calling installOrderTerminatedService.installmentOrderTerminated with functionCode=3, orderId={}", installOrderDTO.getOrderId());
                                int i = installOrderTerminatedService.installmentOrderTerminated("3",installOrderDTO);
                                if(i<1){
                                    logger.error("Batch call to installment order termination processing interface failed");
                                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESS_INST_ORDER_TRMT_FAULT);
                                }
                            }


                        }

                    }
                }else {
                    //订单状态变更日期小于等于系统下一处理日大于系统上一处理日的订单
                    boolean flag = (installOrderDTO.getStatusUpdateDate().isBefore(nextProcessingDay)
                            || installOrderDTO.getStatusUpdateDate().isEqual(nextProcessingDay))
                            && installOrderDTO.getStatusUpdateDate().isAfter(lastProcessingDay);
                    int notBillNum = installPlanSelfMapper.selectCountNotBill(installOrderDTO.getOrderId());
                    if(flag && notBillNum>0){
                        //1）	如果状态为其他，则调用分期订单终止处理。
                        InstallOrder installOrder = new InstallOrder();
                        BeanMapping.copy(installOrderDTO,installOrder);
                        String functionCode=null;
                        InstallmentOrderStatusEnum statusEnum = InstallmentOrderStatusEnum.getEnum(installOrderDTO.getStatus());
                        switch (statusEnum){
                            case REFUNDS_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.RETURNGOODS.getCode();
                                break;
                            case PASSIVE_ADVANCESETTLEMENT_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode();
                                break;
                            case ACTIVE_ADVANCESETTLEMENT_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode();
                                break;
                            case REFUSEPAYMENT_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode();
                                break;
                            case ADVANCE_SHORT_TERM_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.SIX.getCode();
                                break;
                            case ADVANCE_UNCHANGE_TERM_STATUS:
                                functionCode= InstallmentFunctionCodeEnum.SENVEN.getCode();
                                break;
                            case PART_CANCLE:
                                functionCode= InstallmentFunctionCodeEnum.PART_CANCEL.getCode();
                                break;
                            default:
                                break;
                        }
                        logger.info("Calling installOrderTerminatedService.installmentOrderTerminated with functionCode={}, orderId={}", functionCode, installOrderDTO.getOrderId());
                        int i = installOrderTerminatedService.installmentOrderTerminated(functionCode,installOrderDTO);
                        if(i<1){
                            logger.error("Batch call to installment order termination processing failed");
                            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BATCH_PROCESS_INST_ORDER_TRMT_FAULT);
                        }
                    }
                }
            }
        }
    }


    /**
     * 根据Id查询分期流水
     */
    @Override
    public InstallRecordDTO getInstallRecordById(String id) {
        InstallRecord installRecord = installRecordMapper.selectByPrimaryKey(id);
        if (installRecord == null) {
            logger.error("Installment record not found for id={}", id);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_RECORD_NOT_EXIST_FAULT);
        }
        return BeanMapping.copy(installRecord, InstallRecordDTO.class);
    }
    /**
     * 分页查询分期流水
     */
    @Override
    public PageResultDTO<InstallRecordDTO> searchPageByKey(InstallRecordSearchKeyDTO installRecordSearchKeyDTO) {
        Page<InstallRecordDTO> pageHelper = PageHelper.startPage(installRecordSearchKeyDTO.getPage(),
                installRecordSearchKeyDTO.getRows());



        Map<String, Object> searchKeyMap = new HashMap<>(2);
        searchKeyMap.put("cardNumber", installRecordSearchKeyDTO.getCardNumber());
        searchKeyMap.put("transactionDate", installRecordSearchKeyDTO.getTransactionDate());
        searchKeyMap.put("transactionInd",installRecordSearchKeyDTO.getTransactionInd());


        List<InstallRecord> installRecords = installRecordSelfMapper.searchBySearchKey(searchKeyMap);
        List<InstallRecordDTO> installRecordDtos = BeanMapping.copyList(installRecords, InstallRecordDTO.class);
        return new PageResultDTO<>(installRecordSearchKeyDTO.getPage(), installRecordSearchKeyDTO.getRows(), pageHelper.getTotal(), pageHelper.getPages(),
                installRecordDtos);
    }

    @Override
    public int getCount(String partitionKey) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installRecordSelfMapper.getCount(partitionKey0, partitionKey1);
    }

    @Override
    public List<String> queryOrderIds(String partitionKey, List<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return installRecordSelfMapper.queryOrderIds(partitionKey0, partitionKey1, rowNumbers);
    }
}
