package com.anytech.anytxn.installment.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.installment.IAuthInstallmentHandler;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.base.authorization.enums.DebitCreditIndcatorEnum;
import com.anytech.anytxn.business.base.transaction.domain.bo.RecordedBO;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallBookPretreatService;
import com.anytech.anytxn.installment.base.service.IInstallOrderProcessService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import com.anytech.anytxn.transaction.base.service.ITxnRecordedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 授权分期处理，在授权检查位置创建分期订单
 *
 * <AUTHOR>
 * @version 3.0
 * @date 2020/5/7
 */
@Service
public class AuthInstallmentHandler implements IAuthInstallmentHandler {

    private static final Logger logger = LoggerFactory.getLogger(AuthInstallmentHandler.class);


    @Autowired
    private IInstallBookPretreatService iInstallBookPretreatService;

    @Autowired
    private ITxnRecordedService txnRecordedService;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallPlanMapper installPlanMapper;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private IInstallOrderProcessService installOrderProcessService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private IStatementProcessService statementProcessService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private IInstallOrderService installOrderService;

    public static final List<String> INSTALL_TRANSACTION_CODE = Arrays.asList("I","L","M","D","K","E","H");

    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Resource
    private IGlAmsService glAmsServiceImpl;

    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;


    @BatchSharedAnnotation
    @Override
    public InstallTrialResDTO handle(AuthorizationCheckProcessingPayload authorizationCheckProcessing) {
        OutstandingTransactionDTO outstandingTransactionDTO = authorizationCheckProcessing.getOutstandingTransactionDTO();
        outstandingTransactionDTO.setInstallAccountManagerId(authorizationCheckProcessing.getAuthRecordedDTO().getInstallAccountManagerId());
        outstandingTransactionDTO.setCustomerRegion(authorizationCheckProcessing.getAuthRecordedDTO().getCustomerRegion());
        outstandingTransactionDTO.setMcc(authorizationCheckProcessing.getAuthRecordedDTO().getAuthMerchantType());
        outstandingTransactionDTO.setMerchantId(authorizationCheckProcessing.getAuthRecordedDTO().getMerchantId());
        outstandingTransactionDTO.setMerchantName(authorizationCheckProcessing.getAuthRecordedDTO().getAuthCardAcceptorNameLocation());
        outstandingTransactionDTO.setTransactionDescription(authorizationCheckProcessing.getAuthRecordedDTO().getTransactionDesc());
        outstandingTransactionDTO.setTransactionDesc(authorizationCheckProcessing.getAuthRecordedDTO().getTransactionDesc());
        outstandingTransactionDTO.setSettlementAmount(outstandingTransactionDTO.getBillingAmount());
        outstandingTransactionDTO.setSettlementCurrencyCode(outstandingTransactionDTO.getBillingCurrencyCode());
        outstandingTransactionDTO.setInstallmentTotalInterest(authorizationCheckProcessing.getAuthRecordedDTO().getInstallmentTotalInterest());



        OrganizationInfoResDTO organizationInfoResDTO = authorizationCheckProcessing.getOrgInfo();
        CustReconciliationControlDTO custReconciliationDTO = authorizationCheckProcessing.getCustReconciliationDTO();

        logger.info("Authorization returned outstanding transaction info: transactionAmount={}, installmentType={}, productCode={}",
                outstandingTransactionDTO.getTransactionAmount(), outstandingTransactionDTO.getInstallmentType(), outstandingTransactionDTO.getInstalmentProduct());
        //分期入账
        final boolean installCheck = INSTALL_TRANSACTION_CODE.contains(outstandingTransactionDTO.getAuthTransactionTypeTop());
        if (installCheck) {
            logger.info("Calling iInstallBookPretreatService.installBookPretreat with transactionAmount={}", outstandingTransactionDTO.getTransactionAmount());
            String orderId = iInstallBookPretreatService.installBookPretreat(outstandingTransactionDTO,custReconciliationDTO);
            outstandingTransactionDTO.setInstallmentIndicator("1");
            outstandingTransactionDTO.setInstallmentOrderId(orderId);
            logger.info("iInstallBookPretreatService.installBookPretreat completed: orderId={}", orderId);

            LocalDate recordDate = organizationInfoResDTO.getNextProcessingDay();
            if(Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), outstandingTransactionDTO.getInstallmentType())){
                recordDate = organizationInfoResDTO.getToday();
                outstandingTransactionDTO.setTransactionDate(recordDate);
            }

            InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);

            InstallOrderDTO copy = BeanMapping.copy(installOrder, InstallOrderDTO.class);
            copy.setInstallmentTotalInterest(outstandingTransactionDTO.getInstallmentTotalInterest());
            // 检查缓存
            if (CustAccountBO.isBatch()) {
                copy = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallOrder(copy, x-> x.getOrderId().equals(orderId));

                if (copy != null) {
                    installOrder = BeanMapping.copy(copy, InstallOrder.class);
                }
            }


            //如果是分期退货交易不需要入账，走批量入账
            logger.info("Calling transactionCodeService.findTransactionCode with organizationNumber={}, postingTransactionCode={}",
                    outstandingTransactionDTO.getOrganizationNumber(), outstandingTransactionDTO.getPostingTransactionCode());
            TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(outstandingTransactionDTO.getOrganizationNumber(), outstandingTransactionDTO.getPostingTransactionCode());
            logger.info("transactionCodeService.findTransactionCode completed: debitCreditIndicator={}", transactionCode.getDebitCreditIndicator());
            if(Objects.equals(transactionCode.getDebitCreditIndicator(), DebitCreditIndcatorEnum.CREDIT.getCode())){
                return buildTrialData(installOrder);
            }
            //分期整笔不记录交易流水
            outstandingTransactionDTO.setInstallmentPostTransRecord(Boolean.FALSE);
            logger.info("Calling txnRecordedService.firmRealTimeTxnRecorded with recordDate={}", recordDate);
            RecordedBO recorded = txnRecordedService.firmRealTimeTxnRecorded(outstandingTransactionDTO, recordDate, false, custReconciliationDTO);
            logger.info("txnRecordedService.firmRealTimeTxnRecorded completed");
            if (recorded == null){
                logger.error("Transaction recording failed - recorded is null");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.S_TRANS_REJECT);
            }



            boolean isUpdate = (Objects.equals("RD", installOrder.getType()) || Objects.equals("LD", installOrder.getType())) && StringUtils.isNotEmpty(installOrder.getAccountManagementId());
            if (!(Objects.equals( InstallmentTypeEnum.SINGLE_INSTALL.getCode(), installOrder.getType())
                    ||  Objects.equals( InstallmentTypeEnum.STATEMENT_INSTALL.getCode(), installOrder.getType())
                    ||  Objects.equals( InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrder.getType())
                    ||  Objects.equals( InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), installOrder.getType())
                    ||  Objects.equals( InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(), installOrder.getType())
                    ||  isUpdate)){
                installOrder.setOrderId(orderId);
                installOrder.setAccountManagementId(recorded.getTxnAccountManageId());
                authorizationCheckProcessing.getAuthRecordedDTO().setInstallAccountManagerId(recorded.getTxnAccountManageId());


                //中行poc 扣账日（BILL_DATE_METHOD）： 0-交易日、1-账单日、2-还款日
                logger.info("Calling installProductInfoService.findByIndex with organizationNumber={}, productCode={}",
                        installOrder.getOrganizationNumber(), installOrder.getProductCode());
                InstallProductInfoResDTO installProductInfo = installProductInfoService.findByIndex(installOrder.getOrganizationNumber()
                        , installOrder.getProductCode());
                logger.info("installProductInfoService.findByIndex completed: billDateMethod={}", installProductInfo.getBillDateMethod());
                String billDateMethod = installProductInfo.getBillDateMethod();

                //不是按默认交日期抛账的需要更新入账日期
                if(!"0".equals(billDateMethod)){
                    LocalDate firstPostDate = null;
                    LocalDate nextProcessingDay = organizationInfoResDTO.getNextProcessingDay();
                    AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installOrder.getAccountManagementId());
                    Short cycleDay = accountManagementInfo.getCycleDay();
                    logger.info("Calling productInfoService.findProductInfo with organizationNumber={}, productNumber={}",
                            accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber());
                    List<ProductInfoResDTO> productInfoList = productInfoService.findProductInfo(accountManagementInfo.getOrganizationNumber(),
                            accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
                    logger.info("productInfoService.findProductInfo completed");
                    List<InstallPlan> installPlans2 = installPlanMapper.selectByOrderId(installOrder.getOrderId());
                    if (CustAccountBO.isBatch()) {
                        List<InstallPlanDTO> copys = BeanMapping.copyList(installPlans2, InstallPlanDTO.class);
                        copys = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(copys, orderId);

                        if (copys != null && !copys.isEmpty()) {
                            installPlans2 = BeanMapping.copyList(copys, InstallPlan.class);
                        }
                    }
                    for (InstallPlan installPlan : installPlans2) {
                        if("0".equals(billDateMethod)){
                            firstPostDate = nextProcessingDay;
                        }else if("1".equals(billDateMethod)){
                            firstPostDate = calculateStatementDate(nextProcessingDay,cycleDay);
                            if(null == accountManagementInfo.getLastStatementDate() ||
                                    LocalDate.of(1,1,1).compareTo(accountManagementInfo.getLastStatementDate()) ==0){
                                LocalDate maxLastStatementDate = accountManagementInfoSelfMapper.selectMaxLastStatementDateByOrgAndCid(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                                if(null != maxLastStatementDate && maxLastStatementDate.compareTo(LocalDate.of(1,1,1)) >0){
                                    firstPostDate = maxLastStatementDate.plusMonths(1);
                                }
                            }else {
                                if(null !=accountManagementInfo.getLastStatementDate() && firstPostDate.compareTo(accountManagementInfo.getLastStatementDate().plusMonths(1)) <0){
                                    firstPostDate = firstPostDate.plusMonths(1);
                                }
                            }
                        }else if("2".equals(billDateMethod)){
                             firstPostDate = calculateStatementDate(nextProcessingDay, cycleDay);
                            if(null == accountManagementInfo.getLastStatementDate() ||
                                    LocalDate.of(1,1,1).compareTo(accountManagementInfo.getLastStatementDate()) ==0){
                                LocalDate maxLastStatementDate = accountManagementInfoSelfMapper.selectMaxLastStatementDateByOrgAndCid(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
                                if(null != maxLastStatementDate && maxLastStatementDate.compareTo(LocalDate.of(1,1,1)) >0){
                                    firstPostDate = maxLastStatementDate.plusMonths(1);
                                }
                            }else {
                                if(null !=accountManagementInfo.getLastStatementDate() && firstPostDate.compareTo(accountManagementInfo.getLastStatementDate().plusMonths(1)) <0){
                                    firstPostDate = firstPostDate.plusMonths(1);
                                }
                            }
                            logger.info("Calling statementProcessService.findByOrgAndTableId with organizationNumber={}, tableId={}",
                                    accountManagementInfo.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
                            StatementProcessResDTO statementParam = statementProcessService.findByOrgAndTableId(accountManagementInfo.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
                            logger.info("statementProcessService.findByOrgAndTableId completed: dueDays={}", statementParam.getDueDays());
                            firstPostDate = firstPostDate.plusDays(statementParam.getDueDays());
                        }
                        installPlan.setTermPostDate(firstPostDate);

                        if (CustAccountBO.isBatch()) {
                            CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallPlan(BeanMapping.copy(installPlan, InstallPlanDTO.class));
                        } else {
                            installPlanMapper.updateByPrimaryKeySelective(installPlan);
                        }
                    }
                    installOrder.setFirstPostDate(firstPostDate);
                }


                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallOrder(BeanMapping.copy(installOrder, InstallOrderDTO.class));
                } else {
                    installOrderMapper.updateByPrimaryKeySelective(installOrder);
                }


                insertInstallmentGlRecord(copy, installProductInfo, recorded, organizationInfoResDTO);


            }
            //不良abs
            String absType = null;
            if (CustAccountBO.isBatch()) {
                absType = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(recorded.getTxnAccountManageId()).getAbsType();
            } else {
                AccountManagementInfo accountManagementInfo = installmentThreadLocalHolder.setAccountManagementInfo(recorded.getTxnAccountManageId());
                absType = accountManagementInfo.getAbsType();
            }
            if ("3".equals(absType)) {
                logger.info("Calling installOrderProcessService.badAbsToOrder with accountManagementId={}, productCode={}",
                        recorded.getTxnAccountManageId(), installOrder.getProductCode());
                installOrderProcessService.badAbsToOrder(recorded.getTxnAccountManageId(),
                        installOrder.getProductCode());
                logger.info("installOrderProcessService.badAbsToOrder completed");
            }
            authorizationCheckProcessing.setCustReconciliationDTO(custReconciliationDTO);
            return buildTrialData(installOrder);
        }
        return null;
    }

    private void insertInstallmentGlRecord(InstallOrderDTO order,
                                           InstallProductInfoResDTO installProductInfoResDTO,
                                           RecordedBO recorded, OrganizationInfoResDTO organizationInfoResDTO) {


        logger.info("Calling installAccountingTransParmService.selectByIndex with organizationNumber={}, postingTransactionParmId={}",
                order.getOrganizationNumber(), installProductInfoResDTO.getPostingTransactionParmId());
        InstallAccountingTransParmResDTO accountTran = installAccountingTransParmService.selectByIndex(
                order.getOrganizationNumber(), installProductInfoResDTO.getPostingTransactionParmId());
        logger.info("installAccountingTransParmService.selectByIndex completed");


        if (StringUtils.isNotBlank(accountTran.getPrincipalAccountantsTransactionCode())
                && StringUtils.isNotBlank(accountTran.getInterestAccountantsTransactionCode())){
            AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectFinanceStatus(recorded.getTxnAccountManageId());

            BigDecimal costAmount = order.getInstallmentAmount().subtract(order.getInstallmentTotalInterest());

            AccountantGlamsDTO accountantGlamsDTO = new AccountantGlamsDTO();
            accountantGlamsDTO.setAccountManagementId(recorded.getTxnAccountManageId());
            accountantGlamsDTO.setAcctLogo(accountManagementInfo.getProductNumber());
            accountantGlamsDTO.setTxnInd("4");
            accountantGlamsDTO.setCurrencyRate("1");

            accountantGlamsDTO.setCrdNumber(order.getCardNumber());
            accountantGlamsDTO.setPostingDate(organizationInfoResDTO.getNextProcessingDay());
            accountantGlamsDTO.setTxnSource(TransactionSourceEnum.LOCAL.getCode());

            accountantGlamsDTO.setTxnAmt(costAmount);
            accountantGlamsDTO.setTxnCurrency(order.getInstallmentCcy());

            accountantGlamsDTO.setPostingAmt(costAmount);
            accountantGlamsDTO.setPostingCurrencyCode(order.getInstallmentCcy());

            accountantGlamsDTO.setSettleAmt(costAmount);
            accountantGlamsDTO.setSettleCurrency(order.getInstallmentCcy());

            accountantGlamsDTO.setTxnCode(accountTran.getPrincipalAccountantsTransactionCode());
            accountantGlamsDTO.setTxnCodeOrig(accountTran.getInstallTransactionCode());

            logger.info("Calling glAmsServiceImpl.buildInstallmentGlAms for principal with orderId={}", order.getOrderId());
            glAmsServiceImpl.buildInstallmentGlAms(accountantGlamsDTO,order);
            logger.info("glAmsServiceImpl.buildInstallmentGlAms for principal completed");

            accountantGlamsDTO.setTxnAmt(order.getInstallmentTotalInterest());
            accountantGlamsDTO.setPostingAmt(order.getInstallmentTotalInterest());
            accountantGlamsDTO.setSettleAmt(order.getInstallmentTotalInterest());
            accountantGlamsDTO.setTxnCode(accountTran.getInterestAccountantsTransactionCode());
            accountantGlamsDTO.setTxnCodeOrig(accountTran.getInstallTransactionCode());

            logger.info("Calling glAmsServiceImpl.buildInstallmentGlAms for interest with orderId={}", order.getOrderId());
            glAmsServiceImpl.buildInstallmentGlAms(accountantGlamsDTO,order);
            logger.info("glAmsServiceImpl.buildInstallmentGlAms for interest completed");
        }

    }




    /**
     * 授权的分期试算
     * @param authorizationCheckProcessing AuthorizationCheckProcessingPayload
     * @return InstallTrialResDTO
     */
    @Override
    public InstallTrialResDTO handleTrialInstallFee(AuthorizationCheckProcessingPayload authorizationCheckProcessing) {
        OutstandingTransactionDTO outstandingTransactionDTO = authorizationCheckProcessing.getOutstandingTransactionDTO();
        if(null !=outstandingTransactionDTO){
            InstallEntryDTO installEntryDTO = new InstallEntryDTO();
            installEntryDTO.setOrganizationNumber(outstandingTransactionDTO.getOrganizationNumber());
            installEntryDTO.setProductCode(outstandingTransactionDTO.getInstalmentProduct());
            installEntryDTO.setInstallType(outstandingTransactionDTO.getInstallmentType());
            installEntryDTO.setAccountManagementId(authorizationCheckProcessing.getAuthRecordedDTO().getInstallAccountManagerId());
            installEntryDTO.setInstallPriceFlag(outstandingTransactionDTO.getInstallmentPriceFlag());
            installEntryDTO.setInstallTotalFee(outstandingTransactionDTO.getInstallmentTotalFee());
            installEntryDTO.setInstallFeeRate(outstandingTransactionDTO.getInstallmentFeeRate());
            installEntryDTO.setInstallDerateMethod(outstandingTransactionDTO.getInstallmentDerateMethod());
            installEntryDTO.setInstallDerateValue(outstandingTransactionDTO.getInstallmentDerateValue());
            installEntryDTO.setInstallAmount(outstandingTransactionDTO.getTransactionAmount());
            installEntryDTO.setAcquireReferenceNo(outstandingTransactionDTO.getAcquireReferenceNo());
            installEntryDTO.setMerchantId(outstandingTransactionDTO.getMerchantId());
            installEntryDTO.setCustomerRegion(outstandingTransactionDTO.getCustomerRegion());
            logger.info("Calling installOrderService.trialInstallFee with installAmount={}, productCode={}",
                    installEntryDTO.getInstallAmount(), installEntryDTO.getProductCode());
            return installOrderService.trialInstallFee(installEntryDTO);
        }
        return null;
    }


    private InstallTrialResDTO buildTrialData(InstallOrder installOrder){
        InstallTrialResDTO installTrialResDTO = new InstallTrialResDTO();
        installTrialResDTO.setOrderId(installOrder.getOrderId());
        installTrialResDTO.setInstallmentAmount(installOrder.getInstallmentAmount());
        installTrialResDTO.setTotalFeeAmount(installOrder.getTotalFeeAmount());
        installTrialResDTO.setFeeRate(installOrder.getFeeRate());
        installTrialResDTO.setTerm(installOrder.getTerm());
        installTrialResDTO.setFirstTermAmount(installOrder.getFirstTermAmount());
        installTrialResDTO.setTermAmount(installOrder.getTermAmount());
        installTrialResDTO.setFeeTerm(installOrder.getFeeTerm());
        installTrialResDTO.setFirstTermFee(installOrder.getFirstTermFee());
        installTrialResDTO.setTermFee(installOrder.getTermFee());
        installTrialResDTO.setInstallCurrency(installOrder.getInstallmentCcy());
        return installTrialResDTO;
    }
    /**
     * 下一账单日
     *
     * @param nextProcessingDay 下一处理日
     * @param cycleDate 账单日
     * @return 下一账单日
     */
    private LocalDate calculateStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month = 0;
        int year = 0;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        return LocalDate.of(year, month, cycleDate);
    }
}
