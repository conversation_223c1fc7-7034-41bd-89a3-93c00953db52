package com.anytech.anytxn.file.monitor;

import com.anytech.anytxn.file.config.SchedulerClientConfig;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.enums.FileResponseDetailEnum;
import com.anytech.anytxn.file.service.FileManagerScanProcessService;
import com.anytech.anytxn.file.service.FileScanMainOperator;
import com.anytech.anytxn.file.service.processor.IFileProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.config.TriggerTask;
import org.springframework.scheduling.support.CronTrigger;

import java.util.HashMap;
import java.util.Map;


/**
 * @description: 文件定时扫描线程的管理
 * 目前扫描公用一个线程。
 * 多个任务扫描时会堵塞，但由于任务扫描速度较快，有必要再优化
 * @author: zhangnan
 * @create: 2021-03-17
 **/
@EnableScheduling
@Configuration
public class FileScanParamMonitor implements SchedulingConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(FileScanParamMonitor.class);

    private ScheduledTaskRegistrar taskRegistrar;

    @Autowired
    private SchedulerClientConfig schedulerClientConfig;
    private Map<String, ScheduledTask> scheduledTaskMap = new HashMap<>();

    @Autowired
    private IFileProcessor schedulerFileProcessor;

    @Autowired
    private IFileProcessor simpleFileProcessor;
    @Autowired
    private FileManagerScanProcessService fileManagerScanProcessService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        this.taskRegistrar = taskRegistrar;

    }

    /**
     * 增加一个文件扫描监控
     *
     * @param taskId
     * @param cron
     */
    public void addTasks(FileManagerScanParam param) {
        stopTask(param);

        FileScanMainOperator fileScanOperator = new FileScanMainOperator(param,
                processorSelecter(param.getFileProcessType()),fileManagerScanProcessService);
        TriggerTask triggerTask = new TriggerTask(fileScanOperator::doScan, triggerContext -> {
            try {
                CronTrigger trigger = new CronTrigger(param.getScanCron());
                // todo nextExecution 替换nextExecutionTime方法
                return trigger.nextExecution(triggerContext);
            }catch (IllegalArgumentException e){
                logger.error("Cron expression is incorrect", e);
                fileManagerScanProcessService.updateParamStatus(FileResponseDetailEnum.FILE_SACAN_STATUS_103,param);
                return null; // 或者使用其他逻辑来确定一个默认的触发时间
            }

        });
        try {
            scheduledTaskMap.put(param.getId(), taskRegistrar.scheduleTriggerTask(triggerTask));
        }catch (IllegalArgumentException e){
            logger.error("IllegalArgumentException", e);
        }


    }

    public void stopTask(FileManagerScanParam param) {

        if (scheduledTaskMap.containsKey(param.getId())) {
            logger.info("Stopping task: {}", param.getId());
            ScheduledTask scheduledTask = scheduledTaskMap.get(param.getId());
            scheduledTask.cancel();
        }
    }


    /**
     * 选择找到文件后的处理方式
     */
    private IFileProcessor processorSelecter(Integer fileProcessType){
        //1 只执行调度
        if(1 == fileProcessType){
            return schedulerFileProcessor;
        }else {
            //后续待增加
            return simpleFileProcessor;
        }
    }
}
