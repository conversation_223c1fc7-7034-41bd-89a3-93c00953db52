package com.anytech.anytxn.installment.service;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallTrialResDTO;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallFeeCalculationService;
import com.anytech.anytxn.installment.base.service.IInstallOrderAppService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import com.anytech.anytxn.installment.service.interest.Rule78Interest;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.installment.base.utils.InstallmentThreadLocalHolder;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamtrSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.StatementProcessResDTO;
import com.anytech.anytxn.parameter.base.account.service.IStatementProcessService;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamtr;
import com.anytech.anytxn.parameter.base.authorization.service.ICmBizCommitAuditService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.enums.InstallmentAppHandlerEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.ProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @date 2023-12-27 14:19
 **/
@Slf4j
@Service
public class InstallOrderAppServiceImpl implements IInstallOrderAppService {
    private Logger logger = LoggerFactory.getLogger(InstallOrderAppServiceImpl.class);
    @Autowired
    private IInstallPlanService installPlanService;
    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private ICmBizCommitAuditService iCmBizCommitAuditService;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private IInstallOrderService installOrderService;

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private IInstallProductInfoService installProductInfoService;

    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;

    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private InstallmentThreadLocalHolder installmentThreadLocalHolder;

    @Autowired
    private IStatementProcessService statementProcessService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IInstallFeeCalculationService installFeeCalculationService;

    @Autowired
    private TPmsGlamtrSelfMapper tPmsGlamtrSelfMapper;

    @Autowired
    private ITransactionCodeService transactionCodeService;

    @Override
    public InstallTrialResDTO trialInstallFeeApp(InstallEntryDTO installEntryDTO) {
        logger.info("APP--分期试算---installEntryDTO:{}",installEntryDTO);
        InstallTrialResDTO installTrialResDTO = this.trialInstallFee(installEntryDTO);
        logger.info("APP--分期试算---返回数据集installTrialResDTO:{}",installTrialResDTO);
        return installTrialResDTO;
    }

    @Override
    public List<InstallAppResDTO> installmentRecordDetail(String cardNumber, String processStage) {
        LocalDate starDate = LocalDateTimeUtil.now().toLocalDate().plusMonths(-40);
        LocalDate endDate = LocalDateTimeUtil.now().toLocalDate().plusDays(1);
        logger.info("分期列表包含单笔和账单分期查询请求参数cardNumber:{},processStage:{},单笔&账单分期列表查询最近40个月的分期订单开始时间:{}----结束时间:{}"
                ,cardNumber,processStage,starDate,endDate);
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNumber);
        if (null == cardAuthorizationInfo){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_INFO_NOT_EXIST_FAULT);
        }
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(cardAuthorizationInfo.getPrimaryCustomerId(),cardAuthorizationInfo.getProductNumber(),OrgNumberUtils.getOrg());
        if (null == accountManagementInfo){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCOUNT_MANAGEMENT_RECORDED_NOT_EXIST_FAULT);
        }
        List<InstallAppResDTO> installEntryAppResDTOS = null;
        List<String> cardLists = null;
        String customerId = accountManagementInfo.getCustomerId();
        List<CardAuthorizationInfo> cardAuthorizationInfos = cardAuthorizationInfoSelfMapper.selectByCustomerId(customerId);
        if (!CollectionUtils.isEmpty(cardAuthorizationInfos)) {
            cardLists = cardAuthorizationInfos.stream().map(CardAuthorizationInfo::getCardNumber).collect(Collectors.toList());
        }
        if (InstallmentAppHandlerEnum.TRANSACTION_TRANSACTION_STARTING.getCode().equals(processStage)) {
            //待审核+审核通过但是未结清的分期(包含单笔和账单)
            installEntryAppResDTOS = instalmentInProgress(accountManagementInfo,starDate,endDate,cardNumber);
        }
        if (InstallmentAppHandlerEnum.INSTALLMENT_TRANSACTION_SETTLED.getCode().equals(processStage)) {
            installEntryAppResDTOS =  installmentAmorization(accountManagementInfo,starDate,endDate,cardNumber);
        }
        if (InstallmentAppHandlerEnum.INSTALLMENT_TRANSACTION_CHANGE.getCode().equals(processStage)) {
            installEntryAppResDTOS = installmentReject(cardLists,accountManagementInfo,starDate,endDate,cardNumber);
        }
        logger.info("分期列表包含单笔和账单分期返回数据集installEntryAppResDTOS:{}",installEntryAppResDTOS);
        return installEntryAppResDTOS;
    }

    public List<InstallAppResDTO> instalmentInProgress( AccountManagementInfo accountManagementInfo,LocalDate startDate,LocalDate endDate,String cardNumber) {
        List<InstallAppResDTO> installEntryAppResDTOS = null;
        //进行中，分期申请已提交，结清之前
        // 已审核但是还未结清
        List<InstallOrder> installOrders = installOrderSelfMapper.selectByOrgCidAndStatusAndTypes(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId(),
                Arrays.asList(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode()),
                Arrays.asList(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode()),
                startDate,endDate,cardNumber);
        if (!CollectionUtils.isEmpty(installOrders)) {
            installEntryAppResDTOS = new ArrayList<>();
            for (InstallOrder installOrder : installOrders) {
                InstallAppResDTO installAppResDTO = new InstallAppResDTO();
                installAppResDTO.setInstallType(installOrder.getType());
                installAppResDTO.setInstallTerm(installOrder.getTerm());
                installAppResDTO.setCardNumber(installOrder.getCardNumber());
                installAppResDTO.setInstallTotalFee(installOrder.getTotalFeeAmount());
                installAppResDTO.setApplyDateTime(installOrder.getCreateTime());
                installAppResDTO.setStatus("2");//进行中
                installAppResDTO.setInstallAmount(installOrder.getInstallmentAmount());
                installAppResDTO.setOrderId(installOrder.getOrderId());
                installEntryAppResDTOS.add(installAppResDTO);
                }
        }
        return installEntryAppResDTOS;
    }
    /**
     * @param
     * @return
     */
  /*  @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public InstallEntryAppResDTO entryApp(InstallEntryAppDTO installEntryAppDTO) {
        InstallEntryDTO installEntryDTO = BeanMapping.copy(installEntryAppDTO, InstallEntryDTO.class);
        InstallEntryAppResDTO result = null;
        try {
            //短信校验
            if(authSmsManager.verifyOtpToEai(installEntryAppDTO.getCardNumber(), installEntryAppDTO.getOpt())) {
                Long oldVersionNum = installEntryDTO.getVersionNumber();
                String installType = installEntryDTO.getInstallType();
                installManager.getInstallmentProductDesc(installEntryDTO);
                //是否开启交易审批开关
                Boolean isOpenAuditCommit = iParameterAuditService.openAuditCommit("transaction_audit_commit", OrgNumberUtils.getOrg());
                String commitAuditId = installEntryDTO.getCommitAuditId();
                String approveInfo = installEntryDTO.getApproveInfo();


                if ("0".equals(installEntryDTO.getIfAudit())) {
                    if (InstallmentTypeEnum.STATEMENT_INSTALL.getCode().equals(installType)) {
                        result = installBillEntryService.billInInstallmentApp(installEntryDTO);
                    }
                    if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installType)) {
                        result = installSingleEntryService.singleInstallmentApp(installEntryDTO);
                    }
                } else {
                    if (isOpenAuditCommit || ParmStatus.isEffective(installEntryDTO.getIfAudit())) {
                        //首次提交分期交易，进入审核列表交易记录暂存，审核通过后交易记录落库操作
                        CmBizAuditRecordDTO build = CmBizAuditRecordDTO.CmBizAuditRecordDTOBuilder.aCmBizAuditRecordDTO()
                                .withCardNumber(installEntryDTO.getCardNumber())
                                .withObject(installEntryDTO)
                                .withTransDesc(installEntryDTO.getTransactionDesc())
                                .withTransactionTypeEnum(TransactionTypeEnum.INSTALLMENT_TRANSACTION_ENTRY)
                                .build();
                        String dataString = iCmBizCommitAuditService.handleFirstTransaction(build);
                        if (StringUtils.isEmpty(dataString)) {
                            result = new InstallEntryAppResDTO();
                            result.setInstallmentAmount(installEntryDTO.getInstallAmount());
                            result.setApplyDateTime(LocalDateTime.now());
                            return result;
                        }
                        installEntryDTO = JSON.parseObject(dataString).toJavaObject(InstallEntryDTO.class);
                    }
                    if (isOpenAuditCommit || ParmStatus.isEffective(installEntryDTO.getIfAudit())) {
                        //多线程CAS
                        iCmBizCommitAuditService.auditHandle(commitAuditId, null, null, oldVersionNum);
                    }

                    if (InstallmentTypeEnum.STATEMENT_INSTALL.getCode().equals(installType)) {
                        result = installBillEntryService.billInInstallmentApp(installEntryDTO);
                    }
                    if (InstallmentTypeEnum.SINGLE_INSTALL.getCode().equals(installType)) {
                        result = installSingleEntryService.singleInstallmentApp(installEntryDTO);
                    }

                    if (isOpenAuditCommit || ParmStatus.isEffective(installEntryDTO.getIfAudit())) {
                        CmBizCommitAuditDTO cmBizCommitAuditDTO = iCmBizCommitAuditService.auditDetail(commitAuditId);
                        if (cmBizCommitAuditDTO != null) {
                            oldVersionNum = cmBizCommitAuditDTO.getVersionNumber();
                        }
                        //执行完成后 更新审批状态
                        iCmBizCommitAuditService.auditHandle(commitAuditId, AuthStatusEnum.HAS_AUTH.getCode(), approveInfo, oldVersionNum);
                    }
                }
            }
            return result;
        } finally {
            InstallmentThreadLocalHolder.remove();
        }
    }
*/
    private List<InstallAppResDTO> installmentAmorization(AccountManagementInfo accountManagementInfo,LocalDate startDate,LocalDate endDate,String cardNumber) {
        List<InstallAppResDTO> installEntryAppResDTOS = null;
        //已还清 包含提前结清+被动结清+正常结束
        List<InstallOrder> installOrders =  installOrderSelfMapper.selectByOrgCidAndStatusAndTypes(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId(),
               Arrays.asList( InstallmentOrderStatusEnum.NORMALEND_STATUS.getCode(), InstallmentOrderStatusEnum.PASSIVE_ADVANCESETTLEMENT_STATUS.getCode(), InstallmentOrderStatusEnum.ACTIVE_ADVANCESETTLEMENT_STATUS.getCode()),
                Arrays.asList(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode()),startDate,endDate,cardNumber);
        if (!CollectionUtils.isEmpty(installOrders)) {
            installEntryAppResDTOS = new ArrayList<>();
            for (InstallOrder installOrder : installOrders) {
                InstallAppResDTO installAppResDTO = new InstallAppResDTO();
                installAppResDTO.setInstallType(installOrder.getType());
                installAppResDTO.setInstallTerm(installOrder.getTerm());
                installAppResDTO.setCardNumber(installOrder.getCardNumber());
                installAppResDTO.setInstallAmount(installOrder.getInstallmentAmount());
                installAppResDTO.setInstallTotalFee(installOrder.getTotalFeeAmount());
                installAppResDTO.setApplyDateTime(installOrder.getCreateTime());
                installAppResDTO.setStatus("3");//已结清
                installAppResDTO.setOrderId(installOrder.getOrderId());
                installEntryAppResDTOS.add(installAppResDTO);
            }
        }
        return installEntryAppResDTOS;
    }
    private  List<InstallAppResDTO> installmentReject(List<String> cardLists,AccountManagementInfo accountManagementInfo,LocalDate startDate,LocalDate endDate,String cardNumber) {
        List<InstallAppResDTO> installEntryAppResDTOS = new ArrayList<>();
      /*  // 已取消 审核拒绝---不走审核流程
        List<CmBizCommitAuditDTO> data = iCmBizCommitAuditService.selectByCardAndStatus(cardLists, AuthStatusEnum.HAS_AUTH.getCode(), AuthStatusEnum.AUTH_REJECT.getCode());
        if (!CollectionUtils.isEmpty(data)){
            for (CmBizCommitAuditDTO cmBizCommitAuditDTO : data) {
                InstallEntryDTO installEntryDTO = JSON.parseObject(cmBizCommitAuditDTO.getJsonData()).toJavaObject(InstallEntryDTO.class);
                InstallAppResDTO copy = BeanMapping.copy(installEntryDTO, InstallAppResDTO.class);
                copy.setApplyDateTime(cmBizCommitAuditDTO.getApplicationTime());
                copy.setStatus("4");//拒绝
                installEntryAppResDTOS.add(copy);
            }

        }*/
        //已还清 包含提前结清+被动结清+正常结束
        List<InstallOrder> installOrders =  installOrderSelfMapper.selectByOrgCidAndStatusAndTypes(OrgNumberUtils.getOrg(), accountManagementInfo.getCustomerId(),
                Arrays.asList( InstallmentOrderStatusEnum.CANCLE_STATUS.getCode()),
                Arrays.asList(InstallmentTypeEnum.APP_STATEMENT_INSTALL.getCode(),InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode()),startDate,endDate,cardNumber);
        if (!CollectionUtils.isEmpty(installOrders)) {
            for (InstallOrder installOrder : installOrders) {
                InstallAppResDTO installAppResDTO = new InstallAppResDTO();
                installAppResDTO.setInstallType(installOrder.getType());
                installAppResDTO.setInstallTerm(installOrder.getTerm());
                installAppResDTO.setCardNumber(installOrder.getCardNumber());
                installAppResDTO.setInstallAmount(installOrder.getInstallmentAmount());
                installAppResDTO.setInstallTotalFee(installOrder.getTotalFeeAmount());
                installAppResDTO.setApplyDateTime(installOrder.getCreateTime());
                installAppResDTO.setStatus("4");//拒绝
                installAppResDTO.setOrderId(installOrder.getOrderId());
                installEntryAppResDTOS.add(installAppResDTO);
            }
        }
        return installEntryAppResDTOS;
    }
    @Override
    public InstallOrderAppResDTO findOrderAppById(String orderId) {
        logger.info("APP---分期订单和分期计划查询请求参数orderId:{}",orderId);
        InstallOrderDTO installOrderDTO = installOrderService.findOrderById(orderId);
        InstallOrderAppResDTO copy = BeanMapping.copy(installOrderDTO, InstallOrderAppResDTO.class);
        List<InstallPlanDTO> installPlanDTOS = installPlanService.planByOrderId(orderId);
        String accountManagementId = installOrderDTO.getAccountManagementId();
        List<AccountStatementInfo> accountStatementInfos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManagementId);
        if (CollectionUtils.isEmpty(accountStatementInfos)){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCT_STATEMENT_INFO_NOT_EXIST_FAULT);
        }
        copy.setPaymentDate(accountStatementInfos.get(0).getPaymentDueDate()+"");
        copy.setInstallPlanDTOS(installPlanDTOS);
        logger.info("APP---分期订单查询返回数据集APP分期订单(返回订单和分期计划数据):{}",copy);
        return copy;
    }
    /**
     * 分期试算
     * @param installEntryDTO 分期录入实体
     * @return 分期费用试算
     */
    public InstallTrialResDTO trialInstallFee(InstallEntryDTO installEntryDTO){
        InstallParameterDTO installParameter = new InstallParameterDTO();
        String organizationNumber = installEntryDTO.getOrganizationNumber();
        String installProductCode = installEntryDTO.getProductCode();
        InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(organizationNumber, installProductCode);
        String postingTransactionParmId = installProConf.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO accountTran = installAccountingTransParmService.selectByIndex(organizationNumber, postingTransactionParmId);
        InstallOrderDTO installOrderDTO = new InstallOrderDTO();
        installOrderDTO.setOrganizationNumber(organizationNumber);
        installOrderDTO.setType(installEntryDTO.getInstallType());
        installOrderDTO.setAccountManagementId(installEntryDTO.getAccountManagementId());
        installOrderDTO.setInstallPriceFlag(installEntryDTO.getInstallPriceFlag());
        installOrderDTO.setTotalFeeAmount(installEntryDTO.getInstallTotalFee());
        installOrderDTO.setFeeRate(installEntryDTO.getInstallFeeRate());
        installOrderDTO.setFeeDerateFlag(installEntryDTO.getInstallDerateMethod());
        installOrderDTO.setDerateFeeAmount(installEntryDTO.getInstallDerateValue());
        installOrderDTO.setTerm(installProConf.getTerm());
        installOrderDTO.setInstallmentAmount(installEntryDTO.getInstallAmount());
        installOrderDTO.setAcquireReferenceNo(installEntryDTO.getAcquireReferenceNo());
        LocalDate firstPostDate = calculateFirstPostDate(installOrderDTO,installProConf);
        installOrderDTO.setFirstPostDate(firstPostDate);
        installOrderDTO.setMerchantId(installEntryDTO.getMerchantId());
        installOrderDTO.setCustomerRegion(installEntryDTO.getCustomerRegion());
        installOrderDTO.setProductCode(installProductCode);
        installParameter.setInstallOrder(installOrderDTO);
        installParameter.setInstallProInfo(installProConf);
        installParameter.setInstallAccountTran(accountTran);
        //封装手续费计费组件
        InstallRateCalDTO installRateCal = getInstallRateCal(installParameter);
        //封装订单
        installParameter = packageInstallOrder(installRateCal, installParameter);
        //封装计划
        List<InstallPlan> installPlans = pakagePlan(installParameter);
        InstallTrialResDTO installTrialResDTO = new InstallTrialResDTO();
        installTrialResDTO.setInstallPlanList(BeanMapping.copyList(installPlans,InstallPlanDTO.class));
        InstallOrderDTO installOrder = installParameter.getInstallOrder();
        installTrialResDTO.setInstallmentAmount(installOrder.getInstallmentAmount());
        installTrialResDTO.setTotalFeeAmount(installOrder.getTotalFeeAmount());
        installTrialResDTO.setFeeRate(installOrder.getFeeRate());
        installTrialResDTO.setTerm(installOrder.getTerm());
        installTrialResDTO.setFirstTermAmount(installOrder.getFirstTermAmount());
        installTrialResDTO.setTermAmount(installOrder.getTermAmount());
        installTrialResDTO.setFeeTerm(installOrder.getFeeTerm());
        installTrialResDTO.setFirstTermFee(installOrder.getFirstTermFee());
        installTrialResDTO.setTermFee(installOrder.getTermFee());
        installTrialResDTO.setInstallCurrency(installOrder.getInstallmentCcy());
        installTrialResDTO.setDerateFeeAmount(installOrder.getDerateFeeAmount());
        installTrialResDTO.setDiscountRate(installOrder.getDiscountRate());
        return installTrialResDTO;
    }
    private LocalDate calculateFirstPostDate(InstallOrderDTO installOrderDTO,InstallProductInfoResDTO installProInfo){
        LocalDate firstPostDate;
        //延迟下账数/月
        Integer amountOfDelayed = installProInfo.getAmountOfDelayed();
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        if(Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrderDTO.getType())){
            //自动分期是批量入账 赋值today
            firstPostDate = organizationInfo.getToday();
        }else {
            //扣账日 0-交易日、1-账单日、2-还款日
            String billDateMethod = installProInfo.getBillDateMethod();
            firstPostDate  = organizationInfo.getNextProcessingDay();
            String accountManagementId = installOrderDTO.getAccountManagementId();
            if (null != accountManagementId && !"".equals(accountManagementId)) {
                AccountManagementInfo accountManagementInfo = installmentThreadLocalHolder.setAccountManagementInfo(accountManagementId);
//                if (accountManagementInfo == null) {
//                    accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
//                }
                List<ProductInfoResDTO> productInfoList = productInfoService.findProductInfo(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getProductNumber(), accountManagementInfo.getCurrency());
                Short cycleDay = accountManagementInfo.getCycleDay();
                if ("0".equals(billDateMethod)) {
                    firstPostDate = organizationInfo.getNextProcessingDay();
                } else if ("1".equals(billDateMethod)) {
                    firstPostDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
                } else if ("2".equals(billDateMethod)) {
                    LocalDate statementDate = calculateStatementDate(organizationInfo.getNextProcessingDay(), cycleDay);
                    StatementProcessResDTO statementParam = statementProcessService.findByOrgAndTableId(accountManagementInfo.getOrganizationNumber(), productInfoList.get(0).getStatementProcessingTableId());
                    firstPostDate = statementDate.plusDays(statementParam.getDueDays());
                }
            }
        }
        if (amountOfDelayed != null){
            firstPostDate = firstPostDate.plusMonths(amountOfDelayed.longValue());
        }
        return firstPostDate;
    }
    /**
     * 手续费计算参数
     *
     * @param installParameter 分期订单的参数
     * @return InstallRateCalDTO
     **/
    public InstallRateCalDTO getInstallRateCal(InstallParameterDTO installParameter) {
        InstallRateCalDTO installRateCalDTO = new InstallRateCalDTO();
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        //费用代码
        String feeCodeId = installParameter.getInstallProInfo().getFeeCodeId();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        installRateCalDTO.setOrganizationNumber(installOrderDTO.getOrganizationNumber());
        installRateCalDTO.setInstallmentPriceFlag(installOrderDTO.getInstallPriceFlag());
        installRateCalDTO.setInstallmentTotalFee(installOrderDTO.getTotalFeeAmount());
        installRateCalDTO.setInstallmentFeeRate(installOrderDTO.getFeeRate());
        installRateCalDTO.setInstallmentDerateMethod(installOrderDTO.getFeeDerateFlag());
        installRateCalDTO.setInstallmentDerateValue(installOrderDTO.getDerateFeeAmount());
        installRateCalDTO.setInstallmentTerm(installOrderDTO.getTerm());
        installRateCalDTO.setInstallmentAmount(installOrderDTO.getInstallmentAmount());
        installRateCalDTO.setInstallmentFeeCodeBean(feeCodeId);
        installRateCalDTO.setFeeFlag(feeReceiveFlag);
        //本金减免相应字段设置

        if (StringUtils.isBlank(installRateCalDTO.getFeeFlag())) {
            logger.error("手续费收取方式 不存在");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_FEE_FLAG_NOT_NULL_FAULT);
        }

        return installRateCalDTO;
    }
    /**
     * 封装订单
     *
     * @param installRateCal 费率计算参数
     * @param installParameter 费率计算参数
     * @return InstallParameterDTO
     */
    public InstallParameterDTO packageInstallOrder(InstallRateCalDTO installRateCal,
                                                   InstallParameterDTO installParameter) {
        //手续费计算组件
        installRateCal.setInstallParameterDTO(installParameter);
        FeeTypeDTO feeTypeDTO = installFeeCalculationService.installmentFeeCalculation(installRateCal);
        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
        //每期本金计算
        InstallOrder installOrder = principalCalculation(installParameter, feeTypeDTO);

        //封装分期订单信息表
        installOrder.setOrderId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        installOrder.setCustomerRegion(installOrderDTO.getCustomerRegion());
        installOrder.setCustomerId(installOrderDTO.getCustomerId());
        installOrder.setBranchNumber(installOrderDTO.getBranchNumber());
        installOrder.setGroupType(installOrderDTO.getGroupType());
        installOrder.setOrganizationNumber(installOrderDTO.getOrganizationNumber());
        installOrder.setAccountManagementId(installOrderDTO.getAccountManagementId());
        installOrder.setCardNumber(installOrderDTO.getCardNumber());
        installOrder.setProductCode(installOrderDTO.getProductCode());
        if (Objects.equals(InstallmentTypeEnum.AUTO_INSTALL.getCode(), installOrderDTO.getType())) {
            //自动分期是批量入账 赋值today
            installOrder.setTransactionDate(installOrderDTO.getFirstPostDate().atTime(LocalTime.now()));
        } else {
            installOrder.setTransactionDate(installOrderDTO.getTransactionDate());
        }
        installOrder.setAcquireReferenceNo(installOrderDTO.getAcquireReferenceNo());
        installOrder.setAuthorizationCode(installOrderDTO.getAuthorizationCode());
        installOrder.setInstallmentCcy(installOrderDTO.getInstallmentCcy());
        installOrder.setType(installOrderDTO.getType());
        installOrder.setStatus(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode());
        //订单状态变更日期
        installOrder.setStatusUpdateDate(LocalDate.now());
        //首次入账日期 系统当前业务日期
        installOrder.setFirstPostDate(installOrderDTO.getFirstPostDate());
        //已入账本金期数
        installOrder.setPostedTerm(0);
        //分期金额
        installOrder.setInstallmentAmount(installOrderDTO.getInstallmentAmount());
        //未入账金额
        installOrder.setUnpostedAmount(installOrderDTO.getInstallmentAmount());
        //手续费计算总的费用金额
        //总费用金额
        installOrder.setTotalFeeAmount(feeTypeDTO.getTotalCost());

        if (feeTypeDTO.getTotalCost().compareTo(BigDecimal.ZERO) == 0){
            installOrder.setFeeTerm(0);
        }

        //未入账费用金额
        installOrder.setUnpostedFeeAmount(feeTypeDTO.getTotalCost());
        //已入账费用期数
        installOrder.setPostedFeeTerm(0);
        //还款方式
        installOrder.setPaymentWay(installParameter.getInstallProInfo().getPaymentWay());
        //手续费收取方式
        installOrder.setFeeFlag(installParameter.getInstallProInfo().getFeeReceiveFlag());
        //费率
        installOrder.setFeeRate(feeTypeDTO.getFeeRate());
        //手续费减免方式
        installOrder.setFeeDerateFlag(feeTypeDTO.getDerateMethod());

        //费用减免期数, 减免金额> 0 的期数
        installOrder.setDerateTerm(feeTypeDTO.getDerateTerm());


        //费用减免总金额
        BigDecimal feeReduce = feeTypeDTO.getDerateFeeAmount() == null ? BigDecimal.ZERO :
                Arrays.stream(feeTypeDTO.getDerateFeeAmount()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        installOrder.setDerateFeeAmount(feeReduce);

        if (Objects.equals(InstallDerateMethodEnum.EACH_DISCOUNT.getCode(), feeTypeDTO.getDerateMethod())) {
            installOrder.setDiscountRate(feeTypeDTO.getDerateValue());
        }


        //本金减免总金额
        Optional<BigDecimal> prinReduce = Arrays.stream(feeTypeDTO.getPrincipalDerateFeeAmount()).reduce(BigDecimal::add);
        installOrder.setPrincipalDerateTotalAmount(prinReduce.orElse(BigDecimal.ZERO));
        //本金减免期数
        installOrder.setPrincipalDerateTerm(feeTypeDTO.getPrincipalDerateTerm());
        //本金累计减免金额
        installOrder.setPrincipalDerateAmount(BigDecimal.ZERO);

        //手续费累计收取金额
        installOrder.setTotalReceivedFee(BigDecimal.ZERO);
        //手续费累计减免金额
        installOrder.setTotalDerateFee(BigDecimal.ZERO);
        //提前还款违约金收取金额
        installOrder.setReceivedPenatlyAmount(BigDecimal.ZERO);
        //累计退货金额
        installOrder.setTotalReturnedAmount(BigDecimal.ZERO);
        //分期占用额度节点
        installOrder.setLimitCode(installOrderDTO.getLimitCode());
        //商户号
        installOrder.setMerchantId(installOrderDTO.getMerchantId());
        //商户分类
        installOrder.setMcc(installOrderDTO.getMcc());
        installOrder.setTransactionDesc(installOrderDTO.getTransactionDesc());
        //原始交易ID
        installOrder.setOriginTransactionId(installOrderDTO.getOriginTransactionId());
        installOrder.setCreateTime(LocalDateTime.now());
        installOrder.setUpdateTime(LocalDateTime.now());
        installOrder.setUpdateBy("admin");
        installOrder.setVersionNumber(1L);
        installOrder.setGlobalFlowNumber(installOrderDTO.getGlobalFlowNumber());
        installOrder.setAbsStatus(installOrderDTO.getAbsStatus());
        installOrder.setAbsProductCode(installOrderDTO.getAbsProductCode());
        installOrder.setMerchantName(installOrderDTO.getMerchantName());
        installOrder.setRetrievalReferenceNumber(installOrderDTO.getRetrievalReferenceNumber());
        installOrder.setInstallmentTotalInterest(installOrderDTO.getInstallmentTotalInterest());
        logger.info("installment order info is {}", JacksonUtils.toJsonStr(installOrder));
        installParameter.setInstallOrder(BeanMapping.copy(installOrder, installOrderDTO.getClass()));
        installParameter.setFeeTypeDTO(feeTypeDTO);
        return installParameter;
    }
    /**
     * 封装计划
     *
     * @param installParameter 创建分期订单的参数信息
     * @return List<InstallPlan>
     */
    public List<InstallPlan> pakagePlan(InstallParameterDTO installParameter) {
        FeeTypeDTO feeTypeDTO = installParameter.getFeeTypeDTO();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        //产品周期
        String cycle = installParameter.getInstallProInfo().getCycle();
        //分期期数
        Integer term;
        if (PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())) {
            term = installParameter.getInstallOrder().getFeeTerm();
        } else {
            term = installParameter.getInstallOrder().getTerm();
        }
        //解决ANYTXNCMBC-424,先费后本模式，手续费为期初一次性收取或期末一次性收取
        if (PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())
                && (FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag)
                || FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag))) {

            String productCode = installParameter.getInstallOrder().getProductCode();
            InstallProductInfoResDTO installProductInfoResDTO = installProductInfoService.findByIndex(installParameter.getInstallOrder().getOrganizationNumber(), productCode);
            term = installProductInfoResDTO.getTerm();
        }


        //1.计算利息摊销
        List<BigDecimal>  calculateInterestAmortize = calculateInterestAmortize(installParameter, term);

        //2.计算费用摊销
        ImmutablePair<BigDecimal, BigDecimal> calculateFeeAmortize =  calculateFeeAmortize(
                term, feeReceiveFlag, feeTypeDTO, installParameter);


        //封装分期计划表
        List<InstallPlan> arrayList = new ArrayList<>();
        LocalDate termPostDate = installParameter.getInstallOrder().getFirstPostDate();
        if (termPostDate == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.ED_E);
        }
        //中行POC 不收取费用的期数
        Integer unReceiveFeeTerm = feeTypeDTO.getUnReceiveFeeTerm() == null ? 0 : feeTypeDTO.getUnReceiveFeeTerm();
        if (unReceiveFeeTerm.compareTo(term) >= 0) {
            logger.error("不收取费用的期数大于等于分期期数");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.PE_GT_IN);
        }
        //中行poc 不收取费用的期数
        Integer firstReceiveFeeTerm = unReceiveFeeTerm + 1;
        //中行poc
        InstallProductInfoResDTO installProInfo = installParameter.getInstallProInfo();
        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installProInfo.getBalanceMethod();



        //减免期数
        Integer deraTerm = feeTypeDTO.getDerateTerm();

        for (int i = 1; i <= term; i++) {

            InstallPlan installPlan = new InstallPlan();

            installPlan.setOrderId(installParameter.getInstallOrder().getOrderId());
            //机构号
            installPlan.setOrganizationNumber(installParameter.getInstallOrder().getOrganizationNumber());
            //状态
            installPlan.setTermStutus("N");
            installPlan.setAbsStatus(installParameter.getInstallOrder().getAbsStatus());

            //本金减免金额
            installPlan.setPrincipalDerateFeeAmount(Optional.ofNullable(feeTypeDTO.getPrincipalDerateFeeAmount()[i-1]).orElse(BigDecimal.ZERO));

            //第一期特殊处理
            if (i == 1) {
                //入账日期
                installPlan.setTermPostDate(termPostDate);

                //尾款方式 0-余数放首期、1-余数放尾期
                if ("0".equals(balanceMethod) || term == 1) {
                    installPlan.setTermAmount(feeTypeDTO.getFirstAmount());
                } else if ("1".equals(balanceMethod)) {
                    installPlan.setTermAmount(feeTypeDTO.getEashAmount());
                }

                //中行poc
                if (unReceiveFeeTerm.compareTo(1) >= 0) {
                    installPlan.setFeeAmount(BigDecimal.ZERO);
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod) || term == 1 || FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag)) {
                        installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                    } else if ("1".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                    }
                }
                if (deraTerm >= 1) {
                    installPlan.setDerateAmount(feeTypeDTO.getDerateFeeAmount()[i - 1]);
                } else {
                    installPlan.setDerateAmount(BigDecimal.ZERO);
                }
                installPlan.setAmortizeFee(calculateFeeAmortize.getLeft());
                installPlan.setTerm(i);
            } else {
                if (Objects.equals(InstallCycleEnum.YEAR.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusYears(i - 1L));
                } else if (Objects.equals(InstallCycleEnum.HALF_YEAR.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(6L * (i - 1)));
                } else if (Objects.equals(InstallCycleEnum.SEASON.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(3L * (i - 1)));
                } else if (Objects.equals(InstallCycleEnum.MOUTH.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusMonths(i - 1L));
                } else if (Objects.equals(InstallCycleEnum.DAY.getCode(), cycle)) {
                    installPlan.setTermPostDate(termPostDate.plusDays(i - 1L));
                }
                if (i == term && PaymentWayEnum.FIRST_COST_LATER_COST.getCode().equals(installParameter.getInstallOrder().getPaymentWay())) {
                    installPlan.setTermAmount(installParameter.getInstallOrder().getInstallmentAmount());
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setTermAmount(feeTypeDTO.getEashAmount());
                    } else if ("1".equals(balanceMethod)) {

                        if (i == term) {
                            installPlan.setTermAmount(feeTypeDTO.getFirstAmount());
                        } else {
                            installPlan.setTermAmount(feeTypeDTO.getEashAmount());

                        }

                    }
                }

                if (firstReceiveFeeTerm.compareTo(i) == 0) {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                    } else if ("1".equals(balanceMethod)) {
                        if (i == term) {
                            installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                        } else {
                            installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                        }

                    }
                } else if (firstReceiveFeeTerm.compareTo(i) > 0) {
                    installPlan.setFeeAmount(BigDecimal.ZERO);
                } else {
                    //尾款方式 0-余数放首期、1-余数放尾期
                    if ("0".equals(balanceMethod)) {
                        installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                    } else if ("1".equals(balanceMethod)) {
                        if (i == term && !"F".equals(feeReceiveFlag)) {
                            installPlan.setFeeAmount(feeTypeDTO.getFirstPayment());
                        } else {
                            installPlan.setFeeAmount(feeTypeDTO.getEashPayment());
                        }

                    }
                }

                if (i <= feeTypeDTO.getDerateFeeAmount().length) {
                    installPlan.setDerateAmount(feeTypeDTO.getDerateFeeAmount()[i - 1]);
                } else {
                    installPlan.setDerateAmount(BigDecimal.ZERO);
                }

                installPlan.setAmortizeFee(calculateFeeAmortize.getRight());
                installPlan.setTerm(i);
            }
            //中行POC E-期末一次性收取
            if (FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag) && i == term) {
                installPlan.setFeeAmount(feeTypeDTO.getTotalCost());
            }
            installPlan.setAmortizeInterest(calculateInterestAmortize.get(i - 1));
            installPlan.setCreateTime(LocalDateTime.now());
            installPlan.setUpdateTime(LocalDateTime.now());
            installPlan.setUpdateBy("admin");
            installPlan.setVersionNumber(1L);
            arrayList.add(installPlan);
        }
        return arrayList;
    }
    /**
     * 下一账单日
     *
     * @param nextProcessingDay 下一处理日
     * @param cycleDate         账单日
     * @return 下一账单日
     */
    private LocalDate calculateStatementDate(LocalDate nextProcessingDay, short cycleDate) {
        int month;
        int year;
        if (nextProcessingDay.getDayOfMonth() > cycleDate) {
            month = nextProcessingDay.getMonthValue() + 1;
        } else {
            month = nextProcessingDay.getMonthValue();
        }
        year = nextProcessingDay.getYear();
        if (month >= 13) {
            month = month - 12;
            year = year + 1;
        }
        return LocalDate.of(year, month, cycleDate);
    }

    /**
     * 每期本金计算
     *
     * @param installParameter 分期参数
     * @param feeTypeDTO       费用
     * @return InstallOrder
     */
    private InstallOrder principalCalculation(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {
        InstallOrder installOrder = new InstallOrder();

        //获取分期费用代码
        InstallFeeCodeInfoResDTO installFeeCode = installParameter.getInstallFeeCode();
        //本金减免方式
        feeTypeDTO.setPrincipalReliefMethod(installFeeCode.getPrincipalRelief());

        //还款方式
        String paymentWay = installParameter.getInstallProInfo().getPaymentWay();
        //手续费收取方式
        String feeReceiveFlag = installParameter.getInstallProInfo().getFeeReceiveFlag();
        //每期本金
        BigDecimal termAmount;
        //首期本金
        BigDecimal firstTermAmount;
        //分期金额
        BigDecimal installmentAmount = installParameter.getInstallOrder().getInstallmentAmount();
        //分期期数
        Integer term = installParameter.getInstallOrder().getTerm();
        BigDecimal termdb = new BigDecimal(term);
        //中行poc
        InstallProductInfoResDTO installProInfo = installParameter.getInstallProInfo();
        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installProInfo.getBalanceMethod();

        //还款方式为A-等费等本
        if (Objects.equals(PaymentWayEnum.EQUAL_COST_EQUAL_CAPITAL.getCode(), paymentWay)) {
            //每期本金 = 分期金额/分期期数
            termAmount = installmentAmount.divide(termdb, 0, BigDecimal.ROUND_DOWN).setScale(2);
            //首期本金 =分期金额-每期本金 * (分期期数-1)
            firstTermAmount = installmentAmount
                    .subtract(termAmount.multiply(termdb.subtract(BigDecimal.ONE)));
            feeTypeDTO.setEashAmount(termAmount);
            feeTypeDTO.setFirstAmount(firstTermAmount);
            //尾款方式 0-余数放首期、1-余数放尾期
            if ("0".equals(balanceMethod)) {
                installOrder.setTermAmount(termAmount);
                installOrder.setFirstTermAmount(firstTermAmount);
                //本金减免金额
            } else {
                installOrder.setTermAmount(termAmount);
                installOrder.setFirstTermAmount(termAmount);
            }

            setEachPrincipalTermArray(termAmount,firstTermAmount, installParameter, feeTypeDTO);
            installOrder.setTerm(term);
        }

        //还款方式为F-先费后本
        if (Objects.equals(PaymentWayEnum.FIRST_COST_LATER_COST.getCode(), paymentWay)) {
            //每期本金=0
            termAmount = BigDecimal.ZERO;
            feeTypeDTO.setEashAmount(termAmount);
            feeTypeDTO.setFirstAmount(termAmount);
            installOrder.setTermAmount(termAmount);
            installOrder.setFirstTermAmount(termAmount);
            installOrder.setTerm(1);

            setEachPrincipalTermArray(BigDecimal.ZERO,installmentAmount, installParameter, feeTypeDTO);
        }

        principalRelief(installParameter,feeTypeDTO);

        //每期费用计算
        //手续费收取方式为I-分期收取
        if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeReceiveFlag)) {
            //尾款方式 0-余数放首期、1-余数放尾期
            if ("0".equals(balanceMethod)) {
                //每期手续费 = 每期手续费
                installOrder.setTermFee(feeTypeDTO.getEashPayment());
                //首期手续费 = 首期手续费
                installOrder.setFirstTermFee(feeTypeDTO.getFirstPayment());
            } else {
                //每期手续费 = 每期手续费
                installOrder.setTermFee(feeTypeDTO.getEashPayment());
                //首期手续费 = 每期手续费
                installOrder.setFirstTermFee(feeTypeDTO.getEashPayment());
            }

            installOrder.setFeeTerm(term);
        }

        //手续费收取方式为F-期初一次性收取
        if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeReceiveFlag)) {
            //每期手续费 = 0
            installOrder.setTermFee(BigDecimal.ZERO);
            //首期手续费 = 分期总的手续费
            installOrder.setFirstTermFee(feeTypeDTO.getTotalCost());
            installOrder.setFeeTerm(1);
        }
        //手续费收取方式为F-期末一次性收取
        if (Objects.equals(FeeReceiveFagEnum.END_ONE_TIME.getCode(), feeReceiveFlag)) {
            //每期手续费 = 0
            installOrder.setTermFee(BigDecimal.ZERO);
            //首期手续费 = 0
            installOrder.setFirstTermFee(BigDecimal.ZERO);
            installOrder.setFeeTerm(1);
        }
        logger.info("installment feeType info is {}",JacksonUtils.toJsonStr(feeTypeDTO));
        return installOrder;
    }
    /**
     * 设置本金费用金额
     */
    private void setEachPrincipalTermArray(BigDecimal termAmount, BigDecimal firstAmount,
                                           InstallParameterDTO installParameter,
                                           FeeTypeDTO feeTypeDTO) {

        //还款方式
        String paymentWay = installParameter.getInstallProInfo().getPaymentWay();

        //尾款方式 0-余数放首期、1-余数放尾期
        String balanceMethod = installParameter.getInstallProInfo().getBalanceMethod();
        //期数
        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];

        //等费等本
        if (Objects.equals(PaymentWayEnum.EQUAL_COST_EQUAL_CAPITAL.getCode(), paymentWay)) {

            Arrays.fill(array, termAmount);
            //余数放首期
            if (InstallManager.isFirstTerm(balanceMethod)){
                array[0] = firstAmount;
            }else {
                array[array.length - 1] = firstAmount;
            }
        }

        //先费后本
        if ((Objects.equals(PaymentWayEnum.FIRST_COST_LATER_COST.getCode(), paymentWay))){

            Arrays.fill(array, BigDecimal.ZERO);
            array[array.length - 1] = firstAmount;

        }

        feeTypeDTO.setPrincipalEachTermFeeAmount(array);

        logger.info("installment cost array is {}",JacksonUtils.toJsonStr(array));
    }
    /**
     * 本金减免金额计算
     * @param feeTypeDTO 手续费计算涉及参数
     * @param installParameter 分期参数
     */
    private void principalRelief(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金减免方式
        String principalReliefMethod = feeTypeDTO.getPrincipalReliefMethod();


        //不减免
        if (Objects.equals(PrincipalReductionMethodEnum.NO_REDUCTION.getCode(),principalReliefMethod)){
            noPrincipalBreak(feeTypeDTO);
        }
        //减免方式为1-减免期数
        if (Objects.equals(PrincipalReductionMethodEnum.BREAKS_TEM.getCode(),principalReliefMethod)){
            principalBreakTerm(installParameter,feeTypeDTO);
        }
        //减免方式2-减免金额
        else if (Objects.equals(PrincipalReductionMethodEnum.CREDIT_AMOUNT.getCode(),principalReliefMethod)){

            principalBreaksAmount(installParameter,feeTypeDTO);
        }
        //减免方式3-减免期数及金额
        else if (Objects.equals(PrincipalReductionMethodEnum.PERIODS_AND_AMOUNT.getCode(),principalReliefMethod)){

            principalBreaksAmountAndTerm(installParameter,feeTypeDTO);
        }

    }
    /**
     * 不减免
     * @param feeTypeDTO 手续费计算涉及参数
     *
     * */
    private void noPrincipalBreak(FeeTypeDTO feeTypeDTO) {

        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);
        //分期减免期数
        feeTypeDTO.setPrincipalDerateTerm(0);
        //本金减免费用金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }

    /**
     * 减免期数
     * @param feeTypeDTO 手续费计算涉及的参数
     * @param installParameter 分期参数
     *
     */
    private void principalBreakTerm(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金期数减免值
        BigDecimal principalTermValue = installParameter.getInstallFeeCode().getPrincipalTermValue();

        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        int len = principalTermValue.intValue() > installParameter.getInstallOrder().getTerm()
                ? installParameter.getInstallOrder().getTerm() : principalTermValue.intValue();

        //每期-本金分期费用金额
        for (int i = 0; i < len; i++) {
            array[i] = feeTypeDTO.getPrincipalEachTermFeeAmount()[i];
        }

        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);
    }

    /**
     * 减免金额
     * @param feeTypeDTO 手续费计算涉及的参数
     * @param installParameter 分期参数
     */
    private void principalBreaksAmount(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //本金 金额减免值
        BigDecimal principalAmountValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalAmountValue()).orElse(BigDecimal.ZERO);


        BigDecimal[] array = new BigDecimal[feeTypeDTO.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        //获取 本金金额
        BigDecimal[] eachTermPrincipalAmount = feeTypeDTO.getPrincipalEachTermFeeAmount();
        //减免值
        BigDecimal temp = new BigDecimal(principalAmountValue.toString());

        for (int i = 0; i < eachTermPrincipalAmount.length ; i++){

            if (temp.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }

            if (eachTermPrincipalAmount[i].compareTo(BigDecimal.ZERO) == 0){
                array[i] = BigDecimal.ZERO;
            }else if (eachTermPrincipalAmount[i].compareTo(temp) >= 0){
                array[i] = temp;
                temp = temp.subtract(eachTermPrincipalAmount[i]);
            }else {
                array[i] = eachTermPrincipalAmount[i];
                temp = temp.subtract(eachTermPrincipalAmount[i]);
            }
        }
        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }
    /**
     * 减免期数及金额
     *
     */
    private void principalBreaksAmountAndTerm(InstallParameterDTO installParameter, FeeTypeDTO feeTypeDTO) {

        //减免期数
        Integer term = installParameter.getInstallOrder().getTerm();

        //本金减免金额
        BigDecimal principalAmountValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalAmountValue()).orElse(BigDecimal.ZERO);
        //本金减免期数
        BigDecimal principalTermValue = Optional.ofNullable(installParameter.getInstallFeeCode().getPrincipalTermValue()).orElse(BigDecimal.ZERO);

        int len = principalTermValue.intValue() > installParameter.getInstallOrder().getTerm()
                ? installParameter.getInstallOrder().getTerm() : principalTermValue.intValue();


        BigDecimal[] array = new BigDecimal[term];
        Arrays.fill(array,BigDecimal.ZERO);

        //每期-本金分期费用金额
        for (int i = 0 ; i < len ; i++) {

            if (principalAmountValue.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }

            if (feeTypeDTO.getPrincipalEachTermFeeAmount()[i].compareTo(BigDecimal.ZERO) == 0){
                continue;
            }

            if (principalAmountValue.compareTo(feeTypeDTO.getPrincipalEachTermFeeAmount()[i]) <= 0){
                array[i] = principalAmountValue;
            }else{
                array[i] = feeTypeDTO.getPrincipalEachTermFeeAmount()[i];
            }
            principalAmountValue = principalAmountValue.subtract(array[i]);
        }

        //减免期数
        feeTypeDTO.setPrincipalDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());

        //本金减免金额
        feeTypeDTO.setPrincipalDerateFeeAmount(array);

    }
    private List<BigDecimal> calculateInterestAmortize(InstallParameterDTO installParameter,
                                                       Integer term) {

        logger.info("利息总金额为{}", installParameter.getInstallOrder().getInstallmentTotalInterest());
        if (installParameter.getInstallOrder().getInstallmentTotalInterest() == null
                || installParameter.getInstallOrder().getInstallmentTotalInterest().compareTo(BigDecimal.ZERO) == 0){
            BigDecimal[] arrayList = new BigDecimal[term];
            Arrays.fill(arrayList,BigDecimal.ZERO);

            return Arrays.asList(arrayList);
        }


        InstallmentInterestDTO interestDTO = InstallmentInterestDTO
                .InstallmentInterestDTOBuilder
                .anInstallmentInterestDTO()
                .withInterestAmount(installParameter.getInstallOrder().getInstallmentTotalInterest())
                .withTerm(term)
                .build();

        List<BigDecimal> monthlyInterestAmount = new Rule78Interest().getInterestResult(interestDTO).getMonthlyInterestAmount();

        log.info("利息摊销金额为{} ",monthlyInterestAmount);
        return monthlyInterestAmount;
    }
    private ImmutablePair<BigDecimal, BigDecimal> calculateFeeAmortize(Integer term,
                                                                       String feeReceiveFlag,
                                                                       FeeTypeDTO feeTypeDTO,
                                                                       InstallParameterDTO installParameter) {
        BigDecimal termdb = new BigDecimal(term);
        BigDecimal termAmortize = BigDecimal.ZERO;
        BigDecimal firstAmortize = BigDecimal.ZERO;
        //手续费收取方式为F-期初一次性收取 E-期末一次性收取
        if (FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeReceiveFlag) || FeeReceiveFagEnum.END_ONE_TIME.getCode().equals(feeReceiveFlag)) {
            /*
               手续费分摊处理
               首期减免费用
               总的摊销费用 = 首期总的费用-首期减免费用
             */

            BigDecimal totalAmortize;
            if (!Objects.equals(InstallDerateMethodEnum.EACH_DISCOUNT.getCode(), feeTypeDTO.getDerateMethod())) {
                totalAmortize = feeTypeDTO.getTotalCost().subtract(feeTypeDTO.getDerateFeeAmount()[0]);
            } else {
                totalAmortize = feeTypeDTO.getTotalCost();
            }

            //光大POC, 如果分期手续费交易码的增值税标志为0：拆税，则用交易码读取增值税参数表获取增值税税率
            BigDecimal vatRate = BigDecimal.ZERO;
            boolean vatFlag = false;
            if (installParameter.getInstallAccountTran() != null) {
                String feeCode = installParameter.getInstallAccountTran().getFeeTransactionCode();
                TransactionCodeResDTO transactionCode = findParamTransactionCode(feeCode,
                        installParameter.getInstallOrder().getOrganizationNumber());
                if (transactionCode != null) {
                    String taxFlag = transactionCode.getTaxFlag();
                    if ("0".equals(taxFlag)) {
//                        InstallOrderDTO installOrderDTO = installParameter.getInstallOrder();
//                        if (installOrderDTO != null && installOrderDTO.getAccountManagementId() != null) {
//                            AccountManagementInfoDTO accountManagementInfoDTO = BeanMapping.copy(accountManagementInfoMapper.selectByPrimaryKey(installOrderDTO.getAccountManagementId()), AccountManagementInfoDTO.class);
//                            因为管理账户id为空 所以查询增值税率参数表的BRANCHID条件暂时写死
                        TPmsGlamtr tPmsGlamtr = tPmsGlamtrSelfMapper.selectByTxnCodeAndFinanceStatus(
                                installParameter.getInstallOrder().getOrganizationNumber(), "110110", feeCode);
                        if (tPmsGlamtr != null && tPmsGlamtr.getTaxRate() != null) {
                            //成功取得增值税税率
                            vatRate = new BigDecimal(tPmsGlamtr.getTaxRate());
                            vatFlag = true;
                        }
                    }
                }
            }

            if (vatFlag) {
                BigDecimal tenThousand = new BigDecimal("10000");
                /*
                 * 正向公式是：价金额+税金额=总额，假设6%税率
                 * 税金额=价金额*6%
                 * 推导：
                 * 价金额+价金额*6%=总金额，
                 * 价金额=总金额/(1+6%)
                 * 四舍五入
                 */
                totalAmortize = totalAmortize.divide(vatRate.divide(tenThousand).add(BigDecimal.ONE), 2, BigDecimal.ROUND_HALF_UP);
            }

            //每期摊销费用 = 总的摊销费用/分期期数
            termAmortize = totalAmortize.divide(termdb, 2, BigDecimal.ROUND_DOWN);
            //第一期摊销费用 =总的摊销费用-每期摊销费用*（分期期数 - 1）
            firstAmortize = totalAmortize.subtract(termAmortize.multiply(termdb.subtract(BigDecimal.ONE)));
        }
        return ImmutablePair.of(firstAmortize, termAmortize);

    }
    public TransactionCodeResDTO findParamTransactionCode(String transactionCode, String organizationNumber) {
        TransactionCodeResDTO result = transactionCodeService.findTransactionCode(organizationNumber, transactionCode);
        if (result == null) {
            logger.error("根据机构号、交易码查询交易码参数失败, organizationNumber:{}, transactionCode:{}", organizationNumber, transactionCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TRANS_CODE_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
}
