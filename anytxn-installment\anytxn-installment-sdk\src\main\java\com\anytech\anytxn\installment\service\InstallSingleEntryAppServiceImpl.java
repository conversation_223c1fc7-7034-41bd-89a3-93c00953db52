package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.monetary.service.CustReconciliationControlServiceImpl;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallNoFinTranServcie;
import com.anytech.anytxn.installment.base.service.IInstallSingleEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListServiceApp;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallTypeSupportTxnResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallTypeSupportTxnService;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMccSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportMerchantSelfMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallTypeSupportTxnSelfMapper;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMerchant;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单笔分期业务逻辑实现
 *
 * <AUTHOR>
 * @date 2019/8/19
*/
@Service
public class InstallSingleEntryAppServiceImpl implements IInstallSingleEntryAppService {

    private static final Logger logger = LoggerFactory.getLogger(InstallSingleEntryAppServiceImpl.class);

    @Autowired
    private IInstallNoFinTranServcie installNoFinTranServcie;

    @Autowired
    private IInstallStagingListServiceApp installStagingListService;

    @Autowired
    private IInstallEntryAppService installEntryAppService;

    @Autowired
    private CustReconciliationControlServiceImpl custReconciliationControlService;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInstallTypeSupportTxnService installTypeSupportTxnService;
    @Autowired
    private PostedTransactionMapper postedTransactionMapper;
    @Autowired
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;
    @Autowired
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;
    @Autowired
    private InstallTypeSupportMerchantSelfMapper installTypeSupportMerchantSelfMapper;
    @Autowired
    private InstallManager installManager;
    /**
     * 根据账号查询单笔分期
     *
     * @param accountManagementId 管理账户id
     * @return {@link SingleInstallAppDTO}
     */
    @Override
    public List<SingleInstallAppDTO> findByAccountManagementId(String accountManagementId, String installFlag) {
        List<SingleInstallAppDTO> singleInstallDTOList = new ArrayList<>();
        //必输项检查
        if (accountManagementId == null || "".equals(accountManagementId)) {
            logger.error("Single installment query - account management ID cannot be null or empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_QU);
        }
        SingleInstallSearchKeyDTO singleInstallSearchKeyDTO = new SingleInstallSearchKeyDTO();
        singleInstallSearchKeyDTO.setOrganizationNumber(OrgNumberUtils.getOrg());
        singleInstallSearchKeyDTO.setAccountManagementId(accountManagementId);
        List<SingleInstallAppDTO> singleInstallDtos = new ArrayList<>();
        if (InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode().equals(installFlag)){
            singleInstallDtos =  BeanMapping.copyList(installNoFinTranServcie.singleInstallTranQuery(singleInstallSearchKeyDTO),SingleInstallAppDTO.class);
        }
        List<String> installTypeTransCodes =null;
        try {
            //单笔分期
            if (InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode().equals(installFlag)){
                logger.info("Calling installTypeSupportTxnService.getByTypeAndOrgNum with type={}, organizationNumber={}",
                        InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
                List<InstallTypeSupportTxnResDTO> installTypeSupportTxns = installTypeSupportTxnService.getByTypeAndOrgNum(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
                if (!CollectionUtils.isEmpty(installTypeSupportTxns)){
                    installTypeTransCodes = installTypeSupportTxns.stream().map(InstallTypeSupportTxnResDTO::getTransactionCode).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        //不支持分期的MCC
        List<InstallTypeSupportMcc> installTypeMccs = installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
        List<String> mccList = new ArrayList<>();
        if(installTypeMccs !=null && !installTypeMccs.isEmpty()){
            mccList = installTypeMccs.stream().map(InstallTypeSupportMcc::getMcc).collect(Collectors.toList());
        }
        //不支持分期的商户号
        List<InstallTypeSupportMerchant> installTypeMerchants = installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode(), singleInstallSearchKeyDTO.getOrganizationNumber());
        List<String> merchantList = new ArrayList<>();
        if(installTypeMerchants !=null && !installTypeMerchants.isEmpty()){
            merchantList = installTypeMerchants.stream().map(InstallTypeSupportMerchant::getMerchantId).collect(Collectors.toList());
        }

        if (singleInstallDtos.isEmpty()) {
            //如果singleInstallDTOS为空说明该账号已分期
        } else {
            for (SingleInstallAppDTO s : singleInstallDtos) {
                boolean txnFlag = true;
                if(!CollectionUtils.isEmpty(installTypeTransCodes)){
                    txnFlag = installTypeTransCodes.contains(s.getPostingTransactionCode());
                }
                //只筛选列表返回的已入账交易
                if (Objects.equals(s.getPostFlag(), InstallPostFlagEnum.ALREADY_BOOKED.getCode()) && txnFlag && !mccList.contains(s.getMcc()) && !merchantList.contains(s.getMerchantId())) {
                    if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installFlag)){
                        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(s.getOriginTransactionId());
                        if (!ObjectUtils.isEmpty(postedTransaction)){
                            String retrievalReferenceNumber = postedTransaction.getRetrievalReferenceNumber();
                            if (StringUtils.isNotEmpty(retrievalReferenceNumber) && InstallmentConstant.RETRIEVAL_REFERENCE_NUMBER.equals(retrievalReferenceNumber)){
                                s.setMerchantName(postedTransaction.getMerchantName());
                                singleInstallDTOList.add(s);
                            }
                        }
                    }else if (InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode().equals(installFlag)){
                        PostedTransaction postedTransactionKey = postedTransactionMapper.selectByPrimaryKey(s.getOriginTransactionId());
                        s.setMerchantName(postedTransactionKey==null?null:postedTransactionKey.getMerchantName());
                        singleInstallDTOList.add(s);
                    }
                }
            }
        }
        return singleInstallDTOList;
    }

    /**
     * 单笔分期列表查询
     * @return InstallTradingDTO
     */
    @Override
    public List<InstallTradingDTO> findInstallSingleList(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (installTradingSearchKeyDTO == null) {
            logger.error("Single installment list query - parameter cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_Q);
        }
        //必输项检查
        checkInstallTrading(installTradingSearchKeyDTO);
        installTradingSearchKeyDTO.setOrganizationNumber(installTradingSearchKeyDTO.getOrganizationNumber());
        if (InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode());
        }
        if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.RESET_INSTALL.getCode());
        }
        //installTradingSearchKeyDTO.setPaymentWay(InstallPaymentWayEnum.WAIT_FEE.getCode())
        //installTradingSearchKeyDTO.setFeeFlag(InstallFeeReceiveFlagEnum.HIRE_CHARGE.getCode())
        installTradingSearchKeyDTO.setTerm(0);
        installTradingSearchKeyDTO.setInstallAmount(installTradingSearchKeyDTO.getInstallAmount()
                .setScale(2, BigDecimal.ROUND_HALF_UP));

        logger.info("Calling installStagingListService.findInstallTradingByOptions with originTransactionId={}", installTradingSearchKeyDTO.getOriginTransactionId());
        return installStagingListService.findInstallTradingByOptions(installTradingSearchKeyDTO);
    }

    /**
     * 单笔分期交易录入
     *
     * @param installEntryDTO {@link InstallEntryDTO}
     * @return Long
     */
    @Override
    public InstallEntryResDTO singleInstallment(InstallEntryDTO installEntryDTO) {
        if(logger.isInfoEnabled()){
            logger.info("installEntryDTO:{}", DesensitizedUtils.getJson(installEntryDTO));
        }

        if (installEntryDTO == null) {
            logger.error("Single installment entry - installEntryDTO cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_TR);
        }
        //必输项检查
        if (StringUtils.isBlank(installEntryDTO.getAccountManagementId())) {
            logger.error("Single installment entry - account management ID cannot be null or empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SU_IN_MA);
        }

        if (StringUtils.isBlank(installEntryDTO.getProductCode())) {
            logger.error("Single installment entry - product code cannot be null or empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_PR);
        }

        if (installEntryDTO.getInstallAmount() == null) {
            logger.error("Single installment entry - install amount cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_AM);
        }

        if (StringUtils.isBlank(installEntryDTO.getOriginTransactionId())) {
            logger.error("Single installment entry - origin transaction ID cannot be null or empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_OR_ID);
        }

        logger.info("Calling organizationInfoService.findOrganizationInfo with organizationNumber={}", installEntryDTO.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installEntryDTO.getOrganizationNumber());
        if(organizationInfo == null){
            logger.error("Organization info not found for organizationNumber={}", installEntryDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }

        String cardNumber = installManager.getCardNumberByAccountManagementId(installEntryDTO);

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installEntryDTO.getAccountManagementId());
        logger.info("Calling custReconciliationControlService.getControl with customerId={}, organizationNumber={}",
                accountManagementInfo.getCustomerId(), organizationInfo.getOrganizationNumber());
        CustReconciliationControlDTO control = custReconciliationControlService.getControl(accountManagementInfo.getCustomerId(), organizationInfo.getOrganizationNumber());
        logger.info("Calling custReconciliationControlService.getBillingDate");
        LocalDate date = custReconciliationControlService.getBillingDate(control, organizationInfo.getAccruedThruDay(), organizationInfo.getToday(), organizationInfo.getNextProcessingDay());
        // 交易日期计算所得
        installEntryDTO.setTransactionDate(date);
        installEntryDTO.setFunctionCode(InstallEntryFunctionCodeEnum.INSTLL_ENTRY.getCode());
        installEntryDTO.setOrganizationNumber(installEntryDTO.getOrganizationNumber());
        installEntryDTO.setInstallType(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode());
        installEntryDTO.setInstallCcy(accountManagementInfo.getCurrency());
        installEntryDTO.setInstallPriceFlag(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode());
        installEntryDTO.setInstallTotalFee(BigDecimal.ZERO);
        installEntryDTO.setInstallFeeRate(BigDecimal.ZERO);
        installEntryDTO.setInstallDerateMethod(InstallmentDerateMethodEnum.REDUCTION_NO.getCode());
        installEntryDTO.setInstallDerateValue(BigDecimal.ZERO);
        installEntryDTO.setCardNumber(cardNumber);

        installManager.getInstallmentProductDesc(installEntryDTO);

        String transactionId = installEntryDTO.getOriginTransactionId();
        PostedTransaction pt = postedTransactionMapper.selectByPrimaryKey(transactionId);
        String merchantId = pt.getMerchantId();
        installEntryDTO.setMerchantId(merchantId);
        installEntryDTO.setMerchantName(pt.getMerchantName());

        logger.info("Calling installEntryAppService.entry with originTransactionId={}, installAmount={}",
                installEntryDTO.getOriginTransactionId(), installEntryDTO.getInstallAmount());
        String orderId = installEntryAppService.entry(installEntryDTO).getOrderId();

        if (StringUtils.isBlank(orderId)){
            logger.error("card number {}  installment failure ",
                    installEntryDTO.getCardNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_BILL_INST_ENTRY_FAIL_FAULT);
        }

        return InstallEntryResDTO.InstallEntryResDTOBuilder.anInstallEntryResDTO()
                .withCardNumber(cardNumber)
                .withOrderId(orderId).build();
    }

    /**
     * 必输项检查
     *
     * @param installTradingSearchKeyDTO {@link InstallTradingSearchKeyDTO}
     */
    private void checkInstallTrading(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getOriginTransactionId())) {
            logger.error("Transaction ID cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_IN);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getAccountManagementId())) {
            logger.error("Management account ID cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_QM);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getCardNumber())) {
            logger.error("Card number cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_CA);
        }

        if (installTradingSearchKeyDTO.getInstallAmount() == null) {
            logger.error("Install amount cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_AM);
        }
    }
}
