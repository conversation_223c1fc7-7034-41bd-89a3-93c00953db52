package com.anytech.anytxn.authorization.service.channel.express;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.constants.InstallmentPriceFlagEnum;
import com.anytech.anytxn.authorization.base.enums.AuthServicePointConditionCodeEnum;
import com.anytech.anytxn.authorization.base.enums.MTIEnum;
import com.anytech.anytxn.authorization.base.enums.PreAuthTransTypeEnum;
import com.anytech.anytxn.authorization.base.enums.TranTypeDetailEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.express.IExpressTransPreprocessService;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.service.rule.IRuleService;
import com.anytech.anytxn.authorization.base.utils.VerifyDateUtil;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.authorization.enums.ReversalTypeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020 10 23
 */
@Service
public class ExpressTransPreprocessServiceImpl implements IExpressTransPreprocessService {

    private static final Logger logger = LoggerFactory.getLogger(ExpressTransPreprocessServiceImpl.class);

    @Resource
    private IRuleService ruleService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     * 中国银联银行卡交换系统技术规范 第2部分 报文接口规范》域25服务点条件检查，定义范围
     */
    private static final List<String> SEVER_CODE_LIST =
            Arrays.asList("00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "17",
                    "18", "28", "41", "42", "43", "44", "45", "60", "64", "65", "66", "67", "68", "69", "82", "83", "91");
    private static final List<String> MTI_START_VALUE = Arrays.asList("01", "02", "03");

    /**
     * 授权字段校验，交易识别，组装接口
     *
     * @param iso8583Bo iso8583Bo
     * @return AuthRecordedDTO
     */
    @Override
    public AuthRecordedDTO preProcessAuthTrans(ISO8583DTO iso8583Bo) {
        // 字段校验
        boolean bool = checkAuth(iso8583Bo.getFieldMap(), iso8583Bo.getMTI());
        if (!bool) {
            logger.error("Field validation failed");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL);
        }
        // mti值判断
        String mti = iso8583Bo.getMTI();
        Map<Integer, String> isoMap = iso8583Bo.getFieldMap();
        boolean mtiCheck = MTIEnum.AUTH_REQUEST.getCode().equals(mti) || MTIEnum.FINACIAL_REQUEST.getCode().equals(mti)
                || MTIEnum.AUTH_NOTICE.getCode().equals(mti) || MTIEnum.ALL_FINACIAL_NOTICE.getCode().equals(mti)
                || MTIEnum.REVERSAL_TRANS.getCode().equals(mti) || MTIEnum.REVERSAL_NOTICE.getCode().equals(mti);
        if (!mtiCheck) {
            logger.error("MTI check failed: mti={}", mti);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_MTI_CHECK_FAIL);
        }
        // 交易识别,20开头,冲正,撤销,直接跳过交易识别
        boolean transCheck =
                (AuthConstans.MTI_FIRST_TWO_VALUE.equals(isoMap.get(Constants8583Field.FIELD3).substring(0, 2)))
                        || (AuthConstans.MTI_FIRST_TWO_VALUE_17.equals(isoMap.get(Constants8583Field.FIELD3).substring(0, 2)))
                        || MTIEnum.REVERSAL_NOTICE.getCode().equals(mti);
        Map<String, String> ruleResultMap = null;
        if (!transCheck) {
            ruleResultMap = expressTransIdentify(isoMap, mti, OrgNumberUtils.getOrg(), iso8583Bo);
        }
        // 接口组装
        return buildAuthRecorded(iso8583Bo, ruleResultMap, mti);
    }

    /**
     * 2.1 字段校验
     * @return boolean
     */
    private boolean checkAuth(Map<Integer, String> map, String mti) {
        // 冲正 撤销冲正交易判断
        String firstTwo = map.get(Constants8583Field.FIELD3).substring(0, 2);
        boolean revocationReversalFlag =
                MTIEnum.REVERSAL_NOTICE.getCode().equals(mti) || "20".equals(firstTwo) || "17".equals(firstTwo);

        /* 1.域2主账号检查不能为空,且为数字型,长度必须为16位或19位,否则域39应答码赋值“30” */
        String field2 = map.get(Constants8583Field.FIELD2);
        boolean checkFlag = StringUtils.isBlank(field2) || !StringUtils.isNumeric(field2)
                || (field2.length() != 16 && field2.length() != 19 && field2.length() != 15);
        if (checkFlag) {
            //throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D2, field2);
        }
        /* 2.域3交易处理码检查不能为空,且为数字型,前2位须在规范范围内 */
        String field3 = map.get(Constants8583Field.FIELD3);
        checkFlag = StringUtils.isBlank(field3) || !StringUtils.isNumeric(field3)
                || !VerifyDateUtil.isTwoNumber(field3.substring(0, 2));
        if (checkFlag) {
            logger.error("Field 3 validation failed: field3={}", field3);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D3, field3);
        }
        /* 3.域4交易金额检查不能为空,且为数字型,必须大于等于0,否则域39应答码赋值“30” */
        // 4号域可以为空 做查询交易
        String field4 = map.get(Constants8583Field.FIELD4);
        checkFlag = StringUtils.isNotBlank(field4)
                && (!StringUtils.isNumeric(field4) || new BigDecimal(field4).compareTo(BigDecimal.ZERO) < 0);
        if (checkFlag) {
            logger.error("Field 4 validation failed: field4={}", field4);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D4, field4);
        }
        /* 4.域6持卡人扣账金额检查可以为空,如果不为空,必须为数字型且大于等于0,否则域39应答码赋值“30” */
        String field6 = map.get(Constants8583Field.FIELD6);
        checkFlag = StringUtils.isNotBlank(field6)
                && (!StringUtils.isNumeric(field6) || new BigDecimal(field6).compareTo(BigDecimal.ZERO) < 0);
        if (checkFlag) {
            logger.error("Field 6 validation failed: field6={}", field6);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D6, field6);
        }
        /* 5.域7交易传输时间检查格式:MMDDhhmmss,可以为空,如果不为空,必须为日期时间格式,否则域39应答码赋值“30” */
        String field7 = map.get(Constants8583Field.FIELD7);
        if (StringUtils.isNotBlank(field7) && !VerifyDateUtil.isDateMmddhhmmss(field7)) {
            logger.error("Field 7 validation failed: field7={}", field7);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D7, field7);
        }
        /* 6.域11系统跟踪号可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30” */
        String field11 = map.get(Constants8583Field.FIELD11);
        if (StringUtils.isNotBlank(field11) && !StringUtils.isNumeric(field11)) {
            logger.error("Field 11 validation failed: field11={}", field11);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D11, field11);
        }

        /* 7.域14卡片有效期格式:YYMM不能为空,且为数字型,必须为日期格式,否则域39应答码赋值“30” */
        // 撤销 冲正 撤销冲正 有效期时间可以不填
        if (!revocationReversalFlag) {
            String field14 = map.get(Constants8583Field.FIELD14);
            if (StringUtils.isNotBlank(field14) && !VerifyDateUtil.isDateYymm(field14)) {
                logger.error("Field 14 validation failed: field14={}", field14);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D14, field14);
            }
        }

        /* 8.域18商户类型检查如果域1为01xx、02xx、04xx，此域不能为空，且为数字型，否则域39应答码赋值“30” */
        String field18 = map.get(Constants8583Field.FIELD18);
        checkFlag = (MTI_START_VALUE.contains(mti.substring(0, 2)))
                && (StringUtils.isBlank(field18) || !StringUtils.isNumeric(field18));
        if (checkFlag) {
            logger.error("Field 18 validation failed: field18={}", field18);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D18, field18);
        }
        /*
         * 9.域22服务点输入方式码检查格式:2位PAN + 1位PIN不能为空,且为数字型,前2位须在PAN定义中,否则域39应答码赋值“30” PAN定义可参考《中国银联银行卡交换系统技术规范 第2部分
         * 报文接口规范》6.19小节
         */
        String field22 = map.get(Constants8583Field.FIELD22);
        if (StringUtils.isBlank(field22) || !StringUtils.isNumeric(field22)
                || !VerifyDateUtil.isTwoNumber(field22.substring(0, 2))) {
            logger.error("Field 22 validation failed: field22={}", field22);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D22, field22);
        }
        /*
         * 10.域25服务点条件检查不能为空,且为数字型,须在服务点条件码定义中,否则域39应答码赋值“30” 服务点条件码定义可参考《中国银联银行卡交换系统技术规范 第2部分 报文接口规范》6.21小节
         */
        String field25 = map.get(Constants8583Field.FIELD25);
        if (StringUtils.isBlank(field25) || !StringUtils.isNumeric(field25) || !SEVER_CODE_LIST.contains(field25)) {
            logger.error("Field 25 validation failed: field25={}", field25);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D25, field25);
        }
        /* 11.域37检索参考号检查 可以为空，如果不为空，必须为数字型，否则域39应答码赋值“30” */
        String field37 = map.get(Constants8583Field.FIELD37);
        if (StringUtils.isNotBlank(field37) && !StringUtils.isNumeric(field37)) {
            logger.error("Field 37 validation failed: field37={}", field37);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D37, field37);
        }
        /* 12.域49交易货币代码检查不能为空,且为数字型,否则域39应答码赋值“30” */
        String field49 = map.get(Constants8583Field.FIELD49);
        if (StringUtils.isBlank(field49) || !StringUtils.isNumeric(field49)) {
            logger.error("Field 49 validation failed: field49={}", field49);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D49, field49);
        }
        /* 13.域51持卡人账户货币代码可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30” */
        String field51 = map.get(Constants8583Field.FIELD51);
        if (StringUtils.isNotBlank(field51) && !StringUtils.isNumeric(field51)) {
            logger.error("Field 51 validation failed: field51={}", field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D51,field51);
        }
        /*
         * 如果6号域即授权接口 authCardholderBillingAmount 不为空， 则51号域即授权接口中 authBillingCurrencyCode 不能为空，否则应答码赋值为96
         */
        if (StringUtils.isNotBlank(field6) && StringUtils.isBlank(field51)) {
            logger.error("Field 6 and 51 validation failed: field6={}, field51={}", field6, field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL,
                    AuthRepDetailEnum.D6E_D51);
        }
        /*
         * 如果51号域即授权接口 authBillingCurrencyCode 不为空， 则6号域即授权接口中 authCardholderBillingAmount 不能为空，否则应答码赋值为96
         */
        if(StringUtils.isBlank(field6) && StringUtils.isNotBlank(field51)){
            logger.error("Field 6 and 51 validation failed: field6={}, field51={}", field6, field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL,
                    AuthRepDetailEnum.D51E_D6);
        }
        return true;
    }

    /**
     * 2.3 交易识别方式
     * @return map
     */
    private Map<String, String> expressTransIdentify(Map<Integer, String> isoMap, String mti, String organizationNumber, ISO8583DTO iso8583Bo) {
        Map<String, Object> ruleParam = new HashMap<>(16);
        ruleParam.put(AuthConstans.AUTH_SERVICE_POINT_CONDITION_CODE,isoMap.get(Constants8583Field.FIELD25));
        ruleParam.put(AuthConstans.AUTH_MSG_TYPE_ID,mti);
        ruleParam.put(AuthConstans.AUTH_PROCESS_CODE,isoMap.get(Constants8583Field.FIELD3));
        ruleParam.put("authMerchantType",isoMap.get(Constants8583Field.FIELD18));
        ruleParam.put("authServicePointPinCode",isoMap.get(Constants8583Field.FIELD22));
        //添加一个服务点条件码作为预授权交易识别

        if (!StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD48))
                && isoMap.get(Constants8583Field.FIELD48).length() >= 2) {
            ruleParam.put("privateDomainInfoFour",isoMap.get(Constants8583Field.FIELD48).substring(0,2));
        }
        if (!StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD48))
                && isoMap.get(Constants8583Field.FIELD48).length() >= 9) {
            ruleParam.put("privateDomainInfoOne",isoMap.get(Constants8583Field.FIELD48).substring(0,3));
            ruleParam.put("privateDomainInfoTwo",isoMap.get(Constants8583Field.FIELD48).substring(3,6));
            ruleParam.put("privateDomainInfoThree",isoMap.get(Constants8583Field.FIELD48).substring(6,9));
            if(isoMap.get(Constants8583Field.FIELD48).length() >= 12){
                ruleParam.put("privateDomainInfoFive",isoMap.get(Constants8583Field.FIELD48).substring(9,12));
            }
        }

        if(!StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD33))){
            //33号域：发送机构标识码
            ruleParam.put("authForwardingIdentificationCode",isoMap.get(Constants8583Field.FIELD33));
        }
        if(!StringUtils.isBlank(isoMap.get(Constants8583Field.FIELD60))){
            //60.2.5号域：终端类型
            ruleParam.put("authTerminalType",isoMap.get(Constants8583Field.FIELD60).substring(8,10));
            //60.2.9号域：交互方式标志
            ruleParam.put("authInteractiveModeFlag",isoMap.get(Constants8583Field.FIELD60).substring(14,15));
            //60.3.6号域：交易介质
            ruleParam.put("authTransactionMedia",isoMap.get(Constants8583Field.FIELD60).substring(23,24));
        }
        //移动支付标识
        if(StringUtils.isNotBlank(isoMap.get(Constants8583Field.FIELD57))){
            ruleParam.put("authTxnData",isoMap.get(Constants8583Field.FIELD57).substring(0,2));
        }

        ruleParam.put("sourceCode", iso8583Bo.getSourceCode());
        ruleParam.put("acceptorTerminalCode", isoMap.get(Constants8583Field.FIELD41));

        DataInputDTO dataInputDTO = new DataInputDTO();
        dataInputDTO.setRuleType(AuthConstans.TRANS_EXPRESS_IDENTIFY_RULE);
        dataInputDTO.setInput(ruleParam);
        dataInputDTO.setOrganizationNumber(organizationNumber);

        logger.info("Express transaction identification input parameters: ruleType={}, organizationNumber={}", 
                   dataInputDTO.getRuleType(), dataInputDTO.getOrganizationNumber());
        logger.info("Calling ruleService.executeRule");
        Map<String, String> result = ruleService.executeRule(dataInputDTO);
        logger.info("Completed ruleService.executeRule");
        return result;
    }

    /**
     * 组件接口
     * @return AuthRecordedDTO
     */
    private AuthRecordedDTO buildAuthRecorded(ISO8583DTO iso8583Bo, Map<String, String> ruleResultMap, String mti) {
        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        if (ruleResultMap != null) {

            String authTransactionTypeTopCode = ruleResultMap.get(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE);
            authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionTypeTopCode);

            String authTransactionTypeDetailCode = ruleResultMap.get(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE);
            authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetailCode);

            String postingTransactionCode = ruleResultMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE);
            authRecordedDTO.setPostingTransactionCode(postingTransactionCode);

            String postingTransactionCodeDev = ruleResultMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV);
            authRecordedDTO.setPostingTransactionCodeRev(postingTransactionCodeDev);
        }

        authRecordedDTO.setAuthGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        authRecordedDTO.setAuthMessageTypeId(mti);

        Map<Integer, String> map = iso8583Bo.getFieldMap();
        authRecordedDTO.setAuthCardNumber(map.get(Constants8583Field.FIELD2));
        authRecordedDTO.setAuthProcessingCode(map.get(Constants8583Field.FIELD3));

        // 查询交易 4号域可以为空
        // 4号域可以为空
        boolean boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD4))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD4));
        authRecordedDTO.setAuthTransactionAmount(boolField ? BigDecimal.ZERO
                : new BigDecimal(map.get(Constants8583Field.FIELD4)).divide(new BigDecimal(AuthConstans.DIVIDE_100)));

        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD6))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD6));

        authRecordedDTO.setAuthCardholderBillingAmount(boolField ? BigDecimal.ZERO
                : new BigDecimal(map.get(Constants8583Field.FIELD6)).divide(new BigDecimal(AuthConstans.DIVIDE_100)));

        authRecordedDTO.setAuthTransmissionTime(map.get(Constants8583Field.FIELD7));
        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD10))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD10));
        authRecordedDTO.setAuthCardholderBillingRate(
                boolField ? BigDecimal.ZERO : new BigDecimal(map.get(Constants8583Field.FIELD10)));
        authRecordedDTO.setAuthSystemTraceAuditNumber(map.get(Constants8583Field.FIELD11));
        authRecordedDTO.setAuthLocalTransactionTime(map.get(Constants8583Field.FIELD12));
        authRecordedDTO.setAuthLocalTransactionDate(map.get(Constants8583Field.FIELD13));

        // 有效期
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD14))) {
            authRecordedDTO.setAuthCardExpirationDate(map.get(Constants8583Field.FIELD14));
        } else {
            String cardExpireDate = map.get(Constants8583Field.FIELD35) == null ? null
                    : map.get(Constants8583Field.FIELD35).substring(17, 21);
            authRecordedDTO.setAuthCardExpirationDate(cardExpireDate);
        }

        authRecordedDTO.setAuthSettlementDate(map.get(Constants8583Field.FIELD15));
        // 18号域
        authRecordedDTO.setAuthMerchantType(map.get(Constants8583Field.FIELD18));

        authRecordedDTO.setAuthMerchantCountryCode(map.get(Constants8583Field.FIELD19));
        authRecordedDTO.setAuthServicePointCardCode(StringUtils.isBlank(map.get(Constants8583Field.FIELD22)) ? null
                : map.get(Constants8583Field.FIELD22).substring(0, 2));
        authRecordedDTO.setAuthServicePointPinCode(StringUtils.isBlank(map.get(Constants8583Field.FIELD22)) ? null
                : map.get(Constants8583Field.FIELD22).substring(2, 3));
        authRecordedDTO.setAuthCardSequenceNumber(map.get(Constants8583Field.FIELD23));
        authRecordedDTO.setAuthServicePointConditionCode(map.get(Constants8583Field.FIELD25));
        authRecordedDTO.setAuthTransactionFeeIndicator(StringUtils.isBlank(map.get(Constants8583Field.FIELD28)) ? null
                : map.get(Constants8583Field.FIELD28).substring(0, 1));
        authRecordedDTO.setAuthTransactionFee(StringUtils.isBlank(map.get(Constants8583Field.FIELD28)) ? null
                : map.get(Constants8583Field.FIELD28).substring(1, 9));
        authRecordedDTO.setAuthAcquiringIdentificationCode(map.get(Constants8583Field.FIELD32));
        authRecordedDTO.setAuthForwardingIdentificationCode(map.get(Constants8583Field.FIELD33));
        authRecordedDTO.setAuthTrack2Data(map.get(Constants8583Field.FIELD35));
        authRecordedDTO.setAuthRetrievalReferenceNumber(map.get(Constants8583Field.FIELD37));
        // 新增42域赋值
        authRecordedDTO.setMerchantId(map.get(Constants8583Field.FIELD42));
        // 新增43域赋值
        authRecordedDTO.setAuthCardAcceptorNameLocation(map.get(Constants8583Field.FIELD43));
        // 新增57号域赋值
        authRecordedDTO.setAuthAdditionalTxnData(map.get(Constants8583Field.FIELD57));

        authRecordedDTO.setAuthAuthIdentificationResponse(map.get(Constants8583Field.FIELD38));

        // 初始化为"00"
        authRecordedDTO.setAuthResponseCode(AuthConstans.AUTH_CHECK_RESPONSE_CODE);

        authRecordedDTO.setAuthCardAcceptorTerminalCode(map.get(Constants8583Field.FIELD41));
        authRecordedDTO.setAuthCardAcceptorIdentification(map.get(Constants8583Field.FIELD42));
        authRecordedDTO.setAuthCardAcceptorNameLocation(map.get(Constants8583Field.FIELD43));
        authRecordedDTO.setAuthTrack1Data(map.get(Constants8583Field.FIELD45));
        authRecordedDTO.setAuthTransactionCurrencyCode(map.get(Constants8583Field.FIELD49));
        authRecordedDTO.setAuthBillingCurrencyCode(map.get(Constants8583Field.FIELD51));
        authRecordedDTO.setAuthPinData(map.get(Constants8583Field.FIELD52));
        authRecordedDTO.setAuthSecurityRelatedControlInformation(map.get(Constants8583Field.FIELD53));
        authRecordedDTO.setEncryptionMethodUsed(StringUtils.isBlank(map.get(Constants8583Field.FIELD53)) ? null
                : map.get(Constants8583Field.FIELD53).substring(1, 2));
        authRecordedDTO.setAuthIccSystemRelatedData(map.get(Constants8583Field.FIELD55));


        authRecordedDTO.setAuthAccountIdentification1(map.get(Constants8583Field.FIELD102));
        authRecordedDTO.setAuthAccountIdentification2(map.get(Constants8583Field.FIELD103));

        authRecordedDTO.setOpponentAccountName(map.get(Constants8583Field.FIELD1231));

        authRecordedDTO.setOpponentTxnArea(map.get(Constants8583Field.FIELD1232));
        authRecordedDTO.setOpponentTxnArea1(map.get(Constants8583Field.FIELD12321));
        authRecordedDTO.setOpponentTxnArea2(map.get(Constants8583Field.FIELD12322));


        authRecordedDTO.setOpponentBankId(map.get(Constants8583Field.FIELD1233));
        authRecordedDTO.setMerchantInfo2ndCode(map.get(Constants8583Field.FIELD1234));
        authRecordedDTO.setMerchantInfo2ndName(map.get(Constants8583Field.FIELD1235));

        authRecordedDTO.setFinancialNetWorkData(map.get(Constants8583Field.FIELD63));

        //如果都不同 取第一个值
        if (!Objects.equals(authRecordedDTO.getAuthAccountIdentification2(),authRecordedDTO.getAuthCardNumber())){
            authRecordedDTO.setOpponentAccountNumber(authRecordedDTO.getAuthAccountIdentification2());
        }

        if (!Objects.equals(authRecordedDTO.getAuthAccountIdentification1(),authRecordedDTO.getAuthCardNumber())){
            authRecordedDTO.setOpponentAccountNumber(authRecordedDTO.getAuthAccountIdentification1());
        }


        // 57号域 移动支付识别
        // 2位标志（AP） + 22位DPAN数据（19位DPAN+2位状态+1位设备卡类型），DPAN不足19位长，后补空格）识别移动支付交易（前两位=AP）
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD55))) {
            authRecordedDTO.setMobilePayIndicator(map.get(Constants8583Field.FIELD55).substring(0, 2));
        }

        authRecordedDTO.setAuthAuthTypeCode("1");
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD60))) {
            String field60 = map.get(Constants8583Field.FIELD60);
            authRecordedDTO.setAuthTerminalType(field60.substring(8, 10));
            authRecordedDTO.setAuthInteractiveModeFlag(field60.substring(14, 15));
            authRecordedDTO.setAuthTransactionMedia(field60.substring(23, 24));
            authRecordedDTO.setPassPinInd(field60.substring(10,11));
        }
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD61))) {
            String field61 = map.get(Constants8583Field.FIELD61);
            //
            if (field61 != null && field61.length() >= 2) {
                authRecordedDTO.setAuthIdType(field61.substring(0, 2).trim());
            }
            if (field61 != null && field61.length() >= 22) {
                authRecordedDTO.setAuthIdNumber(field61.substring(2, 22).trim());
            }
            if (field61 != null && field61.length() >= 30) {
                authRecordedDTO.setAuthNoCardVerificationValue(field61.substring(27, 30));
            }
            if (field61 != null && field61.length() >= 37) {
                authRecordedDTO.setAuthFormatId(field61.substring(35, 37));
            }
            //AM 用法获取
            if (field61 != null && field61.length() >= 83) {
                authRecordedDTO.setAuthName(field61.substring(53,83).trim());
            }
            if (field61 != null && field61.length() >= 114) {
                authRecordedDTO.setAuthMobileNo(field61.substring(83,113).trim());
            }
        }
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD90))) {
            String field90 = map.get(Constants8583Field.FIELD90);
            authRecordedDTO.setAuthOriginalMessageTypeId(field90.substring(0, 4));
            authRecordedDTO.setAuthOriginalSystemTraceAuditNumber(field90.substring(4, 10));
            authRecordedDTO.setAuthOriginalTransmissionTime(field90.substring(10, 20));
            authRecordedDTO
                    .setAuthOriginalAcquiringIdentificationCode(Long.valueOf(field90.substring(20, 31)).toString());
            authRecordedDTO
                    .setAuthOriginalForwardingIdentificationCode(Long.valueOf(field90.substring(31, 42)).toString());
        }

        String firstTwo = map.get(Constants8583Field.FIELD3).substring(0, 2);
        // 预授权撤销 25号域06/18
        boolean preAuthFlag = AuthServicePointConditionCodeEnum.REVERSAL_TRANS.getCode()
                .equals(authRecordedDTO.getAuthServicePointConditionCode())
                || AuthServicePointConditionCodeEnum.REVOCATION_REVERSAL_TRANS.getCode()
                .equals(authRecordedDTO.getAuthServicePointConditionCode());
        // 0100 0120 0200 0220 普通交易 撤销交易
        if (MTIEnum.AUTH_REQUEST.getCode().equals(mti) || MTIEnum.AUTH_NOTICE.getCode().equals(mti)
                || MTIEnum.FINACIAL_REQUEST.getCode().equals(mti) || MTIEnum.ALL_FINACIAL_NOTICE.getCode().equals(mti)) {
            // 撤销
            if (AuthConstans.MTI_FIRST_TWO_VALUE.equals(firstTwo)
                    || AuthConstans.MTI_FIRST_TWO_VALUE_17.equals(firstTwo)) {
                // 撤销交易
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_TRANS.getCode());
                if (preAuthFlag) {
                    // 预授权撤销 0100
                    if (MTIEnum.AUTH_REQUEST.getCode().equals(mti)) {
                        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.CUP_PRE_AUTH.getCode());
                        authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_REVOCATION.getCode());
                    }
                    // 预授权完成撤销 0200
                    if (MTIEnum.FINACIAL_REQUEST.getCode().equals(mti)) {
                        authRecordedDTO
                                .setAuthTransactionTypeDetailCode(TranTypeDetailEnum.CUP_PRE_AUTH_FINISH.getCode());
                        authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_COMPLETED_REVOCATION.getCode());
                    }
                }
            } else {
                // 普通交易
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
                // 预授权 0100 03
                if (TranTypeDetailEnum.CUP_PRE_AUTH.getCode()
                        .equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
                    authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_REQUEST.getCode());
                }
                // 预授权完成 0200 00
                if (TranTypeDetailEnum.CUP_PRE_AUTH_FINISH.getCode()
                        .equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
                    authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_COMPLETED.getCode());
                }
                // 贷记确认
                if (TranTypeDetailEnum.DEBIT_CONFIRM.getCode()
                        .equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {

                }
            }
        }
        // 0400 0420 冲正交易
        if (MTIEnum.REVERSAL_TRANS.getCode().equals(mti) || MTIEnum.REVERSAL_NOTICE.getCode().equals(mti)) {
            // 撤销冲正
            if (AuthConstans.MTI_FIRST_TWO_VALUE.equals(firstTwo)
                    || AuthConstans.MTI_FIRST_TWO_VALUE_17.equals(firstTwo)) {
                // 预授权撤销冲正,预授权完成撤销冲正
                if (preAuthFlag) {
                    // 临时赋值R008 和 R009 不影响 后面重新赋值
                    authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.CUP_PRE_AUTH.getCode());
                }
                // 普通交易的撤销冲正
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
            } else {
                // 预授权冲正,预授权完成冲正
                if (preAuthFlag) {
                    // 预授权冲正
                    if (AuthConstans.MTI_FIRST_TWO_VALUE_03.equals(firstTwo)) {
                        authRecordedDTO.setAuthTransactionTypeDetailCode(TranTypeDetailEnum.CUP_PRE_AUTH.getCode());
                    }
                    // 预授权完成冲正
                    if (AuthConstans.MTI_FIRST_TWO_VALUE_00.equals(firstTwo)) {
                        authRecordedDTO
                                .setAuthTransactionTypeDetailCode(TranTypeDetailEnum.CUP_PRE_AUTH_FINISH.getCode());
                    }
                }
                // 普通交易的冲正
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());
            }
        }

        authRecordedDTO.setAuthTransactionSourceCode(iso8583Bo.getSourceCode());

        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD48))
                && map.get(Constants8583Field.FIELD48).length() >= 2) {
            // 这里 只解析分期-IP用法
            String useType = map.get(Constants8583Field.FIELD48).substring(0, 2);
            if (!"IP".equals(useType)) {
                return authRecordedDTO;
            }
            if (map.get(Constants8583Field.FIELD48).length() >= 4) {
                String termStr = map.get(Constants8583Field.FIELD48).substring(2, 4);
                if (StringUtils.isNotBlank(termStr)) {
                    // 分期期数
                    authRecordedDTO.setTerm(Integer.valueOf(termStr));
                }
            }
            if (map.get(Constants8583Field.FIELD48).length() >= 42) {
                String termRateStr = map.get(Constants8583Field.FIELD48).substring(36, 42);
                boolean isNumber = VerifyDateUtil.isNumberAndNotAllZero(termRateStr);
                if (isNumber) {
                    // 分期总的费率
                    authRecordedDTO.setInstallmentFeeRate(
                            new BigDecimal(termRateStr).divide(new BigDecimal(1000), 3, BigDecimal.ROUND_DOWN));
                    // 分期定价方式
                    authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.DELIVERYRATE_FLAG.getCode());
                } else {
                    authRecordedDTO.setInstallmentFeeRate(BigDecimal.ZERO);
                    authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.BASICPRICING_FLAG.getCode());
                }
            } else {
                authRecordedDTO.setInstallmentFeeRate(BigDecimal.ZERO);
                authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.BASICPRICING_FLAG.getCode());
            }
            // 分期减免方式
            authRecordedDTO.setInstallmentDerateMethod(String.valueOf(AuthConstans.ZERO));
        }
        return authRecordedDTO;
    }
}
