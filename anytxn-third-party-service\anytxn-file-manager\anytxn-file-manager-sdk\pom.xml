<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>anytxn-file-manager</artifactId>
        <groupId>com.anytech</groupId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-file-manager-sdk</artifactId>

    <dependencies>
        <!-- 内部模块引用 start -->
        <dependency>
            <groupId>jrx.anyscheduler</groupId>
            <artifactId>anyscheduler-batch-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sharding</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sequence</artifactId>
        </dependency>
        <!-- 内部模块引用 end -->


        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>



    </dependencies>

</project>
