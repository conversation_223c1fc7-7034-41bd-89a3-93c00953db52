package com.anytech.anytxn.file.runner;

import com.anytech.anytxn.common.core.config.SegmentProperties;
import com.anytech.anytxn.common.core.constants.ShardingConstant;
import com.anytech.anytxn.common.core.utils.BaseContextHandler;
import com.anytech.anytxn.file.mapper.FileManagerScanParamMapper;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.monitor.FileScanParamMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @description: 服务启动时需要启动的一些监控
 * @author: zhangnan
 * @create: 2021-03-18
 **/
@Component
public class FileRunner implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(FileRunner.class);

    @Autowired
    private FileManagerScanParamMapper fileManagerScanParamMapper;
    @Autowired
    private FileScanParamMonitor fileScanParamMonitor;
    @Autowired
    private SegmentProperties segmentProperties;

    @Override
    public void run(ApplicationArguments args) throws Exception {

        logger.info("Initializing file scan monitoring");
        //启动时要初始化需要监控的扫描文件定时
        /*List<FileManagerScanParam> list =  fileManagerScanParamMapper.selectScanList();
        for (FileManagerScanParam f:list) {
            fileScanParamMonitor.addTasks(f);
        }*/
        String tenantIds = segmentProperties.getTenantIds();
        if (StringUtils.isNotBlank(tenantIds)) {
            String[] tenantIdArray = tenantIds.split(",");
            Arrays.stream(tenantIdArray).forEach(item -> {
                try {
                    BaseContextHandler.set(ShardingConstant.TENANT_ID, item);
                    List<FileManagerScanParam> list =  fileManagerScanParamMapper.selectScanList();
                    for (FileManagerScanParam f:list) {
                        fileScanParamMonitor.addTasks(f);
                    }
                } finally {
                    BaseContextHandler.remove();
                }
            });
        }

    }
}
