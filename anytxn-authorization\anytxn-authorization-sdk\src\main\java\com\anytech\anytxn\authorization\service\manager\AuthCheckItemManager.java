package com.anytech.anytxn.authorization.service.manager;

import com.anytech.anytxn.authorization.base.service.auth.ICurrencyCommonService;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.account.service.CommonAccountService;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
/**
 * @Author: sukang
 * @Date: 2020/8/13 10:39
 *
 * 授权检查项管理类
 */
@Service
public class AuthCheckItemManager {

    private static final Logger logger = LoggerFactory.getLogger(AuthCheckItemManager.class);

    @Resource
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Resource
    private ICurrencyCommonService currencyCommonService;

    @Resource
    private CommonAccountService commonAccountService;





    /**
     * 获取账户管理dto
     *      1.先获取主账户id的管理账户dto
     *      2.获取不到则按照 客户号+主账产品编号+入账币种 获取管理账户
     *      3.如果获取不到 上一账单日,开户日期 取卡片授权信息表
     * @param authorizationCheckProcessingPayload {link AuthorizationCheckProcessingPayload}
     * @return 账户管理dto
     */
    public AccountManagementInfoDTO getAccountManagementInfoDTO(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload){

        AuthRecordedDTO authRecordedDto = authorizationCheckProcessingPayload.getAuthRecordedDTO();

        //如果获取不到主账产品组则构建一个虚拟账户dto
        if (authRecordedDto.getPrimaryAccountGroupAuthControlDTO() == null){
            AccountManagementInfoDTO managementInfo = new AccountManagementInfoDTO();
            buildOpenDate(managementInfo,authorizationCheckProcessingPayload);
            return managementInfo;
        }
        //主账产品的账户id不为空则按照主账户id的账户信息表
        if (StringUtils.isNotEmpty(authorizationCheckProcessingPayload.getAccountManagementInfoDTO()
                .getAccountManagementId())){

            return authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        }
        //如果主账产品组的主账户id为空，则按照 客户号+主账产品编号+入账币种 获取管理账户

        CommonAccountDTO commonAccountDTO = CommonAccountDTO.CommonAccountDTOBuilder
                .aCommonAccountDTO()
                .withCurrency(authRecordedDto.getAuthBillingCurrencyCode())
                .withCustomerId(authRecordedDto.getAuthCustomerId())
                .withOrganizationNumber(authRecordedDto.getOrganizationNumber())
                .withAcctProductNumber(authRecordedDto.getPrimaryAccountGroupAuthControlDTO().getAcctProductCode())
                .build();

        logger.info("Calling commonAccountService.selectByCusIdProNumAndCurr: customerId={}, productCode={}", authRecordedDto.getAuthCustomerId(), authRecordedDto.getPrimaryAccountGroupAuthControlDTO().getAcctProductCode());
        AccountManagementInfo managementInfo = commonAccountService.selectByCusIdProNumAndCurr(commonAccountDTO);
        logger.info("CommonAccountService call completed: customerId={}, productCode={}", authRecordedDto.getAuthCustomerId(), authRecordedDto.getPrimaryAccountGroupAuthControlDTO().getAcctProductCode());

        if (managementInfo != null){
            return BeanMapping.copy(managementInfo, AccountManagementInfoDTO.class);
        }

        //如果获取不到
        buildOpenDate(authorizationCheckProcessingPayload.getAccountManagementInfoDTO(),authorizationCheckProcessingPayload);
        return authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
    }


    private void buildOpenDate(AccountManagementInfoDTO accountManagementInfo,
                               AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload){

        LocalDate openDate = authorizationCheckProcessingPayload.getCardAuthorizationDTO().getOpenDate();
        accountManagementInfo.setOpenDate(openDate);
        accountManagementInfo.setLastStatementDate(openDate);
    }


    /**
     * 汇率转换
     * @param organizationNumber 机构号
     * @param sourceCurrencyCode  来源币种
     * @param targetCurrencyCode  目标币种
     * @param amount 初始金额
     * @return   转换后金额
     */
    public BigDecimal converterRate(String organizationNumber,String sourceCurrencyCode,
                                    String targetCurrencyCode, BigDecimal amount){
        if (StringUtils.isAnyBlank(organizationNumber,sourceCurrencyCode,targetCurrencyCode)){
            logger.error("converterRate: orgNumber={}, sourceCurrency={}, targetCurrency={}, amount={}", organizationNumber, sourceCurrencyCode, targetCurrencyCode, amount);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.I_CCP);
        }
        logger.info("Calling currencyCommonService.getCurrencyRate: orgNumber={}, sourceCurrency={}, targetCurrency={}", organizationNumber, sourceCurrencyCode, targetCurrencyCode);
        BigDecimal result = currencyCommonService.getCurrencyRate(organizationNumber, sourceCurrencyCode
                , targetCurrencyCode, amount);
        logger.info("CurrencyCommonService call completed: orgNumber={}, sourceCurrency={}, targetCurrency={}", organizationNumber, sourceCurrencyCode, targetCurrencyCode);
        return result;
    }
}
