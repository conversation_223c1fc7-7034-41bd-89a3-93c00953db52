package com.anytech.anytxn.customer.service.impl;

import com.anytech.anytxn.business.base.customer.domain.dto.*;
import com.anytech.anytxn.common.core.utils.*;
import com.anytech.anytxn.customer.base.exception.AnyTxnCardholderException;
import com.anytech.anytxn.customer.base.enums.AnyTxnCardholderResCodeEnum;
import com.anytech.anytxn.customer.base.enums.CardholderRepDetailEnum;
import com.anytech.anytxn.business.base.card.domain.dto.AccountOpeningDTO;
import com.anytech.anytxn.customer.base.service.ICardOpenSubsidiaryCustomerService;
import com.anytech.anytxn.customer.base.service.ICustomerAuthorizationService;
import com.anytech.anytxn.customer.base.service.ICustomerInfoService;
import com.anytech.anytxn.business.base.card.constants.CardBusinessConstant;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.monetary.mapper.CustReconciliationControlMapper;
import com.anytech.anytxn.business.dao.monetary.model.CustReconciliationControl;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.base.customer.enums.AddressTypeEnum;
import com.anytech.anytxn.business.base.customer.enums.CustomerRelationshipEnum;
import com.anytech.anytxn.business.base.customer.enums.RelationshipTypeEnum;

import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;

import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: sumingyue
 * @create: 2020/07/14 14:41
 */
@Service
public class CardOpenSubsidiaryCustomerServiceImpl implements ICardOpenSubsidiaryCustomerService {
    private static final Logger logger = LoggerFactory.getLogger(CardOpenSubsidiaryCustomerServiceImpl.class);

    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Autowired
    private ICustomerInfoService customerInfoService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private ICustomerAuthorizationService customerAuthorizationService;
    @Autowired
    private CustReconciliationControlMapper custReconciliationControlMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean cardOpenSubsidiaryCustomer(AccountOpeningDTO record) {
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", record.getOrganizationNumber());
        OrganizationInfoResDTO orgInfo = organizationInfoService.findOrganizationInfo(
                record.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: orgInfo={}", orgInfo != null ? "found" : "null");

        if (orgInfo == null) {
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_NOT_EXIST, CardholderRepDetailEnum.NOT_EXIST_ORGANIZATION);
        }
        // 查询附卡客户对账表
        logger.info("Calling custReconciliationControlService.getControl: subAppCustomerId={}, organizationNumber={}", record.getSubAppCustomerId(), record.getOrganizationNumber());
        CustReconciliationControlDTO controlDTO = custReconciliationControlService.getControl(record.getSubAppCustomerId(), record.getOrganizationNumber());
        logger.info("custReconciliationControlService.getControl completed: controlDTO={}", controlDTO != null ? "found" : "null");
        CustomerAuthorizationInfoDTO customerAuthInfoSubsidiary = getCustomerAuthorizationInfoDTO(record.getAppsIdType(), record.getAppsIdNumber(),record.getAppPartnerId());
        logger.info("Start to save or edit subsidiary customer info: idType={}, idNumber={}, customerExists={}", record.getAppsIdType(), record.getAppsIdNumber(), customerAuthInfoSubsidiary == null ? "false" : "true");
        if (customerAuthInfoSubsidiary == null) {
            createSubsidiaryCustomerBasicInfo(record, record.getSubAppCustomerId(), orgInfo);
            createSubsidiaryCustomerAuthorizationInfo(record, record.getSubAppCustomerId());
            createSubsidiaryCustomerRelationshipInfo(record, record.getSubAppCustomerId());
            /**
             * 添加地址
             */
            subsidiaryCustomerAddressInfoManage(record,true);
            createSubsidiaryCustomerAdditionalInfo(record, record.getSubAppCustomerId());
            createSubsidiaryCustomerForeignerInfo(record, record.getSubAppCustomerId());

            if (controlDTO == null) {
                createCustReconciliationControl(record);
            }
        } else{
            //如果是老客户 更新客户标志
            updateSubsidiaryCustomerBasicInfo(record);
            updateSubsidiaryCustomerAuthorizationInfo(record, controlDTO);
            updateSubsidiaryCustomerRelationshipInfo(record,record.getSubAppCustomerId());
            /**
             * 编辑地址
             */
            subsidiaryCustomerAddressInfoManage(record,false);
            updateSubsidiaryCustomerAdditionalInfo(record, record.getSubAppCustomerId());
            updateSubsidiaryCustomerForeignerInfo(record, record.getSubAppCustomerId());
        }
        return true;
    }

    /**
     * 创建附卡新客户对账控制信息
     * @param record
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int createCustReconciliationControl(AccountOpeningDTO record) {
        CustReconciliationControl custRc = new CustReconciliationControl();
        custRc.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        custRc.setOrganizationNumber(record.getOrganizationNumber());
        if (StringUtils.isEmpty(record.getSubAppCustomerId())) {
            logger.error("CardOpenSubsidiaryCustomerServiceImpl.createCustReconciliationControl: record.getSubAppCustomerId() is empty");
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_NOT_EXIST, CardholderRepDetailEnum.CHECK_CARD_OPEN_SUBSIDIARY_ID);
        }
        custRc.setCustomerId(record.getSubAppCustomerId());
        // 业务处理日赋值为系统当前处理日
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", record.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(record.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationInfo={}", organizationInfo != null ? "found" : "null");
        custRc.setReconciliationDate(organizationInfo.getAccruedThruDay());
        custRc.setPartitionKey(PartitionKeyUtils.partitionKey(record.getAppCustomerId()));
        custRc.setOptmisticLockCount(0L);
        custRc.setCreateTime(LocalDateTime.now());
        custRc.setUpdateTime(LocalDateTime.now());
        custRc.setUpdateBy(LoginUserUtils.getLoginUserName());
        custRc.setVersionNumber(0L);
        return custReconciliationControlMapper.insert(custRc);
    }

    /**
     * 附卡客户地址信息维护
     * @param record
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void subsidiaryCustomerAddressInfoManage(AccountOpeningDTO record,boolean isNew) {
        String customerId = record.getSubAppCustomerId();
        List<CustomerAddressInfoDTO> customerAddressInfoDTOS = encapsulationSubsidiaryCustomerAddressInfo(record,customerId);
        try {
            if (isNew) {
                for (CustomerAddressInfoDTO nw : customerAddressInfoDTOS) {
                    logger.info("Calling customerInfoService.addCustomerAddressInfo: customerId={}, type={}", nw.getCustomerId(), nw.getType());
                    customerInfoService.addCustomerAddressInfo(nw);
                    logger.info("customerInfoService.addCustomerAddressInfo completed");
                }
            } else {
                logger.info("Calling customerInfoService.findCustomerAddressInfo: organizationNumber={}, customerId={}", record.getOrganizationNumber(), customerId);
                List<CustomerAddressInfoDTO> customerAddressInfos = customerInfoService.findCustomerAddressInfo(record.getOrganizationNumber(), customerId);
                logger.info("customerInfoService.findCustomerAddressInfo completed: size={}", customerAddressInfos != null ? customerAddressInfos.size() : 0);
                for (CustomerAddressInfoDTO nw : customerAddressInfoDTOS) {
                    CustomerAddressInfoDTO temp = null;
                    for (CustomerAddressInfoDTO old : customerAddressInfos) {
                        //类型相同则更新
                        if (old.getType().equals(nw.getType())) {
                            old.setAddress(nw.getAddress());
                            old.setCity(nw.getCity());
                            old.setDistrict(nw.getDistrict());
                            old.setProvince(nw.getProvince());
                            old.setZipcode(nw.getZipcode());
                            temp = old;
                            break;
                        }
                    }
                    if (temp != null) {
                        logger.info("Calling customerInfoService.modifyCustomerAddressInfo: customerId={}, type={}", temp.getCustomerId(), temp.getType());
                        customerInfoService.modifyCustomerAddressInfo(temp);
                        logger.info("customerInfoService.modifyCustomerAddressInfo completed");
                    } else {
                        logger.info("Calling customerInfoService.addCustomerAddressInfo: customerId={}, type={}", nw.getCustomerId(), nw.getType());
                        customerInfoService.addCustomerAddressInfo(nw);
                        logger.info("customerInfoService.addCustomerAddressInfo completed");
                    }

                }
            }
        } catch (Exception e) {
            logger.error("CardHolder failed to maintain subsidiary customer address info", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }


    private List<CustomerAddressInfoDTO> encapsulationSubsidiaryCustomerAddressInfo(AccountOpeningDTO record, String customerId) {
        String loginUserName = LoginUserUtils.getLoginUserName();
        List<CustomerAddressInfoDTO> customerAddressInfos = new ArrayList<>();
        //家庭
        if(StringUtils.isNotBlank(record.getAppsHomeAddress4())){
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.HOME_ADDRESS.getCode());
            customerAddressInfo.setAddress(record.getAppsHomeAddress4());
            customerAddressInfo.setDistrict(record.getAppsHomeAddress3());
            customerAddressInfo.setCity(record.getAppsHomeAddress2());
            customerAddressInfo.setProvince(record.getAppsHomeAddress1());
            customerAddressInfo.setZipcode(record.getAppsHomeZipcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy(loginUserName);
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }
        //房产地址
       /* if(Objects.nonNull(record.getAppsHouseAddress4())){
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.PROPERTY_ADDRESS.getCode());
            customerAddressInfo.setAddress(record.getAppsHouseAddress4());
            customerAddressInfo.setDistrict(record.getAppsHouseAddress3());
            customerAddressInfo.setCity(record.getAppsHouseAddress2());
            customerAddressInfo.setProvince(record.getAppsHouseAddress1());
            customerAddressInfo.setZipcode(record.getAppsHouseZipcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy("admin");
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }*/
        //户籍地址
        /*if(Objects.nonNull(record.getAppsRegisterAddress4())){
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.PERMANENT_ADDRESS.getCode());
            customerAddressInfo.setAddress(record.getAppsRegisterAddress4());
            customerAddressInfo.setDistrict(record.getAppsRegisterAddress3());
            customerAddressInfo.setCity(record.getAppsRegisterAddress2());
            customerAddressInfo.setProvince(record.getAppsRegisterAddress1());
            customerAddressInfo.setZipcode(record.getAppsRegisterZipcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy("admin");
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }*/
        //单位地址
        if(StringUtils.isNotBlank(record.getAppsOfficeAddress4())){
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.COMPANY_ADDRESS.getCode());
            customerAddressInfo.setAddress(record.getAppsOfficeAddress4());
            customerAddressInfo.setDistrict(record.getAppsOfficeAddress3());
            customerAddressInfo.setCity(record.getAppsOfficeAddress2());
            customerAddressInfo.setProvince(record.getAppsOfficeAddress1());
            customerAddressInfo.setZipcode(record.getAppsOfficeZipcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy(loginUserName);
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }
        //备用账单地址1
        if (StringUtils.isNotBlank(record.getAppsAlternateBillingAddress4())) {
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.ALTERNATE_BILLING_ADDRESS1.getCode());
            customerAddressInfo.setAddress(record.getAppsAlternateBillingAddress1());
            customerAddressInfo.setAddress2(record.getAppsAlternateBillingAddress2());
            customerAddressInfo.setAddress3(record.getAppsAlternateBillingAddress3());
            customerAddressInfo.setAddress4(record.getAppsAlternateBillingAddress4());
            customerAddressInfo.setAddress5(record.getAppsAlternateBillingAddress5());
            customerAddressInfo.setCountryCode(record.getAppsAlternateBillingAddress1CountryCode());
            customerAddressInfo.setCity(record.getAppsAlternateBillingAddress1City());
            customerAddressInfo.setProvince(record.getAppsAlternateBillingAddress1State());
            customerAddressInfo.setZipcode(record.getAppsAlternateBillingAddress1Postcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy(loginUserName);
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }
        //备用账单地址2
        if (StringUtils.isNotBlank(record.getAppsAlternateBillingAddress24())) {
            CustomerAddressInfoDTO customerAddressInfo = new CustomerAddressInfoDTO();
            customerAddressInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerAddressInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerAddressInfo.setCustomerId(customerId);
            customerAddressInfo.setType(AddressTypeEnum.ALTERNATE_BILLING_ADDRESS2.getCode());
            customerAddressInfo.setAddress(record.getAppsAlternateBillingAddress21());
            customerAddressInfo.setAddress2(record.getAppsAlternateBillingAddress22());
            customerAddressInfo.setAddress3(record.getAppsAlternateBillingAddress23());
            customerAddressInfo.setAddress4(record.getAppsAlternateBillingAddress24());
            customerAddressInfo.setAddress5(record.getAppsAlternateBillingAddress25());
            customerAddressInfo.setCountryCode(record.getAppsAlternateBillingAddress2CountryCode());
            customerAddressInfo.setCity(record.getAppsAlternateBillingAddress2City());
            customerAddressInfo.setProvince(record.getAppsAlternateBillingAddress2State());
            customerAddressInfo.setZipcode(record.getAppsAlternateBillingAddress2Postcode());
            customerAddressInfo.setCreateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateTime(LocalDateTime.now());
            customerAddressInfo.setUpdateBy(loginUserName);
            customerAddressInfo.setVersionNumber(1L);
            customerAddressInfos.add(customerAddressInfo);
        }
        return customerAddressInfos;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void createSubsidiaryCustomerRelationshipInfo(AccountOpeningDTO record,
                                                         String customerId) {

        List<CustomerRelationshipInfoDTO> customerRelationshipInfos = encapsulationSubsidiaryCustomerRelationshipInfo(record,customerId);
        try {
            for (CustomerRelationshipInfoDTO customerRelationshipInfo:customerRelationshipInfos) {
                logger.info("Calling customerInfoService.addCustomerRelationInfo: customerId={}, relationship={}", customerRelationshipInfo.getCustomerId(), customerRelationshipInfo.getRelationship());
                customerInfoService.addCustomerRelationInfo(customerRelationshipInfo);
                logger.info("customerInfoService.addCustomerRelationInfo completed");
            }
        } catch (Exception e) {
            logger.error("Failed to create subsidiary customer contact information[%s]", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
        logger.info("Successfully created subsidiary customer contact information");
    }

    private List<CustomerRelationshipInfoDTO> encapsulationSubsidiaryCustomerRelationshipInfo(AccountOpeningDTO record, String customerId) {
        String loginUserName = LoginUserUtils.getLoginUserName();
        List<CustomerRelationshipInfoDTO> customerRelationshipInfos = new ArrayList<>();
        if(Objects.nonNull(record.getAppsRelationship1())){
            CustomerRelationshipInfoDTO customerRelationshipInfo = new CustomerRelationshipInfoDTO();
            customerRelationshipInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerRelationshipInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerRelationshipInfo.setCustomerId(customerId);
            customerRelationshipInfo.setType(RelationshipTypeEnum.IMMEDIATE_FAMILY.getCode());
            customerRelationshipInfo.setRelationship(record.getAppsRelationship1());
            customerRelationshipInfo.setIdType(record.getAppsRelationship1IdType());
            customerRelationshipInfo.setIdNumber(record.getAppsRelationship1IdNumber());
            customerRelationshipInfo.setChineseName(record.getAppsRelationship1ChineseName());
            customerRelationshipInfo.setEnglishName(record.getAppsRelationship1EnglishName());
            customerRelationshipInfo.setMobilePhone(record.getAppsRelationship1MobilePhone());
            customerRelationshipInfo.setSex(record.getAppsRelationship1Sex());
            customerRelationshipInfo.setHomePhone(record.getAppsRelationship1HomePhone());
            customerRelationshipInfo.setEmployerName(record.getAppsRelationship1EmployerName());
            customerRelationshipInfo.setOfficePhone(record.getAppsRelationship1OfficePhone());
            customerRelationshipInfo.setExtentionNumber(record.getAppsRelationship1ExtentionNumber());
            customerRelationshipInfo.setCreateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateBy(loginUserName);
            customerRelationshipInfo.setVersionNumber(1L);
            customerRelationshipInfos.add(customerRelationshipInfo);
        }

        if(Objects.nonNull(record.getAppsRelationship2())){
            CustomerRelationshipInfoDTO customerRelationshipInfo = new CustomerRelationshipInfoDTO();
          customerRelationshipInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerRelationshipInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerRelationshipInfo.setCustomerId(customerId);
            if(CustomerRelationshipEnum.CHILDREN.getCode().equals(record.getAppsRelationship2())
                    || CustomerRelationshipEnum.PARENT.getCode().equals(record.getAppsRelationship2())
                    || CustomerRelationshipEnum.SPOUSE.getCode().equals(record.getAppsRelationship2())){

                customerRelationshipInfo.setType(RelationshipTypeEnum.IMMEDIATE_FAMILY.getCode());
            }else{
                customerRelationshipInfo.setType(RelationshipTypeEnum.EMERGENCY_CONTACT.getCode());
            }

            customerRelationshipInfo.setRelationship(record.getAppsRelationship2());
            customerRelationshipInfo.setIdType(record.getAppsRelationship2IdType());
            customerRelationshipInfo.setIdNumber(record.getAppsRelationship2IdNumber());
            customerRelationshipInfo.setChineseName(record.getAppsRelationship2ChineseName());
            customerRelationshipInfo.setEnglishName(record.getAppsRelationship2EnglishName());
            customerRelationshipInfo.setMobilePhone(record.getAppsRelationship2MobilePhone());
            customerRelationshipInfo.setSex(record.getAppsRelationship2Sex());
            customerRelationshipInfo.setHomePhone(record.getAppsRelationship2HomePhone());
            customerRelationshipInfo.setEmployerName(record.getAppsRelationship2EmployerName());
            customerRelationshipInfo.setOfficePhone(record.getAppsRelationship2OfficePhone());
            customerRelationshipInfo.setExtentionNumber(record.getAppsRelationship2ExtentionNumber());
            customerRelationshipInfo.setCreateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateBy(loginUserName);
            customerRelationshipInfo.setVersionNumber(1L);
            customerRelationshipInfos.add(customerRelationshipInfo);
        }

        if(Objects.nonNull(record.getAppsRelationship3())){
            CustomerRelationshipInfoDTO customerRelationshipInfo = new CustomerRelationshipInfoDTO();
          customerRelationshipInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerRelationshipInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerRelationshipInfo.setCustomerId(customerId);
            if(CustomerRelationshipEnum.CHILDREN.getCode().equals(record.getAppsRelationship3())
                    || CustomerRelationshipEnum.PARENT.getCode().equals(record.getAppsRelationship3())
                    || CustomerRelationshipEnum.SPOUSE.getCode().equals(record.getAppsRelationship3())){

                customerRelationshipInfo.setType(RelationshipTypeEnum.IMMEDIATE_FAMILY.getCode());
            }else{
                customerRelationshipInfo.setType(RelationshipTypeEnum.EMERGENCY_CONTACT.getCode());
            }
            customerRelationshipInfo.setRelationship(record.getAppsRelationship3());
            customerRelationshipInfo.setIdType(record.getAppsRelationship3IdType());
            customerRelationshipInfo.setIdNumber(record.getAppsRelationship3IdNumber());
            customerRelationshipInfo.setChineseName(record.getAppsRelationship3ChineseName());
            customerRelationshipInfo.setEnglishName(record.getAppsRelationship3EnglishName());
            customerRelationshipInfo.setMobilePhone(record.getAppsRelationship3MobilePhone());
            customerRelationshipInfo.setSex(record.getAppsRelationship3Sex());
            customerRelationshipInfo.setHomePhone(record.getAppsRelationship3HomePhone());
            customerRelationshipInfo.setEmployerName(record.getAppsRelationship3EmployerName());
            customerRelationshipInfo.setOfficePhone(record.getAppsRelationship3OfficePhone());
            customerRelationshipInfo.setExtentionNumber(record.getAppsRelationship3ExtentionNumber());
            customerRelationshipInfo.setCreateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateBy(loginUserName);
            customerRelationshipInfo.setVersionNumber(1L);
            customerRelationshipInfos.add(customerRelationshipInfo);
        }

        if(Objects.nonNull(record.getAppsRelationship4())){
            CustomerRelationshipInfoDTO customerRelationshipInfo = new CustomerRelationshipInfoDTO();
          customerRelationshipInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerRelationshipInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerRelationshipInfo.setCustomerId(customerId);
            customerRelationshipInfo.setType(RelationshipTypeEnum.GUARANTEE.getCode());
            customerRelationshipInfo.setRelationship(record.getAppsRelationship4());
            customerRelationshipInfo.setIdType(record.getAppsRelationship4IdType());
            customerRelationshipInfo.setIdNumber(record.getAppsRelationship4IdNumber());
            customerRelationshipInfo.setChineseName(record.getAppsRelationship4ChineseName());
            customerRelationshipInfo.setEnglishName(record.getAppsRelationship4EnglishName());
            customerRelationshipInfo.setMobilePhone(record.getAppsRelationship4MobilePhone());
            customerRelationshipInfo.setSex(record.getAppsRelationship4Sex());
            customerRelationshipInfo.setHomePhone(record.getAppsRelationship4HomePhone());
            customerRelationshipInfo.setEmployerName(record.getAppsRelationship4EmployerName());
            customerRelationshipInfo.setOfficePhone(record.getAppsRelationship4OfficePhone());
            customerRelationshipInfo.setExtentionNumber(record.getAppsRelationship4ExtentionNumber());
            customerRelationshipInfo.setCreateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateTime(LocalDateTime.now());
            customerRelationshipInfo.setUpdateBy(loginUserName);
            customerRelationshipInfo.setVersionNumber(1L);
            customerRelationshipInfos.add(customerRelationshipInfo);
        }
        return customerRelationshipInfos;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void createSubsidiaryCustomerAuthorizationInfo(AccountOpeningDTO record,
                                                          String customerId) {
        CustomerAuthorizationInfoDTO customerAuthorizationInfo = new CustomerAuthorizationInfoDTO();
        customerAuthorizationInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        customerAuthorizationInfo.setOrganizationNumber(record.getOrganizationNumber());
        customerAuthorizationInfo.setCustomerId(customerId);
        customerAuthorizationInfo.setStatus(CardBusinessConstant.CUSTOMER_AUTH_STATUS_1);
        customerAuthorizationInfo.setIdType(record.getAppsIdType());
        customerAuthorizationInfo.setIdNumber(record.getAppsIdNumber());
        customerAuthorizationInfo.setIdExpireDate(StringUtils.isBlank(record.getAppsIdExpireDate())?null:LocalDate.parse(record.getAppsIdExpireDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAuthorizationInfo.setIdStartDate(StringUtils.isBlank(record.getAppsIdStartDate())?null:LocalDate.parse(record.getAppsIdStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAuthorizationInfo.setIdDepartment(record.getAppsIdDepartment());
        customerAuthorizationInfo.setType(record.getAppsCustomerType());
        customerAuthorizationInfo.setChineseName(record.getAppsChineseName());
        customerAuthorizationInfo.setEnglishName(record.getAppsEnglishName());
        customerAuthorizationInfo.setMobilePhone(record.getAppsMobilePhone());
        customerAuthorizationInfo.setBlockCode("");
        customerAuthorizationInfo.setBlockCodeDate(null);
        customerAuthorizationInfo.setPreviousBlock("");
        customerAuthorizationInfo.setPreviousBlockDate(null);
        customerAuthorizationInfo.setPreviousBlockStopDate(null);
        customerAuthorizationInfo.setGroupType(record.getAppsClass());
        customerAuthorizationInfo.setEcifNumber(record.getAppsEcifNumber());
        customerAuthorizationInfo.setCreateTime(LocalDateTime.now());
        customerAuthorizationInfo.setUpdateTime(LocalDateTime.now());
        customerAuthorizationInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        customerAuthorizationInfo.setVersionNumber(1L);
        customerAuthorizationInfo.setOldIdNumber(record.getAppsOldIdNumber());
        try {
            logger.info("Calling customerAuthorizationService.addCustomerAuthorizationInfo: customerId={}, idNumber={}", customerAuthorizationInfo.getCustomerId(), customerAuthorizationInfo.getIdNumber());
            customerAuthorizationService.addCustomerAuthorizationInfo(customerAuthorizationInfo);
            logger.info("customerAuthorizationService.addCustomerAuthorizationInfo completed");
        } catch (Exception e) {
            logger.error("Failed to create subsidiary customer authorization information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
        logger.info("Successfully created subsidiary authorization information");
    }

    private CustomerAuthorizationInfoDTO getCustomerAuthorizationInfoDTO(String idType, String idNumber,String partnerId) {
        logger.info("Query customer authorization information based on ID number and ID type, ID type:{}, ID number:{}", idType, idNumber);

        try {
            CustomerAuthorizationInfo customerAuthorizationInfo = this.customerAuthorizationInfoSelfMapper.selectByIdTypeAndIdNumberAndPartnerId(OrgNumberUtils.getOrg(),idType, idNumber,partnerId);
            return customerAuthorizationInfo == null ? null : (CustomerAuthorizationInfoDTO)BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfoDTO.class);
        } catch (Exception e) {
            logger.error("Error querying customer authorization information based on ID number and ID type", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void createSubsidiaryCustomerBasicInfo(AccountOpeningDTO record, String customerId,
                                                  OrganizationInfoResDTO orgInfo) {
        CustomerBasicInfoDTO customerBasicInfo = new CustomerBasicInfoDTO();
        customerBasicInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        customerBasicInfo.setOrganizationNumber(record.getOrganizationNumber());
        customerBasicInfo.setCustomerId(customerId);
        customerBasicInfo.setBranchNumber(record.getAppBranchNumber());
        customerBasicInfo.setNetworkPointNumber(record.getAppNetworkPointNumber());
        customerBasicInfo.setPrincipalSupplementaryInd(CardBusinessConstant.SUPPLEMENT_CUSTOMER_INDICATOR);
        customerBasicInfo.setOpenDate(orgInfo.getNextProcessingDay());
        customerBasicInfo.setApplicationChannel(record.getApplicationChannel());
        customerBasicInfo.setCycleDay(StringUtils.isBlank(record.getAppCycleDay())?null:Short.parseShort(record.getAppCycleDay()));
        customerBasicInfo.setStatementAddressType(record.getAppStatementAddressType());
        customerBasicInfo.setRegularPaymentIndicator(CardBusinessConstant.REGULAR_PAYMENT_INDICATOR);
        customerBasicInfo.setStatementType(record.getAppStatementType());
        customerBasicInfo.setSex(record.getAppsSex());
        customerBasicInfo.setMarital(record.getAppsMarital());
        customerBasicInfo.setQualification(record.getAppsQualification());
        customerBasicInfo.setBirthDate(StringUtils.isBlank(record.getAppsBirthDate())?null:LocalDate.parse(record.getAppsBirthDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerBasicInfo.setNationality(record.getAppsNationality());
        customerBasicInfo.setRace(record.getAppsRace());
        customerBasicInfo.setResidentProperty(record.getAppsResidentProperty());
        customerBasicInfo.setHouseType(record.getAppsHouseType());
        customerBasicInfo.setResidentPeriod(record.getAppsResidentPeriod());
        customerBasicInfo.setHomePhone(record.getAppsHomePhone());
        customerBasicInfo.setRelativeName("");
        customerBasicInfo.setSupplementaryRelationship("");
        customerBasicInfo.setEmail(record.getAppsEmail());
        customerBasicInfo.setEmployerName(record.getAppsEmployerName());
        customerBasicInfo.setOfficePhone(record.getAppsOfficePhone());
        customerBasicInfo.setExtentionNumber(record.getAppsExtentionNumber());
        customerBasicInfo.setOccupation(record.getAppsOccupationCode());
        customerBasicInfo.setOccupationPeriod(record.getAppsOccupationPeriod());
        customerBasicInfo.setAnnualSalary(StringUtils.isBlank(record.getAppsAnnualSalary())?null:Long.parseLong(record.getAppsAnnualSalary()));
        customerBasicInfo.setBusinessNature(record.getAppsBusinessNature());
        customerBasicInfo.setRecommendedByCustomerId(record.getAppRecommendedByCustomerId());
        customerBasicInfo.setSalesManager(record.getAppSalesManager());
        customerBasicInfo.setNumberOfCrediteCard(StringUtils.isBlank(record.getAppNumberOfCrediteCard())?null:Integer.parseInt(record.getAppNumberOfCrediteCard()));
        customerBasicInfo.setMemberSince(StringUtils.isBlank(record.getAppMemberSince())?null:LocalDate.parse(record.getAppMemberSince(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerBasicInfo.setMemo("");
        customerBasicInfo.setCreateTime(LocalDateTime.now());
        customerBasicInfo.setUpdateTime(LocalDateTime.now());
        customerBasicInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        customerBasicInfo.setVersionNumber(1L);

        /**开户新增违约金免除标志 利息免除标志 财务状态 财务状态设置日期******/
        customerBasicInfo.setCustWaiveInterestFlg("0");
        customerBasicInfo.setCustWaiveLateFeeFlg("0");
        customerBasicInfo.setFinanceStatus("0");
        customerBasicInfo.setFinanceStatusSetDate(customerBasicInfo.getOpenDate());


        customerBasicInfo.setHouseArea(StringUtils.isBlank(record.getAppsHouseArea())?null:new BigDecimal(record.getAppsHouseArea()));
        customerBasicInfo.setHouseLoan(StringUtils.isBlank(record.getAppsHouseLoan())?null:new BigDecimal(record.getAppsHouseLoan()));
        customerBasicInfo.setCompanyCategory(record.getAppsCompanyCategory());
        customerBasicInfo.setCompanySize(record.getAppsCompanySize());
        customerBasicInfo.setPositionLevel(record.getAppsPositionLevel());
        customerBasicInfo.setPositionTitle(record.getAppsPositionTitle());
        customerBasicInfo.setDepartmentName(record.getAppsDepartment());
        //语言
        customerBasicInfo.setLanguage(record.getAppLanguage());
        //修改帐单日次数
        customerBasicInfo.setCycleDayModifyCount(0);
        //上次修改帐单日日期
        customerBasicInfo.setPreviousCycleDaySetDate(LocalDate.of(1,1,1));
        customerBasicInfo.setDegree(record.getAppsDegree());

        customerBasicInfo.setInitial(record.getAppsInitial());

        customerBasicInfo.setAlianName(record.getAppsAliasName());

        customerBasicInfo.setNativeCode(record.getAppsNativeCode());

        //附卡 客户 三亲
        if (StringUtils.equals("1", record.getAppsSanqinIndicator())) {
            customerBasicInfo.setSanqinIndicator(record.getAppsSanqinIndicator());
            customerBasicInfo.setSanqinBranchCode(record.getAppsSanqinBranchCode());
            customerBasicInfo.setSanqinChannel(record.getAppsSanqinChannel());
            customerBasicInfo.setSanqinDate(record.getAppsSanqinDate());
            customerBasicInfo.setSanqinEmployeeNumber(record.getAppsSanqinEmployeeNumber());
            customerBasicInfo.setSanqinMethod(record.getAppsSanqinMethod());
        }

        try {
            logger.info("Calling customerInfoService.addCustomerBasicInfo: customerId={}, chineseName={}", customerBasicInfo.getCustomerId(), customerBasicInfo.getChineseName());
            customerInfoService.addCustomerBasicInfo(customerBasicInfo);
            logger.info("customerInfoService.addCustomerBasicInfo completed");
        } catch (Exception e) {
            logger.error("Failed to create subsidiary customer basic information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
        logger.info("Successfully created subsidiary customer basic information");
    }

    private void updateSubsidiaryCustomerForeignerInfo(AccountOpeningDTO record, String customerIdSubsidiary) {
        if (!StringUtils.isBlank(record.getAppsResidentProperty()) && ("2".equals(record.getAppsResidentProperty()) || "3".equals(record.getAppsResidentProperty()))) {
            logger.info("Calling customerInfoService.queryCustomerForeignerInfo: organizationNumber={}, customerId={}", record.getOrganizationNumber(), customerIdSubsidiary);
            CustomerForeignerInfoDTO customerForeignerInfo = customerInfoService.queryCustomerForeignerInfo(record.getOrganizationNumber(),customerIdSubsidiary);
            logger.info("customerInfoService.queryCustomerForeignerInfo completed: customerForeignerInfo={}", customerForeignerInfo != null ? "found" : "null");
            if(Objects.isNull(customerForeignerInfo)){
                logger.error("Customer non-resident information does not exist! customerId={}",customerIdSubsidiary);
                throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, CardholderRepDetailEnum.CHECK_CARD_OPEN_SUBSIDIARY_CUSTOMER_FOREIGNER_INFO);
            }
            customerForeignerInfo.setLiveNationCode(record.getAppsLiveNationCode());
            customerForeignerInfo.setTaxNationCode(record.getAppsTaxNationCode());
            customerForeignerInfo.setTaxNationCode2(record.getAppsTaxNationCode2());
            customerForeignerInfo.setTaxNationCode3(record.getAppsTaxNationCode3());
            customerForeignerInfo.setTaxpayerIdNumber(record.getAppsTaxpayerIdNumber());
            customerForeignerInfo.setTaxpayerIdNumber2(record.getAppsTaxpayerIdNumber2());
            customerForeignerInfo.setTaxpayerIdNumber3(record.getAppsTaxpayerIdNumber3());
            customerForeignerInfo.setLackTaxDescription(record.getAppsLackTaxDescription());
            customerForeignerInfo.setLiveDetailAddress(record.getAppsLiveDetailaddress());
            customerForeignerInfo.setBirthDetailAddress(record.getAppsBirthDetailAddress());
            customerForeignerInfo.setSelfAttestationFlag(record.getAppsSelfAttestationFlag());
            customerForeignerInfo.setUpdateTime(LocalDateTime.now());
            customerForeignerInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
            try {
                logger.info("Calling customerInfoService.modifyCustomerForeignerInfo: customerId={}", customerForeignerInfo.getCustomerId());
                customerInfoService.modifyCustomerForeignerInfo(customerForeignerInfo);
                logger.info("customerInfoService.modifyCustomerForeignerInfo completed");
            } catch (Exception e) {
                logger.error("Failed to update old customer non-resident information", e);
                throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
            }
        }
    }

    private void updateSubsidiaryCustomerAdditionalInfo(AccountOpeningDTO record, String customerIdSubsidiary) {
        logger.info("Calling customerInfoService.queryCustomerAdditionalInfo: organizationNumber={}, customerId={}", record.getOrganizationNumber(), customerIdSubsidiary);
        CustomerAdditionalInfoDTO customerAdditionalInfo = customerInfoService.queryCustomerAdditionalInfo(record.getOrganizationNumber(),customerIdSubsidiary);
        logger.info("customerInfoService.queryCustomerAdditionalInfo completed: customerAdditionalInfo={}", customerAdditionalInfo != null ? "found" : "null");
        if(Objects.isNull(customerAdditionalInfo)){
            logger.error("Customer additional information does not exist! customerId={}",customerIdSubsidiary);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, CardholderRepDetailEnum.CHECK_CARD_OPEN_SUBSIDIARY_CUSTOMER_ADDITIONAL_INFO);
        }
        customerAdditionalInfo.setIncomeSource(record.getAppsIncomeSource());
        customerAdditionalInfo.setFosterNumber(StringUtils.isBlank(record.getAppsFosternumber())?null:Short.parseShort(record.getAppsFosternumber()));
        customerAdditionalInfo.setChildrenNumber(StringUtils.isBlank(record.getAppsChildrennumber())?null:Short.parseShort(record.getAppsChildrennumber()));
        customerAdditionalInfo.setQqNumber(record.getAppsQqNumber());
        customerAdditionalInfo.setWebchatNumber(record.getAppsWebchatNumber());
        customerAdditionalInfo.setAdditionalCreditCardType(record.getAppsAdditionalCreditCardType());
        customerAdditionalInfo.setAdditionalCreditCardNumber(record.getAppsAdditionalCreditCardNumber());
        customerAdditionalInfo.setCustomerLevelCode1(record.getAppsCustomerLevelcode1());
        customerAdditionalInfo.setCustomerLevelCode2(record.getAppsCustomerLevelcode2());
        customerAdditionalInfo.setCustomerLevelCode3(record.getAppsCustomerLevelcode3());
        customerAdditionalInfo.setGraduationSchoolChinese(record.getAppsGraduationSchoolChinese());
        customerAdditionalInfo.setGraduationSchoolEnglish(record.getAppsGraduationSchoolEnglish());
        customerAdditionalInfo.setEmailOfGraduationSchool(record.getAppsEmailOfGraduationSchool());
        customerAdditionalInfo.setEnrollmentDate(StringUtils.isBlank(record.getAppsEnrollmentdate())?null:LocalDate.parse(record.getAppsEnrollmentdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setGraduationDate(StringUtils.isBlank(record.getAppsGraduationdate())?null:LocalDate.parse(record.getAppsGraduationdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setPreviousCompanyName(record.getAppsPreviousEmployerName());
        customerAdditionalInfo.setPreviousAnnualSalary(StringUtils.isBlank(record.getAppsPreviousAnnualSalary())?null:new BigDecimal(record.getAppsPreviousAnnualSalary()));
        customerAdditionalInfo.setPreviousDepartment(record.getAppsPreviousDepartment());
        customerAdditionalInfo.setPreviousExtentionNumber(record.getAppsPreviousExtentionNumber());
        customerAdditionalInfo.setPreviousOccupationLevel(record.getAppsPreviousPositionLevel());
        customerAdditionalInfo.setPreviousOccupationPeriod(StringUtils.isBlank(record.getAppsPreviousOccupationPeriod())?null:Short.parseShort(record.getAppsPreviousOccupationPeriod()));
        customerAdditionalInfo.setPreviousOfficePhone(record.getAppsPreviousOfficePhone());
        customerAdditionalInfo.setLicensePlateNumber(record.getAppsLicensePlateNumber());
        customerAdditionalInfo.setIfVechicleLicenseExist(record.getAppsIfVechicleLicenseExist());
        customerAdditionalInfo.setVechicleLicenseDate(StringUtils.isBlank(record.getAppsVechicleLicenseDate())?null:LocalDate.parse(record.getAppsVechicleLicenseDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setCarPurchaseDate(StringUtils.isBlank(record.getAppsCarPurchaseDate())?null:LocalDate.parse(record.getAppsCarPurchaseDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setCarValue(StringUtils.isBlank(record.getAppsCarValue())?null:new BigDecimal(record.getAppsCarValue()));
        customerAdditionalInfo.setSpouseAnnulaSalary(StringUtils.isBlank(record.getAppsSpouseAnnualSalary())?null:new BigDecimal(record.getAppsSpouseAnnualSalary()));
        customerAdditionalInfo.setSpouseCompanyName(record.getAppsSpouseCompanyName());
        customerAdditionalInfo.setSpouseDepartment(record.getAppsSpouseDepartment());
        customerAdditionalInfo.setSpouseExtentionNumber(record.getAppsSpouseExtentionNumber());
        customerAdditionalInfo.setSpouseIdNumber(record.getAppsSpouseIdNumber());
        customerAdditionalInfo.setSpouseIdType(record.getAppsSpouseIdType());
        customerAdditionalInfo.setSpouseIncomeSource(record.getAppsSpouseIncomeSource());
        customerAdditionalInfo.setSpouseMobile(record.getAppsSpouseMobile());
        customerAdditionalInfo.setSpouseNameChinese(record.getAppsSpouseNamechinese());
        customerAdditionalInfo.setSpouseOccupationLevel(record.getAppsSpousePositionLevel());
        customerAdditionalInfo.setSpouseOccupationPeriod(StringUtils.isBlank(record.getAppsSpouseOccupationPeriod())?null:Short.parseShort(record.getAppsSpouseOccupationPeriod()));
        customerAdditionalInfo.setSpouseOfficePhone(record.getAppsSpouseOfficePhone());
        customerAdditionalInfo.setUpdateTime(LocalDateTime.now());
        customerAdditionalInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        customerAdditionalInfo.setSocialSecurityCardNumber(record.getAppsSocialSecurityCardNumber());
        customerAdditionalInfo.setProvince(record.getAppsProvince());
        customerAdditionalInfo.setCity(record.getAppsCity());
        customerAdditionalInfo.setDistrict(record.getAppsDistrict());
        customerAdditionalInfo.setCityVillageFlg(record.getAppsCityVillageFlg());
        try {
            logger.info("Calling customerInfoService.modifyCustomerAdditionalInfo: customerId={}", customerAdditionalInfo.getCustomerId());
            customerInfoService.modifyCustomerAdditionalInfo(customerAdditionalInfo);
            logger.info("customerInfoService.modifyCustomerAdditionalInfo completed");
        } catch (Exception e) {
            logger.error("Failed to update old customer additional information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }

    }

    private void createSubsidiaryCustomerForeignerInfo(AccountOpeningDTO record, String customerIdSubsidiary) {
        if (!StringUtils.isBlank(record.getAppsResidentProperty()) && ("2".equals(record.getAppsResidentProperty()) || "3".equals(record.getAppsResidentProperty()))) {
            CustomerForeignerInfoDTO customerForeignerInfo = new CustomerForeignerInfoDTO();
            customerForeignerInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
            customerForeignerInfo.setOrganizationNumber(record.getOrganizationNumber());
            customerForeignerInfo.setCustomerId(customerIdSubsidiary);
            customerForeignerInfo.setLiveNationCode(record.getAppsLiveNationCode());
            customerForeignerInfo.setTaxNationCode(record.getAppsTaxNationCode());
            customerForeignerInfo.setTaxNationCode2(record.getAppsTaxNationCode2());
            customerForeignerInfo.setTaxNationCode3(record.getAppsTaxNationCode3());
            customerForeignerInfo.setTaxpayerIdNumber(record.getAppsTaxpayerIdNumber());
            customerForeignerInfo.setTaxpayerIdNumber2(record.getAppsTaxpayerIdNumber2());
            customerForeignerInfo.setTaxpayerIdNumber3(record.getAppsTaxpayerIdNumber3());
            customerForeignerInfo.setLackTaxDescription(record.getAppsLackTaxDescription());
            customerForeignerInfo.setLiveDetailAddress(record.getAppsLiveDetailaddress());
            customerForeignerInfo.setBirthDetailAddress(record.getAppsBirthDetailAddress());
            customerForeignerInfo.setSelfAttestationFlag(record.getAppsSelfAttestationFlag());
            customerForeignerInfo.setCreateTime(LocalDateTime.now());
            customerForeignerInfo.setUpdateTime(LocalDateTime.now());
            customerForeignerInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
            customerForeignerInfo.setVersionNumber(1L);
            try {
                logger.info("Calling customerInfoService.addCustomerForeignerInfo: customerId={}", customerForeignerInfo.getCustomerId());
                customerInfoService.addCustomerForeignerInfo(customerForeignerInfo);
                logger.info("customerInfoService.addCustomerForeignerInfo completed");
            } catch (Exception e) {
                logger.error("Failed to create customer non-resident information", e);
                throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
            }
        }
    }

    private void createSubsidiaryCustomerAdditionalInfo(AccountOpeningDTO record, String customerIdSubsidiary) {
        CustomerAdditionalInfoDTO customerAdditionalInfo = new CustomerAdditionalInfoDTO();
        customerAdditionalInfo.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        customerAdditionalInfo.setOrganizationNumber(record.getOrganizationNumber());
        customerAdditionalInfo.setCustomerId(customerIdSubsidiary);
        customerAdditionalInfo.setIncomeSource(record.getAppsIncomeSource());
        customerAdditionalInfo.setFosterNumber(StringUtils.isBlank(record.getAppsFosternumber())?null:Short.parseShort(record.getAppsFosternumber()));
        customerAdditionalInfo.setChildrenNumber(StringUtils.isBlank(record.getAppsChildrennumber())?null:Short.parseShort(record.getAppsChildrennumber()));
        customerAdditionalInfo.setQqNumber(record.getAppsQqNumber());
        customerAdditionalInfo.setWebchatNumber(record.getAppsWebchatNumber());
        customerAdditionalInfo.setAdditionalCreditCardType(record.getAppsAdditionalCreditCardType());
        customerAdditionalInfo.setAdditionalCreditCardNumber(record.getAppsAdditionalCreditCardNumber());
        customerAdditionalInfo.setCustomerLevelCode1(record.getAppsCustomerLevelcode1());
        customerAdditionalInfo.setCustomerLevelCode2(record.getAppsCustomerLevelcode2());
        customerAdditionalInfo.setCustomerLevelCode3(record.getAppsCustomerLevelcode3());
        customerAdditionalInfo.setGraduationSchoolChinese(record.getAppsGraduationSchoolChinese());
        customerAdditionalInfo.setGraduationSchoolEnglish(record.getAppsGraduationSchoolEnglish());
        customerAdditionalInfo.setEmailOfGraduationSchool(record.getAppsEmailOfGraduationSchool());
        customerAdditionalInfo.setEnrollmentDate(StringUtils.isBlank(record.getAppsEnrollmentdate())?null:LocalDate.parse(record.getAppsEnrollmentdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setGraduationDate(StringUtils.isBlank(record.getAppsGraduationdate())?null:LocalDate.parse(record.getAppsGraduationdate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setPreviousCompanyName(record.getAppsPreviousEmployerName());
        customerAdditionalInfo.setPreviousAnnualSalary(StringUtils.isBlank(record.getAppsPreviousAnnualSalary())?null:new BigDecimal(record.getAppsPreviousAnnualSalary()));
        customerAdditionalInfo.setPreviousDepartment(record.getAppsPreviousDepartment());
        customerAdditionalInfo.setPreviousExtentionNumber(record.getAppsPreviousExtentionNumber());
        customerAdditionalInfo.setPreviousOccupationLevel(record.getAppsPreviousPositionLevel());
        customerAdditionalInfo.setPreviousOccupationPeriod(StringUtils.isBlank(record.getAppsPreviousOccupationPeriod())?null:Short.parseShort(record.getAppsPreviousOccupationPeriod()));
        customerAdditionalInfo.setPreviousOfficePhone(record.getAppsPreviousOfficePhone());
        customerAdditionalInfo.setLicensePlateNumber(record.getAppsLicensePlateNumber());
        customerAdditionalInfo.setIfVechicleLicenseExist(record.getAppsIfVechicleLicenseExist());
        customerAdditionalInfo.setVechicleLicenseDate(StringUtils.isBlank(record.getAppsVechicleLicenseDate())?null:LocalDate.parse(record.getAppsVechicleLicenseDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setCarPurchaseDate(StringUtils.isBlank(record.getAppsCarPurchaseDate())?null:LocalDate.parse(record.getAppsCarPurchaseDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAdditionalInfo.setCarValue(StringUtils.isBlank(record.getAppsCarValue())?null:new BigDecimal(record.getAppsCarValue()));
        customerAdditionalInfo.setSpouseAnnulaSalary(StringUtils.isBlank(record.getAppsSpouseAnnualSalary())?null:new BigDecimal(record.getAppsSpouseAnnualSalary()));
        customerAdditionalInfo.setSpouseCompanyName(record.getAppsSpouseCompanyName());
        customerAdditionalInfo.setSpouseDepartment(record.getAppsSpouseDepartment());
        customerAdditionalInfo.setSpouseExtentionNumber(record.getAppsSpouseExtentionNumber());
        customerAdditionalInfo.setSpouseIdNumber(record.getAppsSpouseIdNumber());
        customerAdditionalInfo.setSpouseIdType(record.getAppsSpouseIdType());
        customerAdditionalInfo.setSpouseIncomeSource(record.getAppsSpouseIncomeSource());
        customerAdditionalInfo.setSpouseMobile(record.getAppsSpouseMobile());
        customerAdditionalInfo.setSpouseNameChinese(record.getAppsSpouseNamechinese());
        customerAdditionalInfo.setSpouseOccupationLevel(record.getAppsSpousePositionLevel());
        customerAdditionalInfo.setSpouseOccupationPeriod(StringUtils.isBlank(record.getAppsSpouseOccupationPeriod())?null:Short.parseShort(record.getAppsSpouseOccupationPeriod()));
        customerAdditionalInfo.setSpouseOfficePhone(record.getAppsSpouseOfficePhone());
        customerAdditionalInfo.setProvince(record.getAppsProvince());
        customerAdditionalInfo.setCity(record.getAppsCity());
        customerAdditionalInfo.setDistrict(record.getAppsDistrict());
        customerAdditionalInfo.setCityVillageFlg(record.getAppsCityVillageFlg());
        customerAdditionalInfo.setCreateTime(LocalDateTime.now());
        customerAdditionalInfo.setUpdateTime(LocalDateTime.now());
        customerAdditionalInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        customerAdditionalInfo.setVersionNumber(1L);
        customerAdditionalInfo.setSocialSecurityCardNumber(record.getAppsSocialSecurityCardNumber());
        try {
            logger.info("Calling customerInfoService.addCustomerAdditionalInfo: customerId={}", customerAdditionalInfo.getCustomerId());
            customerInfoService.addCustomerAdditionalInfo(customerAdditionalInfo);
            logger.info("customerInfoService.addCustomerAdditionalInfo completed");
        } catch (Exception e) {
            logger.error("Failed to create customer additional information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateSubsidiaryCustomerBasicInfo(AccountOpeningDTO record) {
        logger.info("Calling customerInfoService.findCustomerBasicInfo: organizationNumber={}, subAppCustomerId={}", record.getOrganizationNumber(), record.getSubAppCustomerId());
        CustomerBasicInfoDTO customerBasicInfo = customerInfoService.findCustomerBasicInfo(record.getOrganizationNumber(),record.getSubAppCustomerId());
        logger.info("customerInfoService.findCustomerBasicInfo completed: customerBasicInfo={}", customerBasicInfo != null ? "found" : "null");
        customerBasicInfo.setStatementType(record.getAppStatementType());
        customerBasicInfo.setStatementAddressType(record.getAppStatementAddressType());
        customerBasicInfo.setSex(record.getAppsSex());
        customerBasicInfo.setMarital(record.getAppsMarital());
        customerBasicInfo.setQualification(record.getAppsQualification());
        customerBasicInfo.setBirthDate(StringUtils.isBlank(record.getAppsBirthDate())?null:LocalDate.parse(record.getAppsBirthDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerBasicInfo.setNationality(record.getAppsNationality());
        customerBasicInfo.setRace(record.getAppsRace());
        customerBasicInfo.setResidentProperty(record.getAppsResidentProperty());
        customerBasicInfo.setHouseType(record.getAppsHouseType());
        customerBasicInfo.setResidentPeriod(record.getAppsResidentPeriod());
        customerBasicInfo.setHomePhone(record.getAppsHomePhone());
        customerBasicInfo.setEmail(record.getAppsEmail());
        customerBasicInfo.setEmployerName(record.getAppsEmployerName());
        customerBasicInfo.setOfficePhone(record.getAppsOfficePhone());
        customerBasicInfo.setExtentionNumber(record.getAppsExtentionNumber());
        customerBasicInfo.setOccupation(record.getAppsOccupationCode());
        customerBasicInfo.setOccupationPeriod(record.getAppsOccupationPeriod());
        customerBasicInfo.setAnnualSalary(StringUtils.isBlank(record.getAppsAnnualSalary())?0L:Long.parseLong(record.getAppsAnnualSalary()));
        customerBasicInfo.setBusinessNature(record.getAppsBusinessNature());
        customerBasicInfo.setSalesManager(record.getAppSalesManager());
        customerBasicInfo.setNumberOfCrediteCard(StringUtils.isBlank(record.getAppNumberOfCrediteCard())?0:Integer.parseInt(record.getAppNumberOfCrediteCard()));
        customerBasicInfo.setHouseArea(StringUtils.isBlank(record.getAppsHouseArea())?null:new BigDecimal(record.getAppsHouseArea()));
        customerBasicInfo.setHouseLoan(StringUtils.isBlank(record.getAppsHouseLoan())?null:new BigDecimal(record.getAppsHouseLoan()));
        customerBasicInfo.setCompanyCategory(record.getAppsCompanyCategory());
        customerBasicInfo.setCompanySize(record.getAppsCompanySize());
        customerBasicInfo.setPositionLevel(record.getAppsPositionLevel());
        customerBasicInfo.setPositionTitle(record.getAppsPositionTitle());
        customerBasicInfo.setDepartmentName(record.getAppsDepartment());
        customerBasicInfo.setUpdateTime(LocalDateTime.now());
        customerBasicInfo.setUpdateBy(LoginUserUtils.getLoginUserName());
        //语言
        customerBasicInfo.setLanguage(record.getAppLanguage());
        customerBasicInfo.setDegree(record.getAppsDegree());

        //主卡 客户 三亲
        if (StringUtils.equals("1", record.getAppsSanqinIndicator())) {
            customerBasicInfo.setSanqinIndicator(record.getAppsSanqinIndicator());
            customerBasicInfo.setSanqinBranchCode(record.getAppsSanqinBranchCode());
            customerBasicInfo.setSanqinChannel(record.getAppsSanqinChannel());
            customerBasicInfo.setSanqinDate(record.getAppsSanqinDate());
            customerBasicInfo.setSanqinEmployeeNumber(record.getAppsSanqinEmployeeNumber());
            customerBasicInfo.setSanqinMethod(record.getAppsSanqinMethod());
        }

        boolean b = customerBasicInfo.getPrincipalSupplementaryInd().equals(CardBusinessConstant.PRIMARY_CUSTOMER_INDICATOR);
        if(b){
            customerBasicInfo.setPrincipalSupplementaryInd(CardBusinessConstant.PRIMARY_SUPPLEMENT_CUSTOMER_INDICATOR);
        }
        try {
            logger.info("Calling customerInfoService.modifyCustomerBasicInfoByPrimary: customerId={}", customerBasicInfo.getCustomerId());
            customerInfoService.modifyCustomerBasicInfoByPrimary(customerBasicInfo);
            logger.info("customerInfoService.modifyCustomerBasicInfoByPrimary completed");
        } catch (Exception e) {
            logger.error("Failed to update old subsidiary customer basic information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateSubsidiaryCustomerAuthorizationInfo(AccountOpeningDTO record, CustReconciliationControlDTO controlDTO) {
        CustomerAuthorizationInfoDTO customerAuthorizationInfo = queryCustomerAuthorizationInfo(record.getOrganizationNumber(),record.getSubAppCustomerId());
        customerAuthorizationInfo.setIdExpireDate(StringUtils.isBlank(record.getAppsIdExpireDate())?null:LocalDate.parse(record.getAppsIdExpireDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAuthorizationInfo.setIdStartDate(StringUtils.isBlank(record.getAppsIdStartDate())?null:LocalDate.parse(record.getAppsIdStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        customerAuthorizationInfo.setIdDepartment(record.getAppsIdDepartment());
        customerAuthorizationInfo.setType(record.getAppsCustomerType());
        customerAuthorizationInfo.setChineseName(record.getAppsChineseName());
        customerAuthorizationInfo.setEnglishName(record.getAppsEnglishName());
        customerAuthorizationInfo.setMobilePhone(record.getAppsMobilePhone());
        customerAuthorizationInfo.setGroupType(record.getAppsClass());
        customerAuthorizationInfo.setEcifNumber(record.getAppsEcifNumber());
        customerAuthorizationInfo.setUpdateTime(LocalDateTime.now());
        customerAuthorizationInfo.setUpdateBy(LoginUserUtils.getLoginUserName());

        try {
            logger.info("Calling customerAuthorizationService.modifyCustomerAuthorizationInfoByPrimary: customerId={}, idNumber={}", customerAuthorizationInfo.getCustomerId(), customerAuthorizationInfo.getIdNumber());
            customerAuthorizationService.modifyCustomerAuthorizationInfoByPrimary(customerAuthorizationInfo, controlDTO);
            logger.info("customerAuthorizationService.modifyCustomerAuthorizationInfoByPrimary completed");
        } catch (Exception e) {
            logger.error("Failed to update old subsidiary customer authorization information", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateSubsidiaryCustomerRelationshipInfo(AccountOpeningDTO record,
                                                         String customerId) {

        logger.info("Calling customerInfoService.findCustomerRelationshipInfo: organizationNumber={}, subAppCustomerId={}", record.getOrganizationNumber(), record.getSubAppCustomerId());
        List<CustomerRelationshipInfoDTO> customerRelationshipInfoDTOList = customerInfoService.findCustomerRelationshipInfo(record.getOrganizationNumber(),record.getSubAppCustomerId());
        logger.info("customerInfoService.findCustomerRelationshipInfo completed: size={}", customerRelationshipInfoDTOList != null ? customerRelationshipInfoDTOList.size() : 0);
        if(customerRelationshipInfoDTOList.isEmpty()){
            createSubsidiaryCustomerRelationshipInfo(record,customerId);
        }else{
            //删了重新添加
            try {
                for (CustomerRelationshipInfoDTO customerRelationshipInfoDTO:customerRelationshipInfoDTOList) {
                    logger.info("Calling customerInfoService.removeCustomerRelation: id={}", customerRelationshipInfoDTO.getId());
                    customerInfoService.removeCustomerRelation(customerRelationshipInfoDTO.getId());
                    logger.info("customerInfoService.removeCustomerRelation completed");
                }
                createSubsidiaryCustomerRelationshipInfo(record,customerId);
            } catch (Exception e) {
                logger.error("Failed to update subsidiary customer contact information", e);
                throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
            }
        }
    }

    public CustomerAuthorizationInfoDTO queryCustomerAuthorizationInfo(
            String organizationNumber, String customerId) {
        logger.info("Query customer authorization information based on organization number and customer ID, organization number:{}, customer ID:{}", organizationNumber, customerId);
        try {
            CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper
                    .selectByOrgNumberAndCustomerId(organizationNumber, customerId);
            if (customerAuthorizationInfo == null) {
                return null;
            }
            return BeanMapping.copy(customerAuthorizationInfo, CustomerAuthorizationInfoDTO.class);
        } catch (Exception e) {
            logger.error("Error querying customer authorization information based on organization number and customer ID", e);
            throw new AnyTxnCardholderException(AnyTxnCardholderResCodeEnum.D_ERR, e);
        }
    }


}
