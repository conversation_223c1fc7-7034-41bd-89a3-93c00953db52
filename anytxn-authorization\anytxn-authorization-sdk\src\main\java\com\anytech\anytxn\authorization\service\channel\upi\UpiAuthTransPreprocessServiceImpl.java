package com.anytech.anytxn.authorization.service.channel.upi;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.constants.InstallmentPriceFlagEnum;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.enums.UpiMTIEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.service.auth.AuthAutoInstallServiceImpl;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.authorization.base.service.upi.IUpiAuthTransPreprocessService;
import com.anytech.anytxn.authorization.base.utils.Field48Util;
import com.anytech.anytxn.authorization.base.utils.VerifyDateUtil;
import com.anytech.anytxn.common.core.enums.RuleTypeEnum;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmMarkUpFee;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.anytech.anytxn.parameter.common.mapper.unicast.system.ParmSysClassSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmSysClass;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmMarkupFeeSelfMapper;
/**
 * UPI
 * <AUTHOR>
 * @discription 授权字段校验，交易识别，组装接口
 * @create 2022-08-12 13:45
 **/
@Service
public class UpiAuthTransPreprocessServiceImpl implements IUpiAuthTransPreprocessService {
    private static final Logger logger = LoggerFactory.getLogger(UpiAuthTransPreprocessServiceImpl.class);
    @Autowired
    private RuleTransferImpl ruleTransferService;
    @Autowired
    private AuthAutoInstallServiceImpl authAutoInstallService;
    @Resource
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Resource
    private ParmSysClassSelfMapper sysClassSelfMapper;

    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Resource
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;

    @Resource
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;
    @Resource
    private ParmMarkupFeeSelfMapper parmMarkUpFeeSelfMapper;


    /**
     * 中国银联银行卡交换系统技术规范 第2部分 报文接口规范》域25服务点条件检查，定义范围
     */
    private static final List<String> SEVER_CODE_LIST =
            Arrays.asList("00", "01", "02", "03", "05", "06", "08", "10", "11", "12", "17","18", "28",
                    "42", "43", "45", "61","62", "63", "64", "66", "68", "70", "71", "73", "82", "83", "91");
    private static final List<String> MTI_START_VALUE = Arrays.asList("01", "02", "03");

    /**
     * 授权字段校验，交易识别，组装接口
     * @param iso8583Bo iso8583Bo
     * @return AuthRecordedDTO
     */
    @Override
    public AuthRecordedDTO preProcessAuthTrans(ISO8583DTO iso8583Bo) throws IOException {
        // 字段校验
        boolean bool = checkAuth(iso8583Bo.getFieldMap(), iso8583Bo.getMTI());
        if (!bool) {
            logger.error("Authorization field validation failed, response code set to 30");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL);
        }
        // mti值判断
        String mti = iso8583Bo.getMTI();
        Map<Integer, String> isoMap = iso8583Bo.getFieldMap();
        String processingCode = isoMap.get(Constants8583Field.FIELD3);
        if (!UpiMTIEnum.exist(mti)) {
            logger.error("MTI check failed: mti={}", mti);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_MTI_CHECK_FAIL);
        }
        // 交易识别,20开头,冲正,撤销,直接跳过交易识别
        boolean transCheck = ((StringUtils.isNotBlank(processingCode) && processingCode.startsWith("20"))
                            || Objects.equals(UpiMTIEnum.UPI_REVERSAL_TRANS.getCode(), mti));
        Map<String, String> map = null;
        // 1.交易识别
        if (!transCheck) {
            map = transIdentify(iso8583Bo);
        }
        // 2.AuthRecordedDTO初始内容封装    接口组装
        return buildAuthRecorded(iso8583Bo, map, mti);
    }

    /**
     * 2.1 字段校验
     *
     * @param map
     * @param mti
     * @return boolean
     */
    private boolean checkAuth(Map<Integer, String> map, String mti) {
        // 冲正 撤销冲正交易判断
        String firstTwo = map.get(Constants8583Field.FIELD3).substring(0, 2);
        boolean revocationReversalFlag =
                UpiMTIEnum.UPI_REVERSAL_TRANS.getCode().equals(mti) || "20".equals(firstTwo) || "17".equals(firstTwo);

        /** 1.域2主账号检查不能为空,且为数字型,长度必须为16位或19位,否则域39应答码赋值“30” */
        String field2 = map.get(Constants8583Field.FIELD2);
        boolean checkFlag = StringUtils.isBlank(field2) || !StringUtils.isNumeric(field2)
                || field2.length() < 14 || field2.length() > 19;
        if (checkFlag) {
            logger.error("Field 2 validation failed: field2={}", field2);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D2, field2);
        }
        /** 2.域3交易处理码检查不能为空,且为数字型,前2位须在规范范围内 */
        String field3 = map.get(Constants8583Field.FIELD3);
        checkFlag = StringUtils.isBlank(field3) || !StringUtils.isNumeric(field3)
                || !VerifyDateUtil.isTwoNumber(field3.substring(0, 2));
        if (checkFlag) {
            logger.error("Field 3 validation failed: field3={}", field3);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D3, field3);
        }
        /** 3.域4交易金额检查不能为空,且为数字型,必须大于等于0,否则域39应答码赋值“30” */
        // 4号域可以为空 做查询交易
        String field4 = map.get(Constants8583Field.FIELD4);
        checkFlag = StringUtils.isNotBlank(field4)
                && (!StringUtils.isNumeric(field4) || new BigDecimal(field4).compareTo(BigDecimal.ZERO) < 0);
        if (checkFlag) {
            logger.error("Field 4 validation failed: field4={}", field4);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D4,field4);
        }
        /** 4.域6持卡人扣账金额检查可以为空,如果不为空,必须为数字型且大于等于0,否则域39应答码赋值“30” */
        String field6 = map.get(Constants8583Field.FIELD6);
        checkFlag = StringUtils.isNotBlank(field6)
                && (!StringUtils.isNumeric(field6) || new BigDecimal(field6).compareTo(BigDecimal.ZERO) < 0);
        if (checkFlag) {
            logger.error("Field 6 validation failed: field6={}", field6);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D6, field6);
        }
        /** 5.域7交易传输时间检查格式:MMDDhhmmss,可以为空,如果不为空,必须为日期时间格式,否则域39应答码赋值“30” */
        String field7 = map.get(Constants8583Field.FIELD7);
        if (StringUtils.isNotBlank(field7) && !VerifyDateUtil.isDateMmddhhmmss(field7)) {
            logger.error("Field 7 validation failed: field7={}", field7);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D7, field7);
        }
        /** 6.域11系统跟踪号可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30” */
        String field11 = map.get(Constants8583Field.FIELD11);
        if (StringUtils.isNotBlank(field11) && !StringUtils.isNumeric(field11)) {
            logger.error("Field 11 validation failed: field11={}", field11);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D11, field11);
        }
        /** 7.域14卡片有效期格式:YYMM不能为空,且为数字型,必须为日期格式,否则域39应答码赋值“30” */
        // 撤销 冲正 撤销冲正 有效期时间可以不填
        /*if (!revocationReversalFlag) {
            String field14 = map.get(Constants8583Field.FIELD14);
            if (StringUtils.isNotBlank(field14) && !VerifyDateUtil.isDateYymm(field14)) {
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D14, field14);
            }
        }*/
        /** 8.域18商户类型检查如果域1为01xx、02xx、04xx，此域不能为空，且为数字型，否则域39应答码赋值“30” */
        String field18 = map.get(Constants8583Field.FIELD18);
        checkFlag = (MTI_START_VALUE.contains(mti.substring(0, 2)))
                && (StringUtils.isBlank(field18) || !StringUtils.isNumeric(field18));
        if (checkFlag) {
            logger.error("Field 18 validation failed: field18={}", field18);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D18, field18);
        }
        /**
         * 9.域22服务点输入方式码检查格式:2位PAN + 1位PIN不能为空,且为数字型,前2位须在PAN定义中,否则域39应答码赋值“30” PAN定义可参考《中国银联银行卡交换系统技术规范 第2部分
         * 报文接口规范》6.19小节
         */
        String field22 = map.get(Constants8583Field.FIELD22);
        if (!revocationReversalFlag && !Objects.equals(UpiMTIEnum.UPI_FINACIAL_NOTICE.getCode(), mti) && (StringUtils.isBlank(field22) || !StringUtils.isNumeric(field22)
                || !VerifyDateUtil.isTwoNumber(field22.substring(0, 2)))) {
            logger.error("Field 22 validation failed: field22={}", field22);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D22, field22);
        }
        /**
         * 10.域25服务点条件检查不能为空,且为数字型,须在服务点条件码定义中,否则域39应答码赋值“30” 服务点条件码定义可参考《中国银联银行卡交换系统技术规范 第2部分 报文接口规范》6.21小节
         */
        String field25 = map.get(Constants8583Field.FIELD25);
        if (StringUtils.isBlank(field25) || !StringUtils.isNumeric(field25) || !SEVER_CODE_LIST.contains(field25)) {
            logger.error("Field 25 validation failed: field25={}", field25);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D25, field25);
        }
        /** 11.域37检索参考号检查 可以为空，如果不为空，必须为数字型，否则域39应答码赋值“30” */
        String field37 = map.get(Constants8583Field.FIELD37);
        if (StringUtils.isNotBlank(field37) && !StringUtils.isAlphanumericSpace(field37)) {
            logger.error("Field 37 validation failed: field37={}", field37);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D37, field37);
        }

       /* boolean bo = Objects.equals(mti,"0200")
                && Objects.equals("300000",map.get(Constants8583Field.FIELD3))
                && Objects.equals("68",field25);

        if (!bo){
            *//** 12.域49交易货币代码检查不能为空,且为数字型,否则域39应答码赋值“30” *//*
            String field49 = map.get(Constants8583Field.FIELD49);
            if(!("89".equals(firstTwo) || "92".equals(firstTwo))){
                if (StringUtils.isBlank(field49) || !StringUtils.isNumeric(field49)) {
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D49, field49);
                }
            }
        }*/
        /** 13.域51持卡人账户货币代码可以为空,如果不为空,必须为数字型,否则域39应答码赋值“30” */
        String field51 = map.get(Constants8583Field.FIELD51);
        if (StringUtils.isNotBlank(field51) && !StringUtils.isNumeric(field51)) {
            logger.error("Field 51 validation failed: field51={}", field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL, AuthRepDetailEnum.D51, field51);
        }
        /**
         * 如果6号域即授权接口 authCardholderBillingAmount 不为空， 则51号域即授权接口中 authBillingCurrencyCode 不能为空，否则应答码赋值为96
         */
        if (StringUtils.isNotBlank(field6) && StringUtils.isBlank(field51)) {
            logger.error("Field 6 and 51 validation failed: field6={}, field51={}", field6, field51);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL,
                    AuthRepDetailEnum.D6E_D51);
        }
        /**
         * 如果51号域即授权接口 authBillingCurrencyCode 不为空， 则6号域即授权接口中 authCardholderBillingAmount 不能为空，否则应答码赋值为96
         */
        /*if(StringUtils.isBlank(field6) && StringUtils.isNotBlank(field51)){
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL,
                    AuthRepDetailEnum.D51E_D6);
        }*/

        /**
         * 48号域，如果不为空且‘IP’开头,则长度不能小于11
         */
        String field48 = map.get(Constants8583Field.FIELD48);
        if(StringUtils.isNotBlank(field48) && Objects.equals("IP",field48.substring(0, 2))){
            if(field48.length() < 11 ){
                logger.error("Field 48 validation failed: field48={}", field48);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_FIELD_ILLEGAL_FAIL,
                        AuthRepDetailEnum.D48);
            }
        }
        return true;
    }

    /**
     * 2.3 交易识别方式
     *
     * @param iso8583Bo
     * @return map
     */
    private Map<String, String> transIdentify(ISO8583DTO iso8583Bo) {
        logger.info("Calling rule transfer service for UPI transaction identification: org={}, mti={}", OrgNumberUtils.getOrg(), iso8583Bo.getMTI());
        Map<String, String> result = ruleTransferService.getUpiTransIdentifyCheckRule(OrgNumberUtils.getOrg(),iso8583Bo);
        logger.info("Rule transfer service completed for UPI transaction identification: org={}, resultSize={}", OrgNumberUtils.getOrg(), result != null ? result.size() : 0);
        return result;
    }

    /**
     * 组件接口
     * @return AuthRecordedDTO
     */
    private AuthRecordedDTO buildAuthRecorded(ISO8583DTO iso8583Bo, Map<String, String> stringStringMap, String mti) {
        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        if (stringStringMap != null) {
            String authTransactionTypeTopCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE);
            authRecordedDTO.setAuthTransactionTypeTopCode(authTransactionTypeTopCode);
            String authTransactionTypeDetailCode = stringStringMap.get(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE);
            authRecordedDTO.setAuthTransactionTypeDetailCode(authTransactionTypeDetailCode);
            String postingTransactionCode = stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE);
            authRecordedDTO.setPostingTransactionCode(postingTransactionCode);
            String postingTransactionCodeDev =
                    stringStringMap.get(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV);
            authRecordedDTO.setPostingTransactionCodeRev(postingTransactionCodeDev);
        }
        authRecordedDTO.setAuthGlobalFlowNumber(AuthThreadLocalManager.getTraceId());
        authRecordedDTO.setFirstGlobalFlowNumber(authRecordedDTO.getAuthGlobalFlowNumber());

        authRecordedDTO.setCurrentAuthLogId(authRecordedDTO.getAuthGlobalFlowNumber());
        authRecordedDTO.setAuthMessageTypeId(mti);

        Map<Integer, String> map = iso8583Bo.getFieldMap();
        authRecordedDTO.setAuthCardNumber(map.get(Constants8583Field.FIELD2));
        authRecordedDTO.setAuthProcessingCode(map.get(Constants8583Field.FIELD3));
        // 查询交易 4号域可以为空
        boolean boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD4))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD4));
        String currencyStr = StringUtils.isNotBlank(map.get(Constants8583Field.FIELD49))? map.get(Constants8583Field.FIELD49) : "702";
        authRecordedDTO.setAuthTransactionAmount(boolField ? BigDecimal.ZERO
                : getTxnAmountByCurrencyCode(map.get(Constants8583Field.FIELD4), currencyStr));
        // Field5
        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD5))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD5));
        authRecordedDTO.setAuthTransactionSettlementAmount(boolField ? BigDecimal.ZERO
                : new BigDecimal(map.get(Constants8583Field.FIELD5)).divide(new BigDecimal(AuthConstans.DIVIDE_100)));

        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD6))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD6));
        authRecordedDTO.setAuthCardholderBillingAmount(boolField ? BigDecimal.ZERO
                : new BigDecimal(map.get(Constants8583Field.FIELD6)).divide(new BigDecimal(AuthConstans.DIVIDE_100)));
        authRecordedDTO.setAuthTransmissionTime(map.get(Constants8583Field.FIELD7));
        // Field9
        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD9))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD9));
        authRecordedDTO.setSettlementConversionRate(boolField ? "" : map.get(Constants8583Field.FIELD9));

        boolField = StringUtils.isBlank(map.get(Constants8583Field.FIELD10))
                || !StringUtils.isNumeric(map.get(Constants8583Field.FIELD10));
        authRecordedDTO.setAuthCardholderBillingRate(
                boolField ? BigDecimal.ZERO : new BigDecimal(map.get(Constants8583Field.FIELD10)));
        authRecordedDTO.setAuthSystemTraceAuditNumber(map.get(Constants8583Field.FIELD11));
        authRecordedDTO.setAuthLocalTransactionTime(map.get(Constants8583Field.FIELD12));
        authRecordedDTO.setAuthLocalTransactionDate(map.get(Constants8583Field.FIELD13));
        // 有效期
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD14))) {
            authRecordedDTO.setAuthCardExpirationDate(map.get(Constants8583Field.FIELD14));
        } else {
            String cardExpireDate = map.get(Constants8583Field.FIELD35) == null ? null
                    : map.get(Constants8583Field.FIELD35).substring(17, 21);
            authRecordedDTO.setAuthCardExpirationDate(cardExpireDate);
        }
        authRecordedDTO.setAuthSettlementDate(map.get(Constants8583Field.FIELD15));
        // 18号域
        authRecordedDTO.setAuthMerchantType(map.get(Constants8583Field.FIELD18));
        authRecordedDTO.setAuthCardAcceptorBusinessCode(map.get(Constants8583Field.FIELD18));
        authRecordedDTO.setAuthMerchantCountryCode(map.get(Constants8583Field.FIELD19));
        authRecordedDTO.setAuthServicePointCardCode(StringUtils.isBlank(map.get(Constants8583Field.FIELD22)) ? null
                : map.get(Constants8583Field.FIELD22).substring(0, 2));
        authRecordedDTO.setAuthServicePointPinCode(StringUtils.isBlank(map.get(Constants8583Field.FIELD22)) ? null
                : map.get(Constants8583Field.FIELD22).substring(2, 3));
        authRecordedDTO.setAuthCardSequenceNumber(map.get(Constants8583Field.FIELD23));
        authRecordedDTO.setAuthServicePointConditionCode(map.get(Constants8583Field.FIELD25));
        authRecordedDTO.setAuthTransactionFeeIndicator(StringUtils.isBlank(map.get(Constants8583Field.FIELD28)) ? null
                : map.get(Constants8583Field.FIELD28).substring(0, 1));
        authRecordedDTO.setAuthTransactionFee(StringUtils.isBlank(map.get(Constants8583Field.FIELD28)) ? null
                : map.get(Constants8583Field.FIELD28).substring(1, 9));
        authRecordedDTO.setAuthAcquiringIdentificationCode(map.get(Constants8583Field.FIELD32));
        authRecordedDTO.setAuthForwardingIdentificationCode(map.get(Constants8583Field.FIELD33));
        authRecordedDTO.setAuthTrack2Data(map.get(Constants8583Field.FIELD35));
        // Field36 TODO
        authRecordedDTO.setAuthRetrievalReferenceNumber(map.get(Constants8583Field.FIELD37));
        authRecordedDTO.setAuthAuthIdentificationResponse(map.get(Constants8583Field.FIELD38));
        // 新增42域赋值
        authRecordedDTO.setMerchantId(map.get(Constants8583Field.FIELD42));
        // 新增43域赋值
        authRecordedDTO.setAuthCardAcceptorNameLocation(map.get(Constants8583Field.FIELD43));
        //55号域
        String f055 = map.get(Constants8583Field.FIELD55);
        authRecordedDTO.setAuthSystemRelatedData(f055);
/*        if (StringUtils.isNotBlank(f055)) {
            authRecordedDTO.setAuthSystemRelatedData(map.get(Constants8583Field.FIELD55).replaceFirst("9100", ""));
        } else {
        authRecordedDTO.setAuthSystemRelatedData(map.get(Constants8583Field.FIELD55));
        }*/
        // 新增57号域赋值
        authRecordedDTO.setAuthAdditionalTxnData(map.get(Constants8583Field.FIELD57));
        // 38号域 (预授权完成用到) 有些其他交易也会用到
        /*if(!"R009".equals(authRecordedDTO.getAuthTransactionTypeDetailCode())
               && StringUtils.isNotBlank(map.get(Constants8583Field.FIELD38))){
            log.error("目前非预授权完成交易,授权标识应答码都应为空,由系统自动生成");
           throw new AnyTXNBusRuntimeException("500","38号域值异常");
        }*/
        authRecordedDTO.setAuthResponseCode(AuthConstans.AUTH_CHECK_RESPONSE_CODE);
        authRecordedDTO.setAuthCardAcceptorTerminalCode(map.get(Constants8583Field.FIELD41));
        authRecordedDTO.setAuthCardAcceptorIdentification(map.get(Constants8583Field.FIELD42));
        authRecordedDTO.setAuthTrack1Data(map.get(Constants8583Field.FIELD45));
        if (map.get(Constants8583Field.FIELD48) != null){
            String f048 = map.get(Constants8583Field.FIELD48);
            Map<String,Object>  map48 = Field48Util.unpack48(String.format("%03d", f048.length())+f048);
            Map<String,String>  asMap = ( map48.get("AS")== null ? Collections.emptyMap() : (Map<String, String>)map48.get("AS"));
            String value = asMap.get("AO");
            String onValue = asMap.get("ON");
            authRecordedDTO.setAdditionalData(value);
        }
        authRecordedDTO.setAuthTransactionCurrencyCode(currencyStr);
        authRecordedDTO.setSettlementCurrencyCode(map.get(Constants8583Field.FIELD50));
        authRecordedDTO.setAuthBillingCurrencyCode(map.get(Constants8583Field.FIELD51));
        authRecordedDTO.setAuthPinData(map.get(Constants8583Field.FIELD52));
        authRecordedDTO.setAuthSecurityRelatedControlInformation(map.get(Constants8583Field.FIELD53));
        authRecordedDTO.setEncryptionMethodUsed(StringUtils.isBlank(map.get(Constants8583Field.FIELD53)) ? null
                : map.get(Constants8583Field.FIELD53).substring(1, 2));
        authRecordedDTO.setAuthIccSystemRelatedData(map.get(Constants8583Field.FIELD55));
        authRecordedDTO.setTerminalType(map.get(Constants8583Field.FIELD60_2_5));
        authRecordedDTO.setTransfereeName(map.get(Constants8583Field.FIELD61_6_2_NM_1));
        authRecordedDTO.setTurnOutPartyName(map.get(Constants8583Field.FIELD61_6_2_NM_2));
        String f116 = map.get(Constants8583Field.FIELD116);
        authRecordedDTO.setAuthCardIssuerReferenceData(StringUtils.isBlank(f116) ? "{}": f116);
        authRecordedDTO.setTransferPartyCardNumber(map.get(Constants8583Field.FIELD103));
        authRecordedDTO.setTurnOutPartyCardNumber(map.get(Constants8583Field.FIELD102));
        authRecordedDTO.setAuthAccountIdentification1(map.get(Constants8583Field.FIELD102));
        authRecordedDTO.setAuthAccountIdentification2(map.get(Constants8583Field.FIELD103));
        String f104 = iso8583Bo.getFieldMap().get(Constants8583Field.FIELD104);
        if(StringUtils.isNotBlank(f104)) {
            authRecordedDTO.setAuthTransSpecificData(f104);
            ObjectNode objectNode = JacksonUtils.readValue(f104, ObjectNode.class);
            JsonNode si = Optional.ofNullable(objectNode.get("SI")).orElse(JacksonUtils.objectNode());
            JsonNode cd = Optional.ofNullable(objectNode.get("CD")).orElse(JacksonUtils.objectNode());
            JsonNode em = Optional.ofNullable(objectNode.get("EM")).orElse(JacksonUtils.objectNode());
            authRecordedDTO.setSenderAccount(si.has("01") ? si.get("01").asText() : null);
            authRecordedDTO.setSenderName(si.has("02") ? si.get("02").asText() : null);
            authRecordedDTO.setSenderAccountBankNumber(si.has("17") ? si.get("17").asText() : null);
            authRecordedDTO.setSendingOrganizationChineseAbbreviation(si.has("14") ? si.get("14").asText() : null);
            authRecordedDTO.setClientIpAddress(cd.has("02") ? cd.get("02").asText() : null);
            authRecordedDTO.setMacAddress(cd.has("03") ? cd.get("03").asText() : null);
            authRecordedDTO.setMerchantInfo2ndSort(em.has("02") ? em.get("02").asText() : null);
            authRecordedDTO.setTradingAddress(em.has("04") ? em.get("04").asText() : null);
        }
        authRecordedDTO.setOpponentAccountName(map.get(Constants8583Field.FIELD1231));
        authRecordedDTO.setOpponentTxnArea(map.get(Constants8583Field.FIELD1232));
        authRecordedDTO.setOpponentTxnArea1(map.get(Constants8583Field.FIELD12321));
        authRecordedDTO.setOpponentTxnArea2(map.get(Constants8583Field.FIELD12322));
        authRecordedDTO.setOpponentBankId(map.get(Constants8583Field.FIELD1233));
        authRecordedDTO.setMerchantInfo2ndCode(map.get(Constants8583Field.FIELD1234));
        authRecordedDTO.setMerchantInfo2ndName(map.get(Constants8583Field.FIELD1235));
        authRecordedDTO.setFinancialNetWorkData(map.get(Constants8583Field.FIELD63));
        authRecordedDTO.setDirectoryServerTransactionId(map.get(Constants8583Field.FIELD12501));
        //如果都不同 取第一个值
        if (!Objects.equals(authRecordedDTO.getAuthAccountIdentification2(),authRecordedDTO.getAuthCardNumber())){
            authRecordedDTO.setOpponentAccountNumber(authRecordedDTO.getAuthAccountIdentification2());
        }
        if (!Objects.equals(authRecordedDTO.getAuthAccountIdentification1(),authRecordedDTO.getAuthCardNumber())){
            authRecordedDTO.setOpponentAccountNumber(authRecordedDTO.getAuthAccountIdentification1());
        }
        // 57号域 移动支付识别
        // 2位标志（AP） + 22位DPAN数据（19位DPAN+2位状态+1位设备卡类型），DPAN不足19位长，后补空格）识别移动支付交易（前两位=AP）
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD55))) {
            authRecordedDTO.setMobilePayIndicator(map.get(Constants8583Field.FIELD55).substring(0, 2));
        }
        authRecordedDTO.setAuthAuthTypeCode("1");
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD60))) {
            String field60 = map.get(Constants8583Field.FIELD60);
            authRecordedDTO.setField60TxnData(field60);
            //T 60.2.2 终端读取能力增加赋值
            authRecordedDTO.setAuthTerminalEntryCap(field60.substring(5,6));
            //F60.2.5：终端类型
            authRecordedDTO.setAuthTerminalType(field60.substring(8, 10));
            //F60.2.6：受理免验密码标志
            authRecordedDTO.setPassPinInd(field60.substring(10,11));
            //F60.2.8：电子商务标志
            authRecordedDTO.setElectronicCommerceIndicators(field60.substring(12, 14));
            // F60.2.9 Interactive Mode Identifier
            authRecordedDTO.setAuthInteractiveModeFlag(field60.substring(14, 15));
            // 60.3.5 交易发起方式增加赋值
            authRecordedDTO.setAuthTransactionMode(field60.substring(22, 23));
            //F60.3.6：交易介质
            authRecordedDTO.setAuthTransactionMedia(field60.substring(23, 24));
        }
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD61))) {
            String field61 = map.get(Constants8583Field.FIELD61);
            if (field61 != null && field61.length() >= 2) {
                authRecordedDTO.setAuthIdType(field61.substring(0, 2).trim());
            }
            if (field61 != null && field61.length() >= 22) {
                authRecordedDTO.setAuthIdNumber(field61.substring(2, 22).trim());
            }
            if (field61 != null && field61.length() >= 30) {
                authRecordedDTO.setAuthNoCardVerificationValue(field61.substring(27, 30));
            }
            if (field61 != null && field61.length() >= 37) {
                authRecordedDTO.setAuthFormatId(field61.substring(35, 37));
            }
            //AM 用法获取
            if (field61 != null && field61.length() >= 83) {
                authRecordedDTO.setAuthName(field61.substring(53,83).trim());
            }
            if (field61 != null && field61.length() >= 114) {
                authRecordedDTO.setAuthMobileNo(field61.substring(83,113).trim());
            }
            authRecordedDTO.setAuthAdditionalAuthenticationData(map.get(Constants8583Field.FIELD61_6_2_AM_2_5));
        }
        if (StringUtils.isNotBlank(map.get(Constants8583Field.FIELD90))) {
            String field90 = map.get(Constants8583Field.FIELD90);
            authRecordedDTO.setAuthOriginalMessageTypeId(field90.substring(0, 4));
            authRecordedDTO.setAuthOriginalSystemTraceAuditNumber(field90.substring(4, 10));
            authRecordedDTO.setAuthOriginalTransmissionTime(field90.substring(10, 20));
            authRecordedDTO.setAuthOriginalAcquiringIdentificationCode(field90.substring(20, 31));
            authRecordedDTO.setAuthOriginalForwardingIdentificationCode(map.get(Constants8583Field.FIELD33));
        }
        //根据mti和交易类型 确认 正向交易 撤销交易 冲正交易 等
        buildTransType(authRecordedDTO, map, mti);

        authRecordedDTO.setAuthTransactionSourceCode(iso8583Bo.getSourceCode());
        //构建自动分期
        buildAutoInstall(authRecordedDTO, map, stringStringMap);

        AuthThreadLocalManager.setIso8583dtoThreadLocal(iso8583Bo);
        //partnerId不为空的交易占额计算markup
        //花花世界交易，授权金额添加markupFee
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(authRecordedDTO.getAuthCardNumber(), OrgNumberUtils.getOrg());
        if (Objects.isNull(cardAuthorizationInfo)) {
            logger.error("Card information not found for cardNumber: {}", authRecordedDTO.getAuthCardNumber());
            AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(authRecordedDTO);
            throw new AnyTxnAuthException(AuthResponseCodeEnum.CARD_INVALID_CODE, AuthResponseCodeEnum.CARD_INVALID_CODE.getErrorDetailCode());
        }
        if(StringUtils.isNotBlank(cardAuthorizationInfo.getPartnerId())&&(!StringUtils.equalsAny(authRecordedDTO.getAuthTransactionCurrencyCode(),authRecordedDTO.getAuthBillingCurrencyCode()))
             && StringUtils.isNotBlank(authRecordedDTO.getAuthBillingCurrencyCode())){
            authRecordedDTO.setAuthCardholderBillingAmount(hhsjAddMarkupFeeAmount2(authRecordedDTO,cardAuthorizationInfo));
        }
        AuthThreadLocalManager.setAuthRecordeddtoThreadLocal(authRecordedDTO);
       return authRecordedDTO;
    }

    /**
     * 获取markupFee
     * @param authRecordedDTO
     * @param cardAuthorizationInfo
     * @return
     */
    private BigDecimal hhsjAddMarkupFeeAmount2(AuthRecordedDTO authRecordedDTO, CardAuthorizationInfo cardAuthorizationInfo) {
        String organizationNumber = cardAuthorizationInfo.getOrganizationNumber();
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(organizationNumber, cardAuthorizationInfo.getPrimaryCustomerId());
        ParmCardProductInfo parmCardProductInfo = parmCardProductInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, cardAuthorizationInfo.getProductNumber());
        String groupType = customerAuthorizationInfo == null ? "" : (customerAuthorizationInfo.getGroupType() == null ? "": customerAuthorizationInfo.getGroupType());
        String scheme = StringUtils.isEmpty(cardAuthorizationInfo.getCardScheme()) ? StringUtils.isEmpty(parmCardProductInfo.getScheme()) ? "" : parmCardProductInfo.getScheme() : cardAuthorizationInfo.getCardScheme();
        BigDecimal billAmount = authRecordedDTO.getAuthCardholderBillingAmount();
        ParmSysClass sysClass = sysClassSelfMapper.selectBySysClass(groupType);
        String sysInd = "N";
        if (sysClass != null) {
            sysInd = sysClass.getSysClassStaffInd() == null ? "N" : sysClass.getSysClassStaffInd();
        }
        Map<String, Object> map = new HashMap<>(5);
        map.put("authIndicator", "Y");
        map.put("cardScheme", scheme);
        map.put("groupType", sysInd);
        map.put("cardProductCode", cardAuthorizationInfo.getProductNumber());
        logger.info("Calling rule engine to get markup rule with parameters: authIndicator={}, cardScheme={}, groupType={}", map.get("authIndicator"), map.get("cardScheme"), map.get("groupType"));
        //基于规则类型找到规则匹配器
        logger.info("Getting rule matcher for markup fee rule: org={}, ruleType={}", organizationNumber, RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType());
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType(), organizationNumber);
        if (ruleMatcher == null) {
            logger.error("Rule matcher not found for markup fee rule: org={}, ruleType={}", organizationNumber, RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType());
            return null;
        }
        //规则因子数据
        //规则输入
        DataInputDTO dataInput = new DataInputDTO(map, RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType());
        //执行优先规则匹配
        logger.info("Executing rule matcher for markup fee rule: org={}, ruleType={}", organizationNumber, RuleTypeEnum.MARK_UP_FEE_RULE.getRuleType());
        Map<String, Object> ruleMap = ruleMatcher.execute(dataInput);
        if (ruleMap != null && !ruleMap.isEmpty()) {
            String markupFeeTableId = (String) ruleMap.get("tableId");
            logger.info("Matched markup rule: {}", (String) ruleMap.get("tableId"));
            if(StringUtils.isNotBlank(markupFeeTableId)){
                //查询转换费率
                ParmMarkUpFee parmMarkUpFee = parmMarkUpFeeSelfMapper.selectByOrgAndTableId(organizationNumber, markupFeeTableId);
                if(!ObjectUtils.isEmpty(parmMarkUpFee.getMarkUpFeeRate())&&parmMarkUpFee.getMarkUpFeeRate().compareTo(BigDecimal.ZERO)>0){
                    BigDecimal markupFee = billAmount.multiply(parmMarkUpFee.getMarkUpFeeRate());
                    authRecordedDTO.setAuthMarkupFee(markupFee);
                    billAmount = billAmount.add(markupFee);
                    return billAmount;
                }
            }
        }
        logger.error("Rule engine call failed: ruleType={}, tableId={}", "markUpFeeRule", ruleMap != null ? ruleMap.get("tableId") : null);
        throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.MARKUP_VALID_ERR, AuthRepDetailEnum.MARKUP_VALID_ERR);
    }

    /**
     * 构建交易类型
     * @param authRecordedDTO
     * @param map
     * @param mti
     */
    private void buildTransType(AuthRecordedDTO authRecordedDTO, Map<Integer, String> map, String mti){
        String pcode = map.get(Constants8583Field.FIELD3).substring(0, 2);
        String f25 = map.get(Constants8583Field.FIELD25);
        if (Objects.equals(pcode, "30")) {
            // 余额查询
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
        } else if (Objects.equals(mti, "0420")) {
            // 冲正
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVERSAL_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVERSAL_TRANS.getCode());
            if (Objects.equals(pcode, "03")) {
                // 预授权冲正
                authRecordedDTO.setPreAuth(true);
            } else if (Objects.equals(pcode, "00") && (Objects.equals(f25, "06") || Objects.equals(f25, "18"))) {
                // 预授权完成冲正
                authRecordedDTO.setPreAuthComplete(true);
                authRecordedDTO.setPreAuth(false);
            } else if (Objects.equals(pcode, "20") ) {
                // 预授权撤销冲正，预授权完成撤销冲正，消费撤销冲正
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode());
            }
            // Cash Withdrawal Reversal, Purchase Reversal
        } else if (Objects.equals(pcode, "24")) {
            // Remittance
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        } else if (Objects.equals(mti, "0100") && Objects.equals(pcode, "03")) {
            // 预授权请求
            authRecordedDTO.setPreAuth(true);
            authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_REQUEST.getCode());
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        } else if (Objects.equals(pcode, "20") && (Objects.equals(mti, "0100") || Objects.equals(mti, "0200"))) {
            // 撤销
            if (Objects.equals(mti, "0100")) {
                // 预授权撤销
                authRecordedDTO.setPreAuth(true);
                authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_REVOCATION.getCode());
            } else if (Objects.equals(mti, "0200") && (Objects.equals(f25, "06") || Objects.equals(f25, "18"))) {
                // 预授权完成撤销
                authRecordedDTO.setPreAuthComplete(true);
                authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_COMPLETED_REVOCATION.getCode());
            }
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REVOCATION_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REVOCATION_TRANS.getCode());
            // 消费撤销
        } else if (Objects.equals(pcode, "20") && Objects.equals(mti, "0220")) {
            // 退货
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.REFUNDS_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.REFUNDS_TRANS.getCode());
        } else if (Objects.equals(mti, "0200") && Objects.equals(pcode, "01")) {
            // 现金提取
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        } else if (Objects.equals(pcode, "29")) {
            // Primary Credit
            authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
            authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        } else if (Objects.equals(pcode, "49")) {
            // Account Funding
        } else if (Objects.equals(pcode, "33")) {
            // Account Verification
        } else if (Objects.equals(mti, "0100") && (Objects.equals(pcode, "89") || Objects.equals(pcode, "92"))) {
            // Commission Relationship
        } else if (!Objects.equals(mti, "0420") && Objects.equals(pcode, "00")) {
            if (Objects.equals(mti, "0200") && (Objects.equals(f25, "00") || Objects.equals(f25, "64") || Objects.equals(f25, "08") || Objects.equals(f25, "28"))) {
                // Purchase
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            } else if (Objects.equals(mti, "0200") && (Objects.equals(f25, "06") || Objects.equals(f25, "18"))) {
                // 预授权完成请求
                // Pre-authorization Completion
                authRecordedDTO.setPreAuthComplete(true);
                authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_COMPLETED.getCode());
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            } else if (Objects.equals(mti, "0220") && (Objects.equals(f25, "06") || Objects.equals(f25, "18"))) {
                // 预授权完成通知，结算通知
                // Settlement Advice
                authRecordedDTO.setPreAuthComplete(true);
                authRecordedDTO.setPreauthTxnType(PreAuthTransTypeEnum.PRE_COMPLETED.getCode());
                authRecordedDTO.setAuthReversalType(ReversalTypeEnum.AUNTH_TRANS.getCode());
                authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
            }
        }
        logger.info("Transaction type: {}, preAuth: {}, preAuthComplete: {}", authRecordedDTO.getAuthTransactionTypeCode(), authRecordedDTO.getPreAuth(), authRecordedDTO.getPreAuthComplete());
    }

    private void buildAutoInstall(AuthRecordedDTO authRecordedDTO, Map<Integer, String> map,
                              Map<String, String> stringStringMap){
        //是否是pos分期
        boolean isPosInstall = false;
        String field48 = map.get(Constants8583Field.FIELD48);
        String field25 = map.get(Constants8583Field.FIELD25);
        // 非分期交易
        if (!Objects.equals(field25, "64")) {
            return;
        }
        if (StringUtils.isNotBlank(field48) && field48.length() >= 2) {
            // 这里 只解析分期-IP用法
            String useType = field48.substring(0, 2);
            if (!"IP".equals(useType)) {
                return ;
            }
            if (field48.length() >= 11) {
                String termStr = field48.substring(2, 4);
                if (StringUtils.isNotBlank(termStr)) {
                    // 分期期数
                    authRecordedDTO.setTerm(Integer.valueOf(termStr));
                }
                //分期产品编号
                String installProductCode = field48.substring(4, 11);
                logger.info("Installment product service called with organizationNumber: {}, installProductCode: {}", authRecordedDTO.getOrganizationNumber(), installProductCode);
                InstallProductInfoResDTO installProductInfo = installProductInfoService.findByIndex(authRecordedDTO.getOrganizationNumber(), installProductCode);
                logger.info("Installment product service completed");
                if(null == installProductInfo){
                    logger.error("Installment product not found: org={}, installProductCode={}", authRecordedDTO.getOrganizationNumber(), installProductCode);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_NOT_EXISTS, AuthRepDetailEnum.ST_PRO_E);
                }
                if(installProductInfo.getTerm().compareTo(Integer.valueOf(termStr)) != 0){
                    logger.error("Installment term mismatch: expected={}, actual={}", Integer.valueOf(termStr), installProductInfo.getTerm());
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.TERM_INCONSISTENT);
                }
                authRecordedDTO.setInstallmentProductCode(installProductCode);
                authRecordedDTO.setInstallmentFeeReceiveFag(installProductInfo.getFeeReceiveFlag());
            }
            if (field48.length() >= 42) {
                String termRateStr = field48.substring(36, 42);
                boolean isNumber = VerifyDateUtil.isNumberAndNotAllZero(termRateStr);
                if (isNumber) {
                    // 分期总的费率
                    authRecordedDTO.setInstallmentFeeRate(
                            new BigDecimal(termRateStr).divide(new BigDecimal(1000), 3, BigDecimal.ROUND_DOWN));
                    // 分期定价方式
                    authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.DELIVERYRATE_FLAG.getCode());
                } else {
                    authRecordedDTO.setInstallmentFeeRate(BigDecimal.ZERO);
                    authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.BASICPRICING_FLAG.getCode());
                }
            } else {
                authRecordedDTO.setInstallmentFeeRate(BigDecimal.ZERO);
                authRecordedDTO.setInstallmentPriceFlag(InstallmentPriceFlagEnum.BASICPRICING_FLAG.getCode());
            }
            // 分期减免方式
            authRecordedDTO.setInstallmentDerateMethod(String.valueOf(AuthConstans.ZERO));
            isPosInstall = true;
        }
        //不是pos分期需要判断是否是自动分期
        if(!isPosInstall && stringStringMap != null){
            //自动分期
            logger.info("Auto installment service called");
            authAutoInstallService.autoInstall(authRecordedDTO);
            logger.info("Auto installment service completed");
        }
    }

    /**
     * 币种对应金额处理
     * @param transactionAmount
     * @param currencyStr
     * @return
     */
    private BigDecimal getTxnAmountByCurrencyCode(String transactionAmount ,String currencyStr) {
        ParmCurrencyCode currencyCode = parmCurrencyCodeSelfMapper.selectByCurrencyCode(currencyStr);
        Integer decimalPlace = currencyCode != null? currencyCode.getDecimalPlace() : 2;
        return new BigDecimal(transactionAmount).divide(new BigDecimal(10).pow(decimalPlace));
    }

}
