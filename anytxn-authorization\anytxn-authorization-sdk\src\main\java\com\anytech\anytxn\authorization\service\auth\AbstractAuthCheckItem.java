package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.base.enums.AuthResponseCodeMappingEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.AuthKeyConstant;
import com.anytech.anytxn.authorization.base.domain.dto.AuthCheckItemDTO;
import com.anytech.anytxn.authorization.base.domain.dto.DataResponse;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.service.auth.checkitem.CardOverUseItem;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlResDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionResDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckControlService;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthCheckDefinitionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 授权检查项抽象处理
 *
 * <AUTHOR>
 * @DATETIME: 2023/2/9 15:35
 */
@Component
public abstract class AbstractAuthCheckItem implements AuthCheckItemProcessService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractAuthCheckItem.class);
    
    @Autowired
    private RuleTransferImpl ruleTransferService;
    @Autowired
    private AuthCheckWayDetailServiceImpl authCheckWayDetailService;
    @Autowired
    private IAuthCheckDefinitionService authCheckDefinitionService;
    @Autowired
    private IAuthCheckControlService authCheckControlService;
    @Autowired
    private Map<String, AbstractCheckItem> checkItemMap;
    @Autowired
    private CardOverUseItem cardOverUseItem;


    /**
     * 具体检查项实现
     *
     * @param authorizationCheckProcessingPayload
     * @param dataResponseFuture
     * @return
     */
    @Override
    public int processAuthCheck(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, Future<DataResponse<Map<String, Object>>> dataResponseFuture) {
        //数据准备
        AuthCheckItemDTO authCheckItemDTO = prepareData(authorizationCheckProcessingPayload);
        //具体检查项检查
        int checkResult = processAuthCheckByPriority(authorizationCheckProcessingPayload, authCheckItemDTO);
        //反欺诈
        if (dataResponseFuture != null) {
            checkResult = anyCloudFraudCheckResult(dataResponseFuture, authorizationCheckProcessingPayload, authCheckItemDTO.getAuthCheckControlDTOList());
        }

        if (checkResult == AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode()) {
            return checkResult;
        }
        //检查结果处理
        return checkResProcess(authCheckItemDTO, authorizationCheckProcessingPayload.getAuthRecordedDTO());
    }

    private AuthCheckItemDTO prepareData(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthCheckItemDTO result = new AuthCheckItemDTO();
        //1.授权检查规则获取
        String tableId = tableIdGet(authorizationCheckProcessingPayload);
        logger.info("Retrieved auth check rule tableId: {}", tableId);
        //2.检查方式获取(根据tableId)
        String checkMethod = checkMethodGet(authorizationCheckProcessingPayload.getCardAuthorizationDTO(), tableId);
        //3.检查模板获取(根据tableId)
        List<ParmAuthCheckControlDTO> authCheckControlDTOList = checkControlDtosGet(authorizationCheckProcessingPayload.getCardAuthorizationDTO(), tableId);
        result.setTableId(tableId);
        result.setCheckMethod(checkMethod);
        result.setAuthCheckControlDTOList(authCheckControlDTOList);
        logger.info("Retrieved auth check method: {}", checkMethod);
        return result;
    }


    /**
     * 执行检查项前处理
     * 1:业务流程继续执行具体检查项
     * 0:无须做检查
     * -1:检查拒绝
     *
     * @param authorizationCheckProcessingPayload
     * @param authCheckControlList
     * @return
     */
    protected int applyCheckItemBefore(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, List<ParmAuthCheckControlDTO> authCheckControlList) {
        return AuthConstans.ONE;
    }

    ;

    /**
     * 执行检查项后处理
     *
     * @param authorizationCheckProcessingPayload
     * @return
     */
    protected void applyCheckItemAfter(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
    }

    ;

    /**
     * 检查结果的处理
     *
     * @param authCheckItemDTO
     * @param authRecordedDTO
     * @return
     */
    private int checkResProcess(AuthCheckItemDTO authCheckItemDTO, AuthRecordedDTO authRecordedDTO) {
        //检查结果处理 如果检查方式（check_method）= 0（检查所有项，按拒绝项的优先级响应）：
        if (CheckMethodEnum.INSPECT_ALL_PRIORITY.getCode().equals(authCheckItemDTO.getCheckMethod())) {
            return authCheckWayDetailService.authCheckConfirmOne(authCheckItemDTO.getAuthCheckControlDTOList(), authRecordedDTO);
        }
        // 如果检查方式（check_method）= 1（检查所有项，按第一个拒绝项响应）：
        if (CheckMethodEnum.INSPECT_ALL_FIRST.getCode().equals(authCheckItemDTO.getCheckMethod())) {
            return authCheckWayDetailService.authCheckConfirmTwo(authCheckItemDTO.getAuthCheckControlDTOList(), authRecordedDTO);
        }
        // 如果检查方式（check_method）= 2（检查第一个拒绝项就响应）：
        if (CheckMethodEnum.INSPECT_FIRST.getCode().equals(authCheckItemDTO.getCheckMethod())) {
            return authCheckWayDetailService.authCheckConfirmThree(authCheckItemDTO.getAuthCheckControlDTOList(), authRecordedDTO);
        }
        logger.error("check_method is not valid");
        throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_AUTH_CHECK_METHOD_ERROR);
    }

    /**
     * 反欺诈检查结果
     *
     * @param dataResponseFuture
     * @param authorizationCheckProcessingPayload
     * @param authCheckControlDTOList
     * @return
     */
    private int anyCloudFraudCheckResult(Future<DataResponse<Map<String, Object>>> dataResponseFuture, AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, List<ParmAuthCheckControlDTO> authCheckControlDTOList) {

        try {
            AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
            DataResponse<Map<String, Object>> mapDataResponse = dataResponseFuture.get();
            ParmAuthCheckControlDTO parmAuthCheckControlDTO = authCheckControlDTOList.stream().filter(t -> Objects.equals(t.getCheckItem(), AuthCheckItemEnum.ANY_CLOUD_FRAUD.getCheckItem())).findFirst().orElse(null);
            //存在欺诈检查项
            if (parmAuthCheckControlDTO != null) {
                //拒绝
                if (AuthCheckManager.anyCloudFraudReject(mapDataResponse, authRecordedDTO)) {
                    parmAuthCheckControlDTO.setCheckResult(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                    parmAuthCheckControlDTO.setCheckResponseCode(AuthResponseCodeEnum.BLOCK_CODE_ANYTXN_FRAUD_REJECT.getCode());
                    authRecordedDTO.setAuthAnyCloudFraudResultCode(AuthConstans.AUTH_CHECK_RESULT_REJECT);
                    return AuthItemCheckResCodeEnum.REJECT_CODE.getCode();
                } else {
                    parmAuthCheckControlDTO.setCheckResult(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                    parmAuthCheckControlDTO.setCheckResponseCode(AuthResponseCodeEnum.SUCCESS_CHECK_CODE.getCode());
                    authRecordedDTO.setAuthAnyCloudFraudResultCode(AuthConstans.AUTH_CHECK_RESULT_APPROVE);
                }
            }
        } catch (Exception e) {
            logger.error("Thread interrupted or execution exception occurred", e);
        }

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    ;

    /**
     * 授权检查项检查
     *
     * @param authorizationCheckProcessingPayload
     * @param authCheckItemDTO
     * @return
     */
    private int processAuthCheckByPriority(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload, AuthCheckItemDTO authCheckItemDTO) {
//        if (log.isInfoEnabled()) {
//            log.info("授权进入检查项AuthRecordedDTO：{}", DesensitizedUtils.getJson(authorizationCheckProcessingPayload.getAuthRecordedDTO()));
//        }
        int checkItemBeforeRes = applyCheckItemBefore(authorizationCheckProcessingPayload, authCheckItemDTO.getAuthCheckControlDTOList());
        if (AuthItemCheckResCodeEnum.REJECT_CODE.getCode() == checkItemBeforeRes) {
            return checkItemBeforeRes;
        }
        if (CollectionUtils.isEmpty(authCheckItemDTO.getAuthCheckControlDTOList())) {
            return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }

        //授权检查项赋值,2021/12/11 lpl
        authorizationCheckProcessingPayload.setParmAuthCheckControlDTOList(authCheckItemDTO.getAuthCheckControlDTOList());
        int resCode = AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();

        for (ParmAuthCheckControlDTO parmAuthCheckControlDto : authCheckItemDTO.getAuthCheckControlDTOList()) {
            AuthCheckItemEnum authCheckItemEnum = AuthCheckItemEnum.getEnum(parmAuthCheckControlDto.getCheckItem());
            String beanName = String.valueOf(authCheckItemEnum).concat(AuthKeyConstant.CHECK_ITEM);
            AbstractCheckItem abstractCheckItem = checkItemMap.get(beanName);
            if (abstractCheckItem == null) {
                logger.info("Unsupported check item: {}", parmAuthCheckControlDto.getCheckItem());
                continue;
            }
            resCode = abstractCheckItem.check(authorizationCheckProcessingPayload, parmAuthCheckControlDto);
            logger.info("authGlobalFlowNumber: {}, CheckItem: {}, CheckItem Result: {}", authRecordedDTO.getAuthGlobalFlowNumber(), authCheckItemEnum.getCheckItem() + "(" + authCheckItemEnum.getDescription() + ")", resCode == 0 ? "Approved" : "Rejected");
            //应答码映射，checkItem、authResponseCode、authResponseDetailCode相同开始映射
            responseCodeMapping(authRecordedDTO, parmAuthCheckControlDto);
            if (resGet(resCode, authCheckItemDTO.getCheckMethod())) {
                logger.info("authGlobalFlowNumber: {}, CheckItem: {}, CheckItem Result: {}, Response Result: {}", authorizationCheckProcessingPayload.getAuthRecordedDTO().getAuthGlobalFlowNumber(), parmAuthCheckControlDto.getCheckItem(), parmAuthCheckControlDto.getCheckResult(), parmAuthCheckControlDto.getCheckResponseCode());
                return resCode;
            }
        }
        applyCheckItemAfter(authorizationCheckProcessingPayload);
        //过度用卡作为最后检查不受配置控制
        return cardOverUseItem.cardOverUseCheck(authorizationCheckProcessingPayload, authCheckItemDTO.getAuthCheckControlDTOList(), resCode);
    }

    /**
     * 检查项响应码映射
     * 不同版本检查项响应码不一样，默认使用UPI映射码
     *
     * @param authRecordedDTO
     * @param parmAuthCheckControlDto
     */
    protected void responseCodeMapping(AuthRecordedDTO authRecordedDTO, ParmAuthCheckControlDTO parmAuthCheckControlDto) {
        if (AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())) {
            AuthResponseCodeMappingEnum authResponseCodeMappingEnum = Stream.of(AuthResponseCodeMappingEnum.values()).filter(mappingEnum -> Objects.equals(parmAuthCheckControlDto.getCheckItem(), mappingEnum.getCheckItem()) && Objects.equals(parmAuthCheckControlDto.getCheckResponseCode(), mappingEnum.getCode()) && Objects.equals(authRecordedDTO.getErrorDetailCode(), mappingEnum.getErrorDetailCode()) && Objects.equals(authRecordedDTO.getAuthTransactionSourceCode(), mappingEnum.getSourceCode())).findFirst().orElse(null);
            if (authResponseCodeMappingEnum == null) {
                authResponseCodeMappingEnum = Stream.of(AuthResponseCodeMappingEnum.values()).filter(mappingEnum -> Objects.equals(parmAuthCheckControlDto.getCheckItem(), mappingEnum.getCheckItem()) && Objects.equals(parmAuthCheckControlDto.getCheckResponseCode(), mappingEnum.getCode()) && Objects.equals(authRecordedDTO.getErrorDetailCode(), mappingEnum.getErrorDetailCode())).findFirst().orElse(null);
            }
            if (authResponseCodeMappingEnum != null) {
                authRecordedDTO.setAuthResponseCode(authResponseCodeMappingEnum.getUpiCode());
                authRecordedDTO.setErrorDetailCode(authResponseCodeMappingEnum.getUpiErrorDetailCode());
            }
        }
    }

    /**
     * boolean INSPECT_FIRST = 2
     *
     * @param resCode     返回码值
     * @param checkMethod {@link CheckMethodEnum}
     * @return boolean
     */
    private boolean resGet(int resCode, String checkMethod) {
        return (resCode == AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode() || (CheckMethodEnum.INSPECT_FIRST.getCode().equals(checkMethod) && resCode == AuthItemCheckResCodeEnum.REJECT_CODE.getCode()));
    }

    /**
     * 授权检查规则tableId获取
     *
     * @return String
     */
    private String tableIdGet(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        logger.info("Calling ruleTransferService.getAuthCheckRule for auth check rule retrieval");
        Map<String, String> authCheckObject = ruleTransferService.getAuthCheckRule(authorizationCheckProcessingPayload);
        if (authCheckObject == null || StringUtils.isEmpty(authCheckObject.get(AuthConstans.TABLE_ID))) {
            logger.error("Auth check rule match failed, tableId is null or empty");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_AUTH_RULE_MATCH_FAILE, AuthRepDetailEnum.AU_CH);
        }
        return authCheckObject.get(AuthConstans.TABLE_ID);
    }

    /**
     * 授权检查方式的获取
     *
     * @return String
     */
    private String checkMethodGet(CardAuthorizationDTO cardAuthorizationDTO, String tableId) {
        logger.info("Calling authCheckDefinitionService.findByOrgAndTableId for check method retrieval, org: {}, tableId: {}", cardAuthorizationDTO.getOrganizationNumber(), tableId);
        AuthCheckDefinitionResDTO authCheckDefinition = authCheckDefinitionService.findByOrgAndTableId(cardAuthorizationDTO.getOrganizationNumber(), tableId);
        final boolean checkRes = authCheckDefinition == null || StringUtils.isEmpty(authCheckDefinition.getCheckMethod());
        if (checkRes) {
            logger.error("Auth check method retrieval failed, authCheckDefinition is null or checkMethod is empty");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_AUTH_CHECK_METHOD_FAILE);
        }
        return authCheckDefinition.getCheckMethod();
    }

    /**
     * 授权检查模板获取
     *
     * @return list
     */
    private List<ParmAuthCheckControlDTO> checkControlDtosGet(CardAuthorizationDTO cardAuthorizationDTO, String tableId) {
        logger.info("Calling authCheckControlService.findByOrgAndTableId for check control template retrieval, org: {}, tableId: {}", cardAuthorizationDTO.getOrganizationNumber(), tableId);
        List<AuthCheckControlResDTO> authCheckControlResList = authCheckControlService.findByOrgAndTableId(cardAuthorizationDTO.getOrganizationNumber(), tableId);

        if (CollectionUtils.isEmpty(authCheckControlResList)) {
            return new ArrayList<>();
        }
        List<ParmAuthCheckControlDTO> checkControlDTOList = BeanMapping.copyList(authCheckControlResList, ParmAuthCheckControlDTO.class);

        return checkControlDTOList.stream().sorted(Comparator.comparing(ParmAuthCheckControlDTO::getCheckPriority)).collect(Collectors.toList());
    }
}
