package com.anytech.anytxn.authorization.service.channel.onus;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.enums.OnusAuthTransTypeEnum;
import com.anytech.anytxn.authorization.service.channel.AbstractTransRoutingService;
import com.anytech.anytxn.authorization.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.base.service.onus.IOnusTransactionClassifyService;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Author: sukang
 * @Date: 2023/7/6 10:09
 * @Description:
 */
@Service
public class OnusTransactionClassifyServiceImpl implements IOnusTransactionClassifyService {

    private static final Logger logger = LoggerFactory.getLogger(OnusTransactionClassifyServiceImpl.class);

    @Override
    public int processAuthTrans(AuthRecordedDTO authRecordedDTO, ISO8583DTO iso8583Dto) throws Exception {
        String transType = OnusAuthTransTypeEnum.getTransType(iso8583Dto);
        authRecordedDTO.setAuthTransType(transType);

        boolean containsBean = ApplicationManager.applicationContext.containsBean(transType.concat("_Service"));
        if (containsBean){
            AbstractTransRoutingService transRoutingService = ApplicationManager.applicationContext
                    .getBean(transType.concat("_Service"), AbstractTransRoutingService.class);

            logger.info("Calling trans routing service: transType={}", transType);
            transRoutingService.trans(authRecordedDTO,iso8583Dto);
        } else {
            logger.warn("No routing service found for transType: {}", transType);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

}
