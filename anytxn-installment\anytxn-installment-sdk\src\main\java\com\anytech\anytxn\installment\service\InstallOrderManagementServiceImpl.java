package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentFunctionCodeEnum;
import com.anytech.anytxn.installment.base.enums.InstallmentOrderStatusEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.installment.base.service.IInstallOrderManagementService;
import com.anytech.anytxn.installment.base.service.IInstallOrderTerminatedService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:46
 * 分期订单管理
 **/
@Service
public class InstallOrderManagementServiceImpl implements IInstallOrderManagementService {
    private static final Logger logger = LoggerFactory.getLogger(InstallOrderManagementServiceImpl.class);
    @Autowired
    private InstallOrderSelfMapper installOrderSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private IInstallOrderTerminatedService installOrderTerminatedService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    /**
     * 分期订单管理
     * @param status 状态
     * @param outstandingTransaction 未并账实体参数
     * @param orderId 分期订单编号
     * @return int
     */
    @Override
    public int installmentOrderManagement(String status, OutstandingTransactionDTO outstandingTransaction, String orderId) {
        logger.info("Started installment order management, installment orderId: {}", orderId);
        if (StringUtils.isBlank(status)) {
            logger.error("Order management installment status cannot be empty, status: {}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.OR_MA);
        }
        //订单管理的条件
        if (!(Objects.equals(InstallmentFunctionCodeEnum.RETURNGOODS.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.CANCEL.getCode(), status))) {
            logger.error("Installment order management installment status input error, status: {}", status);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_STATUS_NOT_CORRECT_FAULT);
        }
        //退货  撤销逻辑检查
        if (InstallmentFunctionCodeEnum.RETURNGOODS.getCode().equals(status)
                || InstallmentFunctionCodeEnum.CANCEL.getCode().equals(status)) {
            return returnAndCancel(outstandingTransaction, status, orderId);
        }
        //提前结清、强制结清、拒付检查
        if (Objects.equals(InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status)
                || Objects.equals(InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(), status)) {
            return settleAndRefusal(status,orderId);
        }
        return 0;
    }
    /**
     * 提前结清、强制结清、拒付检查
     * @param orderId
     * @param status
     * @return int
     **/
    public int settleAndRefusal(String status, String orderId)
    {
        InstallOrder install = installOrderMapper.selectByPrimaryKey(orderId);
        if (install == null) {
            logger.error("Queried installment order table does not exist, orderId: {}", orderId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
        }
        if (!Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), install.getStatus())) {
            logger.error("Installment order status is not normal, status: {}", install.getStatus());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_INST_STATUS_NOT_CORRECT_FAULT);
        }
        if (Objects.equals(InstallmentFunctionCodeEnum.EARLYSETTLEMENT.getCode(), status)) {
            install.setStatus(InstallmentOrderStatusEnum.ACTIVE_ADVANCESETTLEMENT_STATUS.getCode());
        }
        if (Objects.equals(InstallmentFunctionCodeEnum.FORCESETTLEMENT.getCode(), status)) {
            install.setStatus(InstallmentOrderStatusEnum.PASSIVE_ADVANCESETTLEMENT_STATUS.getCode());
        }
        if (Objects.equals(InstallmentFunctionCodeEnum.REFUSEPAYMENT.getCode(), status)) {
            install.setStatus(InstallmentOrderStatusEnum.REFUSEPAYMENT_STATUS.getCode());
        }
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", install.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(install.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);
        if(organizationInfo == null){
            logger.error("Organization parameters do not exist, organizationNumber: {}", install.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        install.setStatusUpdateDate(organizationInfo.getNextProcessingDay());
        install.setUpdateTime(LocalDateTime.now());
        try {
            logger.info("Completed installment order management, early settlement, forced settlement, refusal payment check, installment orderId: {}", orderId);
            return installOrderMapper.updateByPrimaryKey(install);
        } catch (Exception e) {
            logger.error("Installment order termination modify order table exception, status: {}", e);
        }
        return 0;
    }
    /**
     * 退货，撤销
     * @param outstandingTransaction
     * @param status
     * @return int
     **/
    public int returnAndCancel(OutstandingTransactionDTO outstandingTransaction,String status,String orderId)
    {
        String indicator = "0";
        String orgNum = outstandingTransaction.getOrganizationNumber();
        InstallOrder install = installOrderMapper.selectByPrimaryKey(orderId);
        String accountManagementId = install.getAccountManagementId();
        String productCode = outstandingTransaction.getInstalmentProduct();
        LocalDate transactionDate = outstandingTransaction.getTransactionDate();
        String authorizationCode = outstandingTransaction.getAuthorizationCode();
        BigDecimal installmentAmount = outstandingTransaction.getTransactionAmount();
        String globalFlowNumber = outstandingTransaction.getOriginalGlobalFlowNumber();
        //检查原始交易是否存在
        String postIndicator = outstandingTransaction.getPostIndicator();
        InstallOrder installOrder = null;
        //未并账交易是准实时入账
        if (Objects.equals(indicator, postIndicator))
        {
            installOrder = installOrderSelfMapper.selectByOrgNumAndAccManageAndGlobalNum(orgNum, accountManagementId, globalFlowNumber);
            if (installOrder == null)
            {
                logger.error("Installment order management installment order table does not exist, orgNum: {}, accountManagementId: {}, globalFlowNumber: {}", orgNum, accountManagementId, globalFlowNumber);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
            }
            if (!Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), installOrder.getStatus())) {
                logger.error("Return goods cancellation installment order status must be normal, status: {}", installOrder.getStatus());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_STATUS_NOT_NORMAL_FAULT);
            }
        }else {
            //卡号、分期产品代码、授权码、分期金额去读分期订单表
            int exists = installOrderSelfMapper.isExistsByIndex(accountManagementId, productCode,
                    transactionDate, authorizationCode, installmentAmount);
            if (exists < 0) {
                logger.error("Queried installment order table does not exist, exists: {}", exists);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_NOT_EXIST_FAULT);
            }
            installOrder = installOrderSelfMapper.orderByManageAndCodeAndDateAndAcquire(
                    accountManagementId, productCode, transactionDate, authorizationCode, installmentAmount);
            if (!Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), installOrder.getStatus())) {
                logger.error("Return goods cancellation installment order status must be normal, status: {}", installOrder.getStatus());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_STATUS_NOT_NORMAL_FAULT);
            }
        }
        if (Objects.equals(InstallmentFunctionCodeEnum.RETURNGOODS.getCode(), status)) {
            installOrder.setStatus(InstallmentOrderStatusEnum.REFUNDS_STATUS.getCode());
        }
        //撤销特殊处理
        if (Objects.equals(InstallmentFunctionCodeEnum.CANCEL.getCode(), status) && Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), installOrder.getStatus())) {
            //分期订单终止
            try {
                InstallOrderDTO installOrderDTO = new InstallOrderDTO();
                BeanMapping.copy(installOrder, installOrderDTO);
                logger.info("Calling installOrderTerminatedService.installmentOrderTerminated: status={}, orderId={}", status, installOrderDTO.getOrderId());
                int result = installOrderTerminatedService.installmentOrderTerminated(status,installOrderDTO);
                logger.info("installOrderTerminatedService.installmentOrderTerminated completed: result={}", result);
                if (result == 0) {
                    logger.error("Installment order termination failed, result: {}", 0);
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_TERMINATED_FAULT);
                }
            } catch (Exception e) {
                logger.error("Call installment order termination exception, Exception: {}", e);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CALL_INST_ORDER_TERMINATED_FAULT, e);
            }
            installOrder.setStatus(InstallmentOrderStatusEnum.CANCLE_STATUS.getCode());
        }
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", installOrder.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrder.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);
        if(organizationInfo == null){
            logger.error("Organization parameters do not exist, organizationNumber: {}", installOrder.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);
        }
        installOrder.setStatusUpdateDate(organizationInfo.getNextProcessingDay());
        installOrder.setUpdateTime(LocalDateTime.now());
        try {
            logger.info("Completed installment order management, return goods, cancellation, installment orderId: {}", orderId);
            return installOrderMapper.updateByPrimaryKey(installOrder);
        } catch (Exception e) {
            logger.error("Call installment order termination modify order table exception, Exception: {}", e);
        }
        return 0;
    }

}
