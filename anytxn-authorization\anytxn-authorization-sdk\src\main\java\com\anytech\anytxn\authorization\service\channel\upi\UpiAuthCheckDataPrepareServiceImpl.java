package com.anytech.anytxn.authorization.service.channel.upi;


import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.enums.CardClassEnum;
import com.anytech.anytxn.authorization.base.enums.PreAuthTransTypeEnum;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.service.auth.AuthCheckDataPrepareServiceImpl;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.UpiAuthDataUpdateManager;
import com.anytech.anytxn.authorization.base.service.rule.IRuleService;
import com.anytech.anytxn.business.dao.customer.model.CorporateRegistrationInfo;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.account.service.CommonAccountService;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.customer.domain.dto.CorporateCustomerInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CorporateRegistrationInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateCustomerInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateDownTopReferenceSelfMapper;
import com.anytech.anytxn.business.dao.customer.mapper.CorporateRegistrationInfoMapper;
import com.anytech.anytxn.business.dao.customer.model.CorporateCustomerInfo;
import com.anytech.anytxn.business.dao.customer.model.CorporateDownTopReference;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.common.service.SharedInfoFindServiceImpl;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitTrialResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAuthorisationProcessingService;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AccountGroupAuthControlDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAccountGroupAuthControlService;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthorizationRuleService;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.product.IProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.transaction.base.domain.dto.InterestTrialDTO;
import com.anytech.anytxn.transaction.base.service.IInterestTrialService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description upi数据准备
 */
@Service
public class UpiAuthCheckDataPrepareServiceImpl {
    private static final Logger logger = LoggerFactory.getLogger(UpiAuthCheckDataPrepareServiceImpl.class);

    @Resource
    private CommonAccountService commonAccountService;

    @Autowired
    private IAuthorizationRuleService authorizationRuleService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private ICardProductInfoService cardProductInfoService;
    @Autowired
    private IProductInfoService productInfoService;
    @Autowired
    private IAuthorisationProcessingService authorisationProcessingService;
    @Autowired
    private SharedInfoFindServiceImpl sharedInfoFindService;
    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Resource
    private IRuleService ruleService;
    @Resource
    private IAccountGroupAuthControlService accountGroupAuthControlService;
    @Resource
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;
    @Resource
    private CorporateCustomerInfoMapper corporateCustomerInfoMapper;
    @Resource
    private CorporateRegistrationInfoMapper corporateRegistrationInfoMapper;
    @Resource
    private CorporateDownTopReferenceSelfMapper corporateDownTopReferenceSelfMapper;
    @Resource
    private CorporateCustomerInfoSelfMapper corporateCustomerInfoSelfMapper;
    @Resource
    private LimitRequestPrepareService limitRequestPrepareService;
    @Resource
    private IInterestTrialService iInterestTrialServiceImpl;
    @Resource
    private UpiAuthDataUpdateManager upiAuthDataUpdateManager;
    @Autowired
    private IPreAuthorizationLogService preAuthorizationLogService;
    @Autowired
    private ISystemTableService iSystemTableService;
    @Autowired
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;

    /**
     * 授权数据准备.校验
     *
     * @param authRecordedDTO
     *            {@link AuthRecordedDTO}
     * @return AuthorizationCheckProcessingDTO
     */
    public AuthorizationCheckProcessingPayload prepareAuthData(AuthRecordedDTO authRecordedDTO) {
        if (logger.isInfoEnabled()){
            logger.debug("Data preparation request AuthRecordedDTO: cardNumber={}, transactionType={}, amount={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                authRecordedDTO.getAuthTransactionTypeCode(), 
                authRecordedDTO.getAuthTransactionAmount());
        }
        // 授权检查处理容器
        AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();
        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
        // 1.卡相关信息
        CardAuthorizationDTO cardAuthorizationDTO = cardRelative(authRecordedDTO, authorizationCheckProcessingPayload);
        if (cardAuthorizationDTO == null) {
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CARD_NOT_EXISTS_CODE.getCode());
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.CARD_NOT_EXISTS_CODE);
            return authorizationCheckProcessingPayload;
        }
        // 批中需要检查
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().checkCardAuthorization(cardAuthorizationDTO);
        }
        // 获取机构相关信息
        logger.info("Calling organizationInfoService.findOrganizationInfo: orgNumber={}", cardAuthorizationDTO.getOrganizationNumber());
        OrganizationInfoResDTO orgInfo = organizationInfoService.findOrganizationInfo(cardAuthorizationDTO.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: orgNumber={}, orgName={}", 
            cardAuthorizationDTO.getOrganizationNumber(), orgInfo != null ? orgInfo.getOrganizationName() : "null");
        if (orgInfo == null) {
            authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORGANIZATION_NOT_EXISTS_CODE);
            return authorizationCheckProcessingPayload;
        }
        // 机构参数
        authorizationCheckProcessingPayload.setOrgInfo(orgInfo);
        authRecordedDTO.setOrganizationNumber(orgInfo.getOrganizationNumber());
        // 系统配置
        SystemTableDTO systemConfig = getSystemConfig();
        authorizationCheckProcessingPayload.setSystemInfo(systemConfig);

        // 2 8、6号域以及51号域的赋值逻辑改造设计
        AuthResponseCodeEnum handle6And51Result = handleField6AndField51(authRecordedDTO, orgInfo, cardAuthorizationDTO.getProductNumber());
        if (AuthCheckManager.UpiIsFailure(handle6And51Result)) {
            authRecordedDTO.setErrorDetail(handle6And51Result);
            return authorizationCheckProcessingPayload;
        }
        // 3 获取并设置 账产品组控制信息
        AuthResponseCodeEnum handleAccountProductResult = getAndSetAccountGroupControlInfo(cardAuthorizationDTO, authRecordedDTO,
                authorizationCheckProcessingPayload);
        if (AuthCheckManager.UpiIsFailure(handleAccountProductResult)) {
            authRecordedDTO.setErrorDetail(handleAccountProductResult);
            return authorizationCheckProcessingPayload;
        }
        // 4.产品相关信息
        productRelative(authRecordedDTO, authorizationCheckProcessingPayload);
        // 5 获取主账户信息，未并账交易阶段使用
        accountRelative(authRecordedDTO, authorizationCheckProcessingPayload);
        // 6.客户相关信息
        customerRelative(cardAuthorizationDTO, authRecordedDTO, authorizationCheckProcessingPayload);
        // 7.核心流程处理前逻辑
        preCoreProcess(authorizationCheckProcessingPayload);
        return authorizationCheckProcessingPayload;
    }

    /**
     * 获取系统参数
     * @return {@link SystemTableDTO}
     */
    private SystemTableDTO getSystemConfig(){
        logger.info("Calling iSystemTableService.findBySystemId: systemId=0000");
        SystemTableDTO systemConfig = iSystemTableService.findBySystemId("0000");
        logger.info("iSystemTableService.findBySystemId completed: systemId=0000, result={}", systemConfig != null ? "success" : "null");
        if(systemConfig!=null){
            return systemConfig;
        }else {
            logger.error("System configuration not found: systemId=0000");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_SYSTEM_FAIL);
        }
    }

    private void preCoreProcess(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        //1.入账金额逻辑处理
        handleAccountAmount(authRecordedDTO,authorizationCheckProcessingPayload);
        //2 缓存数据加载
        initCacheData(authorizationCheckProcessingPayload);
        //3 预授权完成逻辑处理
        preAuthComplete(authorizationCheckProcessingPayload);
    }

    private void preAuthComplete(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        if (authRecordedDTO.getPreAuthComplete() && AuthTransTypeEnum.NORMAL_TRANS.getCode()
                .equals(authRecordedDTO.getAuthTransactionTypeCode())){
            //3预授权完成逻辑处理
            logger.info("Calling preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType: cardNumber={}, authCode={}, transType={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                authRecordedDTO.getAuthAuthIdentificationResponse(), 
                PreAuthTransTypeEnum.PRE_REQUEST.getCode());
            PreAuthorizationLogDTO preAuthorizationLogDTO = preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType(
                    authRecordedDTO.getAuthCardNumber(),authRecordedDTO.getAuthAuthIdentificationResponse()
                    , PreAuthTransTypeEnum.PRE_REQUEST.getCode());
            logger.info("preAuthorizationLogService.getByCardNumAndAuthCodeAndTransType completed: cardNumber={}, result={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                preAuthorizationLogDTO != null ? "found" : "not found");

            //预授权完成在核心逻辑处理前需要先将额度释放
            limitRequestPrepareService.recoverLimitForPreAuthComplete(authorizationCheckProcessingPayload,preAuthorizationLogDTO);
        }
    }

    private void initCacheData(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        upiAuthDataUpdateManager.initLimitCustAccountBO(authorizationCheckProcessingPayload);
    }

    /**
     * 1.如果是销户类交易 并且不是冲正也不是撤销
     *   a. 调用利息试算接口，得到取现利息，消费利息，贷记利息三个值，用取现利息+消费利息-贷记利息 =AMOUNT1(可能为负数);
     *   b. 调用额度试算接口，得到可用额度AMOUNT2;
     *   c. AMOUNT3=AMOUNT2+AMOUNT1
     */
    private void handleAccountAmount(AuthRecordedDTO authRecordedDTO, AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        if (AuthCheckManager.isCancelAccount(authRecordedDTO.getAuthTransactionTypeDetailCode(),authRecordedDTO.getAuthTransactionTypeCode())){
            logger.info("Calling limitRequestPrepareService.getLimitAvailable: cardNumber={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            CalLimitTrialResDTO limitAvailable = limitRequestPrepareService.getLimitAvailable(authorizationCheckProcessingPayload);
            logger.info("limitRequestPrepareService.getLimitAvailable completed: cardNumber={}, minAvailableAmount={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                limitAvailable != null ? limitAvailable.getMinAvailableAmount() : "null");
            InterestTrialDTO interestTrialDto = new InterestTrialDTO();
            interestTrialDto.setCardNumber(authRecordedDTO.getAuthCardNumber());
            interestTrialDto.setBillingCurrency(authRecordedDTO.getAuthBillingCurrencyCode());
            interestTrialDto.setExpireFlag(String.valueOf(AuthConstans.ONE));
            interestTrialDto.setDeadlineDate(authorizationCheckProcessingPayload.getOrgInfo().getNextProcessingDay());
            interestTrialDto.setOrganizationNumber(authRecordedDTO.getOrganizationNumber());
            logger.info("Calling iInterestTrialServiceImpl.interestCalculat: cardNumber={}, billingCurrency={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                authRecordedDTO.getAuthBillingCurrencyCode());
            iInterestTrialServiceImpl.interestCalculat(interestTrialDto);
            logger.info("iInterestTrialServiceImpl.interestCalculat completed: cardNumber={}, cashInterest={}, consumeInterest={}, creditInterest={}", 
                DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), 
                interestTrialDto.getCashInterest(), 
                interestTrialDto.getConsumeInterest(), 
                interestTrialDto.getCreditInterest());

            authRecordedDTO.setMinAvailableAmount(limitAvailable.getMinAvailableAmount());
            BigDecimal amount1 = interestTrialDto.getCashInterest().add(interestTrialDto.getConsumeInterest())
                    .subtract(interestTrialDto.getCreditInterest());
            authRecordedDTO.setAmount1(amount1);
            authRecordedDTO.setAmount3(limitAvailable.getMinAvailableAmount().add(amount1));
            logger.info("Card number {}, account closure transaction amount: amount1={}, amount2={}, amount3={}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()),
                    authRecordedDTO.getAmount1(),authRecordedDTO.getMinAvailableAmount(),authRecordedDTO.getAmount3());
        }
    }

    /**
     * 6号域以及51号域的赋值逻辑改造设计
     *  @param authRecordedDTO {@link AuthRecordedDTO}
     * @param organizationInfo 机构
     */
    private AuthResponseCodeEnum handleField6AndField51(AuthRecordedDTO authRecordedDTO, OrganizationInfoResDTO organizationInfo, String productNumber) {
        // 授权交易来源代码authAuthTransactionSourceCode
        String authAuthTransactionSourceCode = authRecordedDTO.getAuthTransactionSourceCode();
//        boolean cupAndBankFlag = AuthTransactionSourceCodeEnum.UNIONPAY.getCode().equals(authAuthTransactionSourceCode);
        // 6号域即授权接口 authCardholderBillingAmount
        BigDecimal authCardholderBillingAmount = authRecordedDTO.getAuthCardholderBillingAmount();
        // 51号域即授权接口中 authBillingCurrencyCode
        String authBillingCurrencyCode = authRecordedDTO.getAuthBillingCurrencyCode();
        // 发送机构标识码
//        String authForwardingIdentificationCode = authRecordedDTO.getAuthForwardingIdentificationCode();
        String organizationCurrency = organizationInfo.getOrganizationCurrency();
        // add by zcli
        if (StringUtils.isEmpty(authBillingCurrencyCode)) {
            authBillingCurrencyCode = organizationCurrency;
            authRecordedDTO.setAuthBillingCurrencyCode(authBillingCurrencyCode);
        }
        // 如果是银联
//        if (cupAndBankFlag) {
//            if (authForwardingIdentificationCode.length() < 8) {
//                log.info("发送机构标识码：{} 长度不能小于8", authForwardingIdentificationCode);
//                return AnyTxnAuthRespCodeEnum.IDENTIFICATIONCODE_ERROR;
//            }
//            // 查询类 && 银联 交易入账币种需赋值，要调用额度试算接口
//            if("Q".equals(authRecordedDTO.getAuthTransactionTypeTopCode()) && cupAndBankFlag){
//                authRecordedDTO.setAuthBillingCurrencyCode(organizationCurrency);
//                authRecordedDTO.setAuthTransactionCurrencyCode(organizationCurrency);
//                return AuthResponseCodeEnum.APPROVED_BY_ISSUER;
//            }
//            if (authCardholderBillingAmount != null && !BigDecimal.ZERO.equals(authCardholderBillingAmount)
//                    && StringUtils.isBlank(authBillingCurrencyCode)) {
//                log.info("6号域即授权接口 authCardholderBillingAmount：{} 不为空，  则51号域即授权接口中 authBillingCurrencyCode:{} 不能为空",
//                        authCardholderBillingAmount, authBillingCurrencyCode);
//                return AnyTxnAuthRespCodeEnum.FILED6_FILED_51_EXIT_VALUE;
//            }
//            //true 代表境外
//            boolean innerFlag = AuthConstans.AUTHFORWARDINGIDENTIFICATIONCODE.equals(authForwardingIdentificationCode.substring(4, 8));
//            // 境外
//            if (innerFlag) {
//                //如果6  51 有一个为空则
//                if (StringUtils.isBlank(authRecordedDTO.getAuthBillingCurrencyCode()) ||
//                        (authCardholderBillingAmount == null || BigDecimal.ZERO.equals(authCardholderBillingAmount))){
//                    if (!Objects.equals(authRecordedDTO.getAuthTransactionCurrencyCode(),organizationCurrency)){
//                        return AnyTxnAuthRespCodeEnum.UNIONPAY_OUTLANDS_CURRENCY_ERROR;
//                    }else {
//                        // 4号域(交易金额) 赋值给 6号域(持卡人扣账金额)
//                        authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
//                        // 49号域(交易币种) 赋值给 51号域(扣账币种)
//                        authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
//                    }
//                }else {
//                    //如果银联境外, 6号域和51号域没有值 则取 4号域 49号域
//                    if (authRecordedDTO.getAuthBillingCurrencyCode() == null){
//                        // 4号域(交易金额) 赋值给 6号域(持卡人扣账金额)
//                        authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
//                        // 49号域(交易币种) 赋值给 51号域(扣账币种)
//                        authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
//                    }
//                    if (authRecordedDTO.getAuthBillingCurrencyCode() == null ||
//                            !authRecordedDTO.getAuthBillingCurrencyCode().equals(organizationCurrency)) {
//                        log.info("银联境外渠道,扣账货币代码:{} 不等于机构币种:{}", authRecordedDTO.getAuthBillingCurrencyCode(),
//                                organizationCurrency);
//                        return AnyTxnAuthRespCodeEnum.UNIONPAY_OUTLANDS_CURRENCY_ERROR;
//                    }
//                }
//            }else {
//                if (!authRecordedDTO.getAuthTransactionCurrencyCode().equals(organizationCurrency)) {
//                    log.info("银联境内渠道，51号域:{} 不等于机构主币种:{}", authRecordedDTO.getAuthTransactionCurrencyCode(),
//                            organizationCurrency);
//                    return AnyTxnAuthRespCodeEnum.UNIONPAY_INNER_CURRENCY_ERROR;
//                }
//                authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
//                authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
//            }
//        }
        if (Objects.equals(AuthTransactionSourceCodeEnum.THE_BANK.getCode(), authAuthTransactionSourceCode)
                || Objects.equals(AuthTransactionSourceCodeEnum.EXPRESS.getCode(),authAuthTransactionSourceCode)
                || Objects.equals(AuthTransactionSourceCodeEnum.ECPP.getCode(), authAuthTransactionSourceCode)) {
            // 4号域(交易金额) 赋值给 6号域(持卡人扣账金额)
            authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
            // 49号域(交易币种) 赋值给 51号域(扣账币种)
            authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
        }
        //
        if (AuthTransactionSourceCodeEnum.DCI.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.DCS.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.DCS_FEP.getCode().equals(authAuthTransactionSourceCode)
                ||AuthTransactionSourceCodeEnum.VISA.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.AMEX.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.JCB.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.MASTERCARD.getCode().equals(authAuthTransactionSourceCode)
                || AuthTransactionSourceCodeEnum.UPI.getCode().equals(authAuthTransactionSourceCode)) {
            // 查询类 &&  交易入账币种需赋值，要调用额度试算接口
            if(AuthConstans.BALANCE_INQUIRE_TOP_CODE.equals(authRecordedDTO.getAuthTransactionTypeTopCode())){
                authRecordedDTO.setAuthBillingCurrencyCode(organizationCurrency);
                if (StringUtils.isBlank(authRecordedDTO.getAuthTransactionCurrencyCode())) {
                    authRecordedDTO.setAuthTransactionCurrencyCode(organizationCurrency);
                }
                return AuthResponseCodeEnum.CHECK_RESPONSE;
            }
            List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationInfo.getOrganizationNumber(), productNumber);
            // 授权报文中的6号域有值
            if (authCardholderBillingAmount != null && !BigDecimal.ZERO.equals(authCardholderBillingAmount)) {
//                List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectAll(true,organizationInfo.getOrganizationNumber());
                boolean exist = whetherExistIn(cardCurrencyInfoList, authBillingCurrencyCode);
                if (!exist) {
                    logger.info("Posting currency not in organization posting currency table");
                    return AuthResponseCodeEnum.EXCEPTION;
                }
            } else {
                // 授权报文中的6号域为空，并且49号域交易货币代码（authTransactionCurrencyCode）不等于机构主币种
//                List<ParmCardCurrencyInfo> cardCurrencyInfoList = parmCardCurrencyInfoSelfMapper.selectAll(true, organizationInfo.getOrganizationNumber());
                if (!organizationCurrency.equals(authRecordedDTO.getAuthTransactionCurrencyCode())) {
                    boolean exist = whetherExistIn(cardCurrencyInfoList, authRecordedDTO.getAuthTransactionCurrencyCode());
                    if (exist) {
                        authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
                        authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
                    } else {
                        //交易币种
                        String curkyCode = authRecordedDTO.getAuthTransactionCurrencyCode();
                        ParmCurrencyRate parmCurrencyRate = parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(OrgNumberUtils.getOrg(), curkyCode, organizationCurrency, "0");
                        if (parmCurrencyRate == null) {
                            logger.info("Transaction currency to settlement currency exchange rate not configured in parm_currency_rate table");
                            return AuthResponseCodeEnum.EXCEPTION;
                        }
                        BigDecimal exchangeRate = new BigDecimal(parmCurrencyRate.getRateValue()).divide(BigDecimal.valueOf(Math.pow(10, parmCurrencyRate.getExponent())), 8, RoundingMode.HALF_UP);
                        BigDecimal multiplyBillingAmount = authRecordedDTO.getAuthTransactionAmount().multiply(exchangeRate);
                        authRecordedDTO.setAuthCardholderBillingAmount(multiplyBillingAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        authRecordedDTO.setAuthBillingCurrencyCode(organizationCurrency);
                    }
                } else {
                    // 授权报文中的6号域为空 并且授权报文中的49号域交易货币代码（authTransactionCurrencyCode）等于机构主币种
//                    log.info("卡片币种对照表记录：{}", JSON.toJSONString(cardCurrencyInfoList));
                    boolean exist = whetherExistIn(cardCurrencyInfoList, organizationCurrency);
                    if (exist) {
                        authRecordedDTO.setAuthCardholderBillingAmount(authRecordedDTO.getAuthTransactionAmount());
                        authRecordedDTO.setAuthBillingCurrencyCode(authRecordedDTO.getAuthTransactionCurrencyCode());
                    } else {
                        logger.info("Card currency mapping table posting currency records do not contain organization main currency: {}", organizationCurrency);
                        return AuthResponseCodeEnum.EXCEPTION;
                    }
                }
            }
        }
        logger.warn("Card number {}, converted posting amount: {}, posting currency: {}",DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()),
                authRecordedDTO.getAuthCardholderBillingAmount(),authRecordedDTO.getAuthBillingCurrencyCode());
        return AuthResponseCodeEnum.CHECK_RESPONSE;
    }

    /**
     * List<ParmCardCurrencyInfo> 项的币种代码 是否存在指定值为 authBillingCurrencyCode
     * @param cardCurrencyInfoList  {@link ParmCardCurrencyInfo}
     * @param authBillingCurrencyCode 入账币种
     * @return 是否存在
     */
    private boolean whetherExistIn(List<ParmCardCurrencyInfo> cardCurrencyInfoList, String authBillingCurrencyCode) {
        for (ParmCardCurrencyInfo cardCurrencyInfo : cardCurrencyInfoList) {
            if (authBillingCurrencyCode.equals(cardCurrencyInfo.getCurrencyCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取并设置 账产品组控制信息
     * @param cardAuthorizationDTO
     *            {CardAuthorizationDTO}
     * @param authRecordedDTO
     *            {AuthRecordedDTO}
     * @param authorizationCheckProcessingPayload
     *            {@link AuthorizationCheckProcessingPayload}
     */
    private AuthResponseCodeEnum getAndSetAccountGroupControlInfo(CardAuthorizationDTO cardAuthorizationDTO,
                                                                  AuthRecordedDTO authRecordedDTO, AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        Map<String, Object> authVelocityCheckMap = new HashMap<>(9);
        authVelocityCheckMap.put("authTransactionTypeTopCode", authRecordedDTO.getAuthTransactionTypeTopCode());
        authVelocityCheckMap.put("authTransactionTypeDetailCode", authRecordedDTO.getAuthTransactionTypeDetailCode());
        authVelocityCheckMap.put("cardProductNumber", cardAuthorizationDTO.getProductNumber());
        DataInputDTO dataInputDTO = new DataInputDTO();
        dataInputDTO.setRuleType(AuthConstans.ACCOUNT_GROUP_AUTH_CONTROLLER_RULE);
        dataInputDTO.setInput(authVelocityCheckMap);
        dataInputDTO.setOrganizationNumber(cardAuthorizationDTO.getOrganizationNumber());
        logger.info("ruleParm: authTransactionTypeTopCode={}, authTransactionTypeDetailCode={}, cardProductNumber={}", 
            authVelocityCheckMap.get("authTransactionTypeTopCode"), 
            authVelocityCheckMap.get("authTransactionTypeDetailCode"), 
            authVelocityCheckMap.get("cardProductNumber"));
        logger.info("Calling ruleService.executeRule: ruleType={}, orgNumber={}", 
            dataInputDTO.getRuleType(), dataInputDTO.getOrganizationNumber());
        Map<String, String> ruleResultMap = ruleService.executeRule(dataInputDTO);
        logger.info("ruleService.executeRule completed: ruleType={}, accountProductGroupId={}", 
            dataInputDTO.getRuleType(), ruleResultMap != null ? ruleResultMap.get("accountProductGroupId") : "null");
        String accountProductGroupId = ruleResultMap == null ? null : ruleResultMap.get("accountProductGroupId");
        if (accountProductGroupId == null) {
            return AuthResponseCodeEnum.EXCEPTION;
        }
        logger.info("Calling accountGroupAuthControlService.getEffectiveAccountGroupAuthControl: orgNumber={}, accountProductGroupId={}", 
            cardAuthorizationDTO.getOrganizationNumber(), accountProductGroupId);
        List<AccountGroupAuthControlDTO> accountGroupAuthControlDTOList = accountGroupAuthControlService
                .getEffectiveAccountGroupAuthControl(cardAuthorizationDTO.getOrganizationNumber(), accountProductGroupId);
        logger.info("accountGroupAuthControlService.getEffectiveAccountGroupAuthControl completed: orgNumber={}, resultSize={}", 
            cardAuthorizationDTO.getOrganizationNumber(), 
            accountGroupAuthControlDTOList != null ? accountGroupAuthControlDTOList.size() : 0);
        if (CollectionUtils.isNotEmpty(accountGroupAuthControlDTOList)) {
            authRecordedDTO.setAccountGroupAuthControlList(
                    transformAccountGroupControlInfo(accountGroupAuthControlDTOList, cardAuthorizationDTO,
                            authRecordedDTO.getAuthBillingCurrencyCode(), authorizationCheckProcessingPayload));
            return AuthResponseCodeEnum.CHECK_RESPONSE;
        }
        return AuthResponseCodeEnum.EXCEPTION;
    }

    /**
     * 将账产品组控制信息 list 转换成 授权接口中 账产品组控制信息
     * @param accountGroupAuthControlDTOList {@link AccountGroupAuthControlDTO}
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @return {@link AccountGroupAuthControlDTO}
     */
    private List<AuthRecordedDTO.AccountGroupAuthControlDTO> transformAccountGroupControlInfo(
            List<AccountGroupAuthControlDTO> accountGroupAuthControlDTOList, CardAuthorizationDTO cardAuthorizationDTO,
            String currency, AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        List<AuthRecordedDTO.AccountGroupAuthControlDTO> resultList = new ArrayList<>(16);

        if(CollectionUtils.isEmpty(accountGroupAuthControlDTOList)){
            return resultList;
        }

        List<AccountManagementInfoDTO> accountManagementInfoDTOS;
        CustAccountBO<?> custAccountBO = CustAccountBO.threadCustAccountBO.get();

        if (custAccountBO != null && CollectionUtils.isNotEmpty(custAccountBO.getCustomerBO().getManagementInfoList())){
            accountManagementInfoDTOS = custAccountBO.getCustomerBO().getManagementInfoList();
        }else {
            String customerId;
            if(AuthConstans.CORPORATE_INDICATOR_C.equals(cardAuthorizationDTO.getLiability())){
                customerId = cardAuthorizationDTO.getCorporateCustomerId();
            }else {
                customerId = cardAuthorizationDTO.getPrimaryCustomerId();
            }
            CommonAccountDTO commonAccountDTO = CommonAccountDTO.CommonAccountDTOBuilder
                    .aCommonAccountDTO()
                    .withOrganizationNumber(cardAuthorizationDTO.getOrganizationNumber())
                    .withCustomerId(customerId)
                    .build();
            logger.info("Calling commonAccountService.selectByCustomerIdAndOrg: customerId={}, orgNumber={}", 
                customerId, cardAuthorizationDTO.getOrganizationNumber());
            List<AccountManagementInfo> accountManagementInfos = commonAccountService.selectByCustomerIdAndOrg(commonAccountDTO);
            logger.info("commonAccountService.selectByCustomerIdAndOrg completed: customerId={}, resultSize={}", 
                customerId, accountManagementInfos != null ? accountManagementInfos.size() : 0);
            accountManagementInfoDTOS = BeanMapping.copyList(accountManagementInfos, AccountManagementInfoDTO.class);

        }

        authorizationCheckProcessingPayload.setAccountManagementInfoDTOS(accountManagementInfoDTOS);
        for (AccountGroupAuthControlDTO groupAuthControlDTO : accountGroupAuthControlDTOList) {
            AuthRecordedDTO.AccountGroupAuthControlDTO item = new AuthRecordedDTO.AccountGroupAuthControlDTO();
            item.setAcctBlockCodeCheckInd(groupAuthControlDTO.getAcctBlockCodeCheckInd());
            item.setAcctCycleDueCheckInd(groupAuthControlDTO.getAcctCycleDueCheckInd());
            item.setAcctProductCode(groupAuthControlDTO.getAcctProductCode());
            item.setAcctStatusCheckInd(groupAuthControlDTO.getAcctStatusCheckInd());
            item.setPrimarySupplymentaryInd(groupAuthControlDTO.getPrimarySupplymentaryInd());
            AccountManagementInfoDTO accountManagementInfoDto = accountManagementInfoDTOS.stream().filter(t ->
                    Objects.equals(t.getOrganizationNumber(),cardAuthorizationDTO.getOrganizationNumber())
                            && Objects.equals(groupAuthControlDTO.getAcctProductCode(),t.getProductNumber())
                            && Objects.equals(currency, t.getCurrency())).findFirst().orElse(null);
            if (accountManagementInfoDto != null) {
                item.setAccountManagementId(accountManagementInfoDto.getAccountManagementId());
                authorizationCheckProcessingPayload.getAccountManagementInfoMap().put(item.getAccountManagementId(),
                        accountManagementInfoDto);
            }
            resultList.add(item);
        }
        logger.info("Account product group control info: size={}", resultList.size());
        return resultList;
    }

    /**
     * 卡片相关信息
     * @param authRecordedDTO
     *            {@link AuthRecordedDTO}
     * @return CardAuthorizationDTO
     */
    private CardAuthorizationDTO cardRelative(AuthRecordedDTO authRecordedDTO,
                                              AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        CustAccountBO<?> custAccountBO = CustAccountBO.threadCustAccountBO.get();
        CardAuthorizationDTO authorizationDTO;
        if (custAccountBO != null && custAccountBO.getAuthBO().getCardAuthorizationByCarNum(authRecordedDTO.getAuthCardNumber()) != null){
            authorizationDTO = custAccountBO.getAuthBO().getCardAuthorizationByCarNum(authRecordedDTO.getAuthCardNumber());
        }else {
            CardAuthorizationInfo info = cardAuthorizationInfoMapper.selectByPrimaryKey(authRecordedDTO.getAuthCardNumber(),authRecordedDTO.getOrganizationNumber());
            if (info == null) {
                logger.info("Card authorization info table does not exist! cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
                return null;
            }
            authorizationDTO = BeanMapping.copy(info, CardAuthorizationDTO.class);
        }
        authorizationCheckProcessingPayload.setCardAuthorizationDTO(authorizationDTO);
        if (authRecordedDTO.getAuthCardExpirationDate() == null){
            authRecordedDTO.setAuthCardExpirationDate(authorizationDTO.getExpireDate());
        }
        // 读取卡产品信息
        logger.info("Calling cardProductInfoService.findByOrgAndProductNum: orgNumber={}, productNumber={}", 
            authorizationDTO.getOrganizationNumber(), authorizationDTO.getProductNumber());
        CardProductInfoResDTO cardProductInfo = cardProductInfoService
                .findByOrgAndProductNum(authorizationDTO.getOrganizationNumber(), authorizationDTO.getProductNumber());
        logger.info("cardProductInfoService.findByOrgAndProductNum completed: orgNumber={}, productNumber={}, cardClass={}", 
            authorizationDTO.getOrganizationNumber(), 
            authorizationDTO.getProductNumber(), 
            cardProductInfo != null ? cardProductInfo.getCardClass() : "null");
        if (cardProductInfo == null) {
            logger.info("Card product info does not exist!");
            return null;
        }
        authRecordedDTO.setCardClass(CardClassEnum.getCupCardClassCode(cardProductInfo.getCardClass()));
        // 卡授权参数表
        authorizationCheckProcessingPayload.setCardProductInfo(cardProductInfo);
        logger.info("Calling authorizationRuleService.findAuthorizationByTableId: tableId={}, orgNumber={}", 
            cardProductInfo.getAuthCtlTableId(), authorizationDTO.getOrganizationNumber());
        AuthorizationRuleDTO authorizationRuleDTO =
                authorizationRuleService.findAuthorizationByTableId(cardProductInfo.getAuthCtlTableId(), authorizationDTO.getOrganizationNumber());
        logger.info("authorizationRuleService.findAuthorizationByTableId completed: tableId={}, orgNumber={}, result={}", 
            cardProductInfo.getAuthCtlTableId(), 
            authorizationDTO.getOrganizationNumber(), 
            authorizationRuleDTO != null ? "found" : "not found");
        if (authorizationRuleDTO == null) {
            logger.error("Card authorization parameter table does not exist! cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            return null;
        }
        authorizationCheckProcessingPayload.setAuthorizationRuleDTO(authorizationRuleDTO);
        return authorizationDTO;
    }

    /**
     * 账户相关信息 改dto表示 账产品组中检查标识为主检查的账户信息 1。如果主账产品的主客户id不为空 则赋值为主账户实体 2. 如果主账产品的主客户id为空 则赋值一个空对象防止后续引用accountId 报错
     */
    private void accountRelative(AuthRecordedDTO authRecordedDTO,
                                 AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO.AccountGroupAuthControlDTO primaryAccountGroupAuthControl =
                authRecordedDTO.getPrimaryAccountGroupAuthControlDTO();
        if (primaryAccountGroupAuthControl == null) {
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(new AccountManagementInfoDTO());
            return;
        }
        if (StringUtils.isNotBlank(primaryAccountGroupAuthControl.getAccountManagementId())) {
            AccountManagementInfoDTO managementInfoDto = authorizationCheckProcessingPayload
                    .getAccountManagementInfoMap().get(primaryAccountGroupAuthControl.getAccountManagementId());
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(managementInfoDto);
        } else {
            authorizationCheckProcessingPayload.setAccountManagementInfoDTO(new AccountManagementInfoDTO());
        }
    }

    /**
     * 产品相关信息
     */
    private void productRelative(AuthRecordedDTO authRecordedDTO,
                                 AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        /*
          1. 不再单独获取账产品编号 authRecordedDTO中的账产品dto移除
          2. 授权处理参数实体 归户后处理——通过卡产品编号再获取授权处理参数实体
         */
        String authOrganizationNumber = authorizationCheckProcessingPayload.getOrgInfo().getOrganizationNumber();
        // 读取授权处理参数表
        logger.info("Calling authorisationProcessingService.findByOrgAndTableId: orgNumber={}, tableId={}", 
            authOrganizationNumber, authorizationCheckProcessingPayload.getCardProductInfo().getAuthorisationProcessingId());
        AuthorisationProcessingResDTO authorProcessInfo = authorisationProcessingService
                .findByOrgAndTableId(authOrganizationNumber, authorizationCheckProcessingPayload.getCardProductInfo().getAuthorisationProcessingId());
        logger.info("authorisationProcessingService.findByOrgAndTableId completed: orgNumber={}, tableId={}, result={}", 
            authOrganizationNumber, 
            authorizationCheckProcessingPayload.getCardProductInfo().getAuthorisationProcessingId(), 
            authorProcessInfo != null ? "found" : "not found");
        if (authorProcessInfo == null) {
            logger.error("Authorisation processing info not found: orgNumber={}, tableId={}", 
                authOrganizationNumber, authorizationCheckProcessingPayload.getCardProductInfo().getAuthorisationProcessingId());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_AUTHORISATION_PROCESS_FAIL);
        }
        authorizationCheckProcessingPayload.setAuthorProcessInfo(authorProcessInfo);
    }

    /**
     * 客户相关信息
     */
    private void customerRelative(CardAuthorizationDTO cardAuthorizationDTO, AuthRecordedDTO authRecordedDTO,
                                  AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        /*CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = null;
        if (PrincipalSupplementaryIndEnum.PRIMARY_CARD.getCode()
            .equals(cardAuthorizationDTO.getRelationshipIndicator())) {
            customerId = cardAuthorizationDTO.getPrimaryCustomerId();
            customerAuthorizationInfoDTO =sharedInfoFindService.getCustomerAuthorizationByCustomerId(customerId);
        } else if (PrincipalSupplementaryIndEnum.SUBSIDIARY_CARD.getCode()
            .equals(cardAuthorizationDTO.getRelationshipIndicator())) {
            customerId = cardAuthorizationDTO.getSupplementaryCustomerId();
            customerAuthorizationInfoDTO =customerAuthFeignService.getCustomerAuthorizationByCustomerId(cardAuthorizationDTO.getOrganizationNumber(),customerId);
        } else {
            logger.error("cardAuthorizationDTO relationshipIndicator not in (p,s) illegal");
        }*/
        String customerId = cardAuthorizationDTO.getPrimaryCustomerId();
        logger.info("Calling sharedInfoFindService.getCustomerAuthorizationByCustomerId: customerId={}", customerId);
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO =sharedInfoFindService.getCustomerAuthorizationByCustomerId(customerId);
        logger.info("sharedInfoFindService.getCustomerAuthorizationByCustomerId completed: customerId={}, result={}", 
            customerId, customerAuthorizationInfoDTO != null ? "found" : "not found");
        if (StringUtils.isBlank(customerId)) {
            logger.error("Customer ID is blank");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.NULL,customerId);
        }
        authRecordedDTO.setAuthCustomerId(customerId);
        if (customerAuthorizationInfoDTO == null) {
            logger.error("Customer authorization info not found: customerId={}", customerId);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_CUSTOMER_AUTHORIZATION_FAIL, AuthRepDetailEnum.NULL,customerId);
        }
        authorizationCheckProcessingPayload.setCustomerAuthorizationInfoDTO(customerAuthorizationInfoDTO);
        /*
         * 判断卡片授权信息表中的公司卡标志（CORP_INDICATOR），如果为P, 表示此卡为个人卡，保持原获取个人客户信息的逻辑； 如果为C, 并且清偿责任（LIABILITY_INDICATOR）为C,
         * 表示此卡为公司清偿的公司卡，除了获取个人客户信息之外，还需要获取公司客户信息：
         * 通过卡片授权信息表上的公司客户号（CORP_CUSTOMER_ID）读取公司客户信息表（Corporate_Customer_Info），获取公司注册信息ID;
         * 然后用公司注册信息ID(corporate_registration_id)读取公司注册信息表（Corporate_Registration_Info）.
         */
        logger.debug("Data preparation - add corporate customer info logic when getting personal customer info");
        if (cardAuthorizationDTO.isCorporateLiability()) {
            String corporateCustomerId = cardAuthorizationDTO.getCorporateCustomerId();
            if (StringUtils.isBlank(corporateCustomerId)) {
                logger.error("Corporate customer ID is blank");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.NULL,corporateCustomerId);
            }
            CorporateCustomerInfo corporateCustomerInfo =
                    corporateCustomerInfoMapper.selectByPrimaryKey(corporateCustomerId,cardAuthorizationDTO.getOrganizationNumber());
            if (corporateCustomerInfo == null) {
                logger.error("Corporate customer info not found: corporateCustomerId={}", corporateCustomerId);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_AUTHORISATION_PROCESS_FAIL);
            }
            CorporateCustomerInfoDTO corporateCustomerInfoDTO =
                    BeanMapping.copy(corporateCustomerInfo, CorporateCustomerInfoDTO.class);
            authorizationCheckProcessingPayload.setCorporateCustomerInfoDTO(corporateCustomerInfoDTO);
            // 公司注册id
            String corporateRegistrationId = corporateCustomerInfo.getCorporateRegistrationId();
            if (StringUtils.isBlank(corporateRegistrationId)) {
                logger.error("Corporate registration ID is blank: corporateCustomerId={}", corporateCustomerId);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.NULL,corporateRegistrationId);
            }
            CorporateRegistrationInfo corporateRegistrationInfo =
                    corporateRegistrationInfoMapper.selectByPrimaryKey(corporateRegistrationId);
            if (corporateRegistrationInfo == null) {
                logger.error("Corporate registration info not found: corporateRegistrationId={}", corporateRegistrationId);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_AUTHORISATION_PROCESS_FAIL);
            }
            CorporateRegistrationInfoDTO corporateRegistrationInfoDTO =
                    BeanMapping.copy(corporateRegistrationInfo, CorporateRegistrationInfoDTO.class);
            authorizationCheckProcessingPayload.setCorporateRegistrationInfoDTO(corporateRegistrationInfoDTO);
            /*
             * 继续获取所有层级的公司客户信息
             */
            List<CorporateDownTopReference> corporateDownTopReferenceList =
                    corporateDownTopReferenceSelfMapper.selectByParentIdOrChildId(corporateCustomerId);
            if (corporateDownTopReferenceList != null && !corporateDownTopReferenceList.isEmpty()) {
                logger.info("Get corporate top-down reference table by corporate customer ID: {}, resultSize: {}",corporateCustomerId,corporateDownTopReferenceList.size());
                List<CorporateCustomerInfo> parentCorporateCustomerInfoList =
                        corporateCustomerInfoSelfMapper.selectByCorpCusIdList(corporateDownTopReferenceList.stream()
                                .map(CorporateDownTopReference::getCorporateParentId).collect(Collectors.toList()));
                if (parentCorporateCustomerInfoList != null && !parentCorporateCustomerInfoList.isEmpty()) {
                    logger.info("Got all parent corporate customer info: size={}",parentCorporateCustomerInfoList.size());
                    List<CorporateCustomerInfoDTO> corporateCustomerInfoDTOList =
                            BeanMapping.copyList(parentCorporateCustomerInfoList, CorporateCustomerInfoDTO.class);
                    authorizationCheckProcessingPayload
                            .setParentCorporateCustomerInfoDTOList(corporateCustomerInfoDTOList);
                }
            }
        }
    }
}


