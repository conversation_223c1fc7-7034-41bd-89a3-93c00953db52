package com.anytech.anytxn.authorization.service.channel.cup.preauth;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.bo.PreAuthPayload;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.mapper.preauthlog.PreAuthorizationLogMapper;
import com.anytech.anytxn.authorization.mapper.preauthlog.PreAuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.PreAuthorizationLog;
import com.anytech.anytxn.authorization.base.domain.model.PreAuthorizationLogSimple;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.base.limit.domain.dto.PartnerMarginLogDTO;
import com.anytech.anytxn.business.base.limit.enums.PartnerMarginTransTypeEnum;
import com.anytech.anytxn.business.base.limit.enums.PartnerModeEnum;
import com.anytech.anytxn.business.dao.limit.mapper.PartnerInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.PartnerInfo;
import com.anytech.anytxn.limit.base.constant.LimitConstant;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmOrganizationInfoSelfMapper;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmOrganizationInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.authorization.base.domain.dto.PreAuthLogSearchKeyReqDTO;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.base.authorization.enums.DebitCreditIndcatorEnum;
import com.anytech.anytxn.business.base.authorization.enums.PreAuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogSimpleDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitUnitReqDTO;
import com.anytech.anytxn.limit.base.domain.dto.payload.LimitReqDTO;
import com.anytech.anytxn.limit.base.enums.RequestSourceEnum;
import com.anytech.anytxn.limit.base.enums.RequestTypeEnum;
import com.anytech.anytxn.limit.service.CustomerLimitUpdateService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 授权流水
 *
 * <AUTHOR>
 * @date 2018-12-12
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class PreAuthorizationLogServiceImpl implements IPreAuthorizationLogService {

    private static final Logger logger = LoggerFactory.getLogger(PreAuthorizationLogServiceImpl.class);

    @Autowired
    private PreAuthorizationLogSelfMapper preAuthorizationLogSelfMapper;
    @Autowired
    private PreAuthorizationLogMapper preAuthorizationLogMapper;
    @Autowired
    private CustomerLimitUpdateService customerLimitUpdateService;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Resource
    private PartitionKeyInitService partitionKeyInitService;

    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Autowired
    private PartnerInfoMapper partnerInfoMapper;

    @Autowired
    private ParmOrganizationInfoSelfMapper parmOrganizationInfoSelfMapper;

    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     * 根据搜索词分页搜索预授权流水
     *
     * @param page                当前页
     * @param rows                每页展示条数
     * @param authLogSearchKeyReq 搜索词
     * @return PageResultDTO<AuthorizationLogDTO>
     */
    @Override
    public PageResultDTO<PreAuthorizationLogSimpleDTO> searchPageByKey(Integer page, Integer rows, PreAuthLogSearchKeyReqDTO authLogSearchKeyReq) {
        logger.info("Paginated search for authorization logs, current page: {}, rows per page: {}", page, rows);
        //搜索条件
        Map<String, String> keyMap = new HashMap<>(14);
        if (authLogSearchKeyReq != null) {
            keyMap.put("authCode", authLogSearchKeyReq.getAuthCode());
            keyMap.put("cardNumber", authLogSearchKeyReq.getCardNumber());
            keyMap.put(AuthConstans.ORGANIZATION_NUMBER, OrgNumberUtils.getOrg());
        }
        try {
            Page<PreAuthorizationLogSimpleDTO> pageHelper = PageHelper.startPage(page, rows);
            List<PreAuthorizationLogSimple> preAuthorizationLogSimples = preAuthorizationLogSelfMapper.searchBySearchKey(keyMap);
            return new PageResultDTO<>(page, rows, pageHelper.getTotal(), pageHelper.getPages(), BeanMapping.copyList(preAuthorizationLogSimples, PreAuthorizationLogSimpleDTO.class));
        } catch (Exception e) {
            logger.error("Paginated search for authorization logs failed, current page: {}, rows per page: {}", page, rows, e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);
        }
    }

    /**
     * 根据authid获取记录
     *
     * @param preauthLogId 授权流水id
     * @return {@link PreAuthorizationLogDTO}
     */
    @Override
    public PreAuthorizationLogDTO getByPreAuthLogId(String preauthLogId) {
        PreAuthorizationLog preAuthorizationLog = preAuthorizationLogSelfMapper.selectByAuthLogId(preauthLogId);
        PreAuthorizationLogDTO preAuthorizationLogDTO;
        if (preAuthorizationLog != null) {
            preAuthorizationLogDTO = BeanMapping.copy(preAuthorizationLog, PreAuthorizationLogDTO.class);
            return preAuthorizationLogDTO;
        }

        return null;
    }

    /**
     * select AuthorizationLog by globalFlowNumber
     *
     * @param globalFlowNumber 全局流水号
     * @return AuthorizationLogDTO
     */
    @Override
    public PreAuthorizationLogDTO getByGlobalFlowNumber(String globalFlowNumber) {
        if (StringUtils.isEmpty(globalFlowNumber)) {
            logger.error("globalFlowNumber is Null!");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.NULL, "globalFlowNumber is Null!");
        }
        try {
            PreAuthorizationLog preAuthorizationLog = preAuthorizationLogSelfMapper.selectByGlobalFlowNumber(globalFlowNumber);
            if (preAuthorizationLog != null) {
                PreAuthorizationLogDTO preAuthorizationLogDTO = new PreAuthorizationLogDTO();
                BeanCopier copierAccountBalanceModelToDTO = BeanCopier.create(PreAuthorizationLog.class, PreAuthorizationLogDTO.class, false);
                copierAccountBalanceModelToDTO.copy(preAuthorizationLog, preAuthorizationLogDTO, null);
                return preAuthorizationLogDTO;
            }
            return null;
        } catch (Exception e) {
            logger.error("Failed to query authorization log by global flow number, global flow number: {}", globalFlowNumber, e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);
        }
    }

    /**
     * authorizationLog update
     *
     * @param preAuthorizationLogDTO {@link PreAuthorizationLogDTO}
     * @return int
     */
    @Override
    public int updatePreAuthorizationLogByPrimaryId(PreAuthorizationLogDTO preAuthorizationLogDTO) {
        if (preAuthorizationLogDTO == null) {
            logger.error("preAuthorizationLogDTO is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL);
        }
        PreAuthorizationLog preAuthorizationLog = BeanMapping.copy(preAuthorizationLogDTO, PreAuthorizationLog.class);
        try {
            int res = preAuthorizationLogMapper.updateByPrimaryKeySelective(preAuthorizationLog);
            if (res != AuthConstans.ONE) {
                logger.error("preAuthorizationLog update error");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.NULL, "preAuthorizationLog update error");
            }
            return res;
        } catch (Exception e) {
            logger.error("Database operation exception", e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);

        }
    }

    @Override
    public int insert(PreAuthorizationLogDTO preAuthorizationLogDTO, AccountManagementInfoDTO accountManagementInfoDTO) {
        if (preAuthorizationLogDTO == null) {
            logger.error("preAuthorizationLogDTO is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL);
        }

        PreAuthorizationLog preAuthorizationLog = BeanMapping.copy(preAuthorizationLogDTO, PreAuthorizationLog.class);
        try {
            int keyGenerator = partitionKeyInitService.partitionKeyGenerator(null, BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class));
            preAuthorizationLog.setPartitionKey((long) keyGenerator);

            int res = preAuthorizationLogMapper.insertSelective(preAuthorizationLog);
            if (res != AuthConstans.ONE) {
                logger.error("preAuthorizationLog insert error");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.NULL, "preAuthorizationLog insert error");
            }
            return res;
        } catch (Exception e) {
            logger.error("Database operation exception", e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);

        }
    }

    /**
     * 预授权匹配
     */
    @Override
    public PreAuthorizationLogDTO getByCardNumAndAuthCodeAndTransType(String cardNum, String authCode, String authTransType) {
        boolean preFlag = StringUtils.isNotBlank(cardNum) && StringUtils.isNotBlank(authCode) && StringUtils.isNotBlank(authTransType);
        if (preFlag) {
            PreAuthorizationLog preAuthorizationLog = preAuthorizationLogSelfMapper.selectByCardNumberAndAuthCodeAndTransType(cardNum, authCode, authTransType);
            if (preAuthorizationLog != null) {
                return BeanMapping.copy(preAuthorizationLog, PreAuthorizationLogDTO.class);
            }
        }
        return null;
    }

    @Override
    public int getCount(String partitionKey, List<Map<String, Object>> orgs) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return preAuthorizationLogSelfMapper.getCount(partitionKey0, partitionKey1, orgs);
    }

    @Override
    public List<String> queryPreAuthLogIds(String partitionKey, List<Map<String, Object>> orgs, ArrayList<Integer> rowNumbers) {
        String partitionKey0 = null;
        String partitionKey1 = null;
        if (!StringUtils.isBlank(partitionKey)) {
            partitionKey0 = partitionKey.split("-")[0];
            partitionKey1 = partitionKey.split("-")[1];
        }
        return preAuthorizationLogSelfMapper.selectPreAuthLogIds(partitionKey0, partitionKey1, orgs, rowNumbers);
    }

    /**
     * 获取预授权交易日期大于30天的
     */
    @Override
    public List<PreAuthorizationLogDTO> getLogsWithDayOver30(String organizationNumber) {
        List<PreAuthorizationLog> preAuthorizationLogList = preAuthorizationLogSelfMapper.selectByOverDue30(organizationNumber);
        List<PreAuthorizationLogDTO> preAuthorizationLogDTOList;
        if (!CollectionUtils.isEmpty(preAuthorizationLogList)) {
            preAuthorizationLogDTOList = BeanMapping.copyList(preAuthorizationLogList, PreAuthorizationLogDTO.class);
            return preAuthorizationLogDTOList;
        }
        return null;
    }

    @Override
    public PreAuthPayload batchPorcess(PreAuthorizationLogDTO preAuthorizationLogDTO) {
        LimitReqDTO limitReqDTO = new LimitReqDTO();
        limitReqDTO.setOrganizationNumber(preAuthorizationLogDTO.getOrganizationNumber());
        limitReqDTO.setCustomerId(preAuthorizationLogDTO.getCustomerId());
        limitReqDTO.setGlobalFlowNumber(preAuthorizationLogDTO.getGlobalFlowNumber());
        limitReqDTO.setAccountManagementId(preAuthorizationLogDTO.getAccountManagementId());
        limitReqDTO.setCardNumber(preAuthorizationLogDTO.getCardNumber());
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectVaildByMid(preAuthorizationLogDTO.getAccountManagementId());
        CardAuthorizationInfo cardAuthorizationDTO = cardAuthorizationInfoMapper.selectByPrimaryKey(preAuthorizationLogDTO.getCardNumber(), preAuthorizationLogDTO.getOrganizationNumber());

        limitReqDTO.setAccountProductCode(accountManagementInfo.getProductNumber());


        if (StringUtils.isNotBlank(preAuthorizationLogDTO.getBillingCurrencyCode())) {
            limitReqDTO.setAccountCurrency(preAuthorizationLogDTO.getBillingCurrencyCode());
        } else {
            limitReqDTO.setAccountCurrency(preAuthorizationLogDTO.getTransactionCurrencyCode());
        }
        limitReqDTO.setTransactionAmount(preAuthorizationLogDTO.getTransactionAmount());
        limitReqDTO.setDebitCreditIndicator(DebitCreditIndcatorEnum.CREDIT.getCode());

        String limitUnitJson = preAuthorizationLogDTO.getLimitUnitJson();
        if (StringUtils.isNotEmpty(limitUnitJson)) {
            limitReqDTO.setCalLimitUnits(JSONObject.parseArray(limitUnitJson, CalLimitUnitReqDTO.class));

            limitReqDTO.getCalLimitUnits().forEach(t -> {
                t.setRequestType(RequestTypeEnum.EXPIRE_DISCHARGE.getCode());
                t.setRequestSource(RequestSourceEnum.AUTH_MODULE.getCode());
            });
        }

        if (Objects.nonNull(cardAuthorizationDTO.getPartnerId())) {
            PartnerInfo partnerInfo = partnerInfoMapper.selectByPartnerId(cardAuthorizationDTO.getPartnerId());
            if (PartnerModeEnum.PA.getCode().equals(partnerInfo.getPartnerMode())) {
                ParmOrganizationInfo parmOrganizationInfo = parmOrganizationInfoSelfMapper.selectByOrganizationNumber(preAuthorizationLogDTO.getOrganizationNumber());

                //如果有保证金信息，构建保证金的同步历史
                buildPartnerMarginAndLog(preAuthorizationLogDTO, limitReqDTO, cardAuthorizationDTO, parmOrganizationInfo);
            }
        }

        PreAuthPayload preAuthPayload = new PreAuthPayload();
        preAuthPayload.setPreAuthorizationLogDTO(preAuthorizationLogDTO);
        preAuthPayload.setLimitReqDTO(limitReqDTO);
        return preAuthPayload;
    }

    private void buildPartnerMarginAndLog(PreAuthorizationLogDTO preAuthorizationLogDTO, LimitReqDTO limitReqDTO,
                                          CardAuthorizationInfo cardAuthorizationDTO, ParmOrganizationInfo parmOrganizationInfo) {
        String partnerId = cardAuthorizationDTO.getPartnerId();
        PartnerMarginLogDTO partnerMarginLogDTO = new PartnerMarginLogDTO();
        partnerMarginLogDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        partnerMarginLogDTO.setPartnerId(partnerId);
        partnerMarginLogDTO.setProcessFlag("0");
        partnerMarginLogDTO.setTransactionType(PartnerMarginTransTypeEnum.PRE_AUTH_EXPIRE_RELEASE.getCode());
        partnerMarginLogDTO.setDebitCreditInd(limitReqDTO.getDebitCreditIndicator());
        partnerMarginLogDTO.setCardNumber(cardAuthorizationDTO.getCardNumber());
        partnerMarginLogDTO.setRelatedLogId(preAuthorizationLogDTO.getPreauthLogId());
        partnerMarginLogDTO.setProcessDate(parmOrganizationInfo.getToday());
        partnerMarginLogDTO.setCurrency(preAuthorizationLogDTO.getBillingCurrencyCode());
        CalLimitUnitReqDTO paCalLimitUnitReqDTO = limitReqDTO.getCalLimitUnits().stream().filter(c -> LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(c.getLimitUnitCode())).findFirst().orElse(null);
        if (Objects.nonNull(paCalLimitUnitReqDTO)) {
            partnerMarginLogDTO.setPartnerMaxRecoverAmount(paCalLimitUnitReqDTO.getOverdrawAmount());
        }
        partnerMarginLogDTO.setCreateTime(LocalDateTime.now());
        partnerMarginLogDTO.setUpdateTime(LocalDateTime.now());
        partnerMarginLogDTO.setUpdateBy("Limit Expire Release");
        partnerMarginLogDTO.setVersionNumber(1L);
        limitReqDTO.setPartnerMarginLogDTO(partnerMarginLogDTO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchWrite(List<? extends PreAuthPayload> list) {
        if (list.isEmpty()) {
            return;
        }
        for (PreAuthPayload preAuthPayload : list) {
            logger.info("Calling customerLimitUpdateService.calculateLimitAndUpdate for globalFlowNumber: {}", preAuthPayload.getLimitReqDTO().getGlobalFlowNumber());
            customerLimitUpdateService.calculateLimitAndUpdate(preAuthPayload.getLimitReqDTO());
            logger.info("customerLimitUpdateService.calculateLimitAndUpdate completed for globalFlowNumber: {}", preAuthPayload.getLimitReqDTO().getGlobalFlowNumber());
            
            PreAuthorizationLogDTO preAuthorizationLogDTO = preAuthPayload.getPreAuthorizationLogDTO();
            PreAuthorizationLog preAuthorizationLog = new PreAuthorizationLog();
            preAuthorizationLog.setPreauthStatusCurr(PreAuthTrancactionStatusEnum.OVERDUE.getCode());
            preAuthorizationLog.setPreauthLogId(preAuthorizationLogDTO.getPreauthLogId());
            preAuthorizationLogMapper.updateByPrimaryKeySelective(preAuthorizationLog);
        }
    }

    @Override
    public PreAuthorizationLogDTO getByCardNumAndTraceId(String cardNum, String traceId, String settlementDate) {
        try {
            PreAuthorizationLog preAuthorizationLog = preAuthorizationLogSelfMapper.selectByByCardNumAndTraceId(cardNum, traceId, settlementDate);

            if (preAuthorizationLog != null) {
                PreAuthorizationLogDTO preAuthorizationLogDTO = new PreAuthorizationLogDTO();
                BeanCopier copierAccountBalanceModelToDTO = BeanCopier.create(PreAuthorizationLog.class, PreAuthorizationLogDTO.class, false);
                copierAccountBalanceModelToDTO.copy(preAuthorizationLog, preAuthorizationLogDTO, null);
                return preAuthorizationLogDTO;
            }
            return null;
        } catch (Exception e) {
            logger.error("Failed to query pre-authorization log by financial service code and settlement date, financial service code: {}, settlement date: {}", traceId, settlementDate, e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);
        }
    }

    @Override
    public List<PreAuthorizationLogDTO> getOrigPreByAuthCode(String cardNum, String authCode, String authTransType) {
        try {
            List<PreAuthorizationLog> preAuthorizationLogList = preAuthorizationLogSelfMapper.selectByAuthCodeAndTransType(cardNum, authCode, authTransType);
            List<PreAuthorizationLogDTO> result = null;
            if (!CollectionUtils.isEmpty(preAuthorizationLogList)) {
                result = BeanMapping.copyList(preAuthorizationLogList, PreAuthorizationLogDTO.class);
                return result;
            }

            return result;
        } catch (Exception e) {
            logger.error("Failed to query pre-authorization log by card number and authorization code, authorization code: {}, transaction type: {}", authCode, authTransType, e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, e);
        }
    }
}
