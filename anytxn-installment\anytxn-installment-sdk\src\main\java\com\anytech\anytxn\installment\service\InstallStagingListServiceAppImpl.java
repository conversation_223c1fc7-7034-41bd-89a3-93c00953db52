package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallFeeCalculationService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListServiceApp;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 可分期列表查询 业务逻辑实现
 *
 * <AUTHOR>
 * @date 2019/8/12
*/
@Service
public class InstallStagingListServiceAppImpl implements IInstallStagingListServiceApp {

    private static final Logger logger = LoggerFactory.getLogger(InstallStagingListServiceAppImpl.class);


    @Autowired
    private IInstallFeeCalculationService installFeeCalculationService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;
    @Autowired
    private StatementManager statementManager;

    /**
     * 可分期列表查询
     *
     * @param installTradingSearchKeyDTO 可分期列表查询参数
     * @return InstallTradingDTO
     */
    @Override
    public List<InstallTradingDTO> findInstallTradingByOptions(
            InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.debug("Query installable list parameter information");
        if (installTradingSearchKeyDTO  == null) {
            logger.error("Query installable list parameter information - parameter cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.IN_ST);
        }
        List<InstallTradingDTO> installTradingDTOList = new ArrayList<>();
        //必输项检查
        checkInstallTrading(installTradingSearchKeyDTO);
        BigDecimal termAmount = BigDecimal.ZERO;
        BigDecimal termdb;
        BigDecimal firstTermAmount;
        BigDecimal eachFee = BigDecimal.ZERO;
        BigDecimal firstFee = BigDecimal.ZERO;
        List<InstallProductInfoResDTO> installProduct =
                getInstallProByOrgNumType(installTradingSearchKeyDTO.getOrganizationNumber(), installTradingSearchKeyDTO.getType());
        InstallRateCalDTO installRateCal = null;
        for (InstallProductInfoResDTO i : installProduct) {
            InstallTradingDTO installTradingDTO = new InstallTradingDTO();
            if (i.getTerm() == null || i.getTerm() == 0) {
                installTradingDTO.setTerm(0);
                installRateCal = getInstallRateCal(i,0, installTradingSearchKeyDTO.getInstallAmount(), i.getFeeReceiveFlag(), i.getFeeCodeId(),installTradingSearchKeyDTO.getOrganizationNumber());
                termdb = new BigDecimal(0);
            } else {
                installTradingDTO.setTerm(i.getTerm());
                installRateCal = getInstallRateCal(i, i.getTerm(), installTradingSearchKeyDTO.getInstallAmount(), i.getFeeReceiveFlag(), i.getFeeCodeId(),installTradingSearchKeyDTO.getOrganizationNumber());
                termdb = new BigDecimal(i.getTerm());
            }
            installTradingDTO.setProductCode(i.getProductCode());
            logger.info("Calling installFeeCalculationService.installmentFeeCalculation with productCode={}", i.getProductCode());
            FeeTypeDTO feeTypeDTO = installFeeCalculationService.installmentFeeCalculation(installRateCal);
            //中行poc
            //尾款方式 0-余数放首期、1-余数放尾期
            String balanceMethod = i.getBalanceMethod();
            if ("I".equals(i.getFeeReceiveFlag())) {
                //尾款方式 0-余数放首期、1-余数放尾期
                if("0".equals(balanceMethod)){
                    eachFee = feeTypeDTO.getEashPayment();
                    firstFee = feeTypeDTO.getFirstPayment();
                }else {
                    firstFee = feeTypeDTO.getEashPayment();
                    eachFee = feeTypeDTO.getEashPayment();
                }

            }
            if ("F".equals(i.getFeeReceiveFlag())) {
                eachFee = BigDecimal.ZERO;
                firstFee = feeTypeDTO.getTotalCost();
            }
            //期末一次性收取
            if ("E".equals(i.getFeeReceiveFlag())) {
                //尾款方式 0-余数放首期、1-余数放尾期
                if("0".equals(balanceMethod)){
                    firstFee = feeTypeDTO.getEashPayment();
                    eachFee = feeTypeDTO.getFirstPayment();
                }else {
                    firstFee = feeTypeDTO.getEashPayment();
                    eachFee = feeTypeDTO.getEashPayment();
                }
            }
            if ("A".equals(i.getPaymentWay())) {
                if (termdb.compareTo(BigDecimal.ZERO) != 0) {
                    termAmount = installTradingSearchKeyDTO.getInstallAmount().divide(termdb, 0, BigDecimal.ROUND_DOWN).setScale(2);
                } else {
                    termAmount = installTradingSearchKeyDTO.getInstallAmount();
                }
                firstTermAmount = installTradingSearchKeyDTO.getInstallAmount()
                        .subtract(termAmount.multiply(termdb.subtract(BigDecimal.ONE)));
                installTradingDTO.setFirstRepay(firstTermAmount.add(firstFee));
                installTradingDTO.setRemainEachRepay(termAmount.add(eachFee));
            }
            else if ("F".equals(i.getPaymentWay())) {
                termAmount = BigDecimal.ZERO;
                installTradingDTO.setFirstRepay(firstFee);
                installTradingDTO.setRemainEachRepay(eachFee);
            }
            installTradingDTO.setInstallAmount(installTradingSearchKeyDTO.getInstallAmount());
            installTradingDTO.setFeeRate(feeTypeDTO.getFeeRate());
            installTradingDTO.setFirstFee(firstFee);
            installTradingDTO.setRemainEachFee(eachFee);
            installTradingDTO.setEndRepay(termAmount.add(eachFee));
            installTradingDTO.setEndFee(eachFee);
            installTradingDTO.setFeeFlag(i.getFeeReceiveFlag());
            installTradingDTO.setAccountManagementId(installTradingSearchKeyDTO.getAccountManagementId());
            installTradingDTO.setCardNumber(installTradingSearchKeyDTO.getCardNumber());
            installTradingDTO.setOriginTransactionId(installTradingSearchKeyDTO.getOriginTransactionId());

            installTradingDTO.setMerchantId(installTradingSearchKeyDTO.getMerchantId());
            installTradingDTO.setCustomerRegion(installTradingSearchKeyDTO.getCustomerRegion());
            installTradingDTO.setMcc(installTradingSearchKeyDTO.getMcc());
            installTradingDTO.setTransDate(installTradingSearchKeyDTO.getTransDate());
            //首次入账日计算
            logger.info("Calling statementManager.calculateFirstPostDate with accountManagementId={}", installTradingDTO.getAccountManagementId());
            installTradingDTO.setPaymentDate(statementManager.calculateFirstPostDate(i, installTradingDTO.getAccountManagementId()));
            installTradingDTOList.add(installTradingDTO);
        }
        return installTradingDTOList;
    }

    /**
     * 计算费用(信用卡运营用)
     * @param installDto
     * @return {@link InstallTradingDTO}
     */
    @Override
    public InstallTradingDTO calculateFee(InstallTradingSearchKeyDTO installDto){
        logger.info("Calling installProductInfoService.findByIndexOrgNumTypeTermPayWayFee with organizationNumber={}, type={}, term={}",
                installDto.getOrganizationNumber(), installDto.getType(), installDto.getTerm());
        InstallProductInfoResDTO installProduct = installProductInfoService.findByIndexOrgNumTypeTermPayWayFee(installDto.getOrganizationNumber(), installDto.getType(),
                installDto.getTerm(), installDto.getPaymentWay(), installDto.getFeeFlag(),"1");
        logger.info("installProductInfoService.findByIndexOrgNumTypeTermPayWayFee completed: productCode={}", installProduct != null ? installProduct.getProductCode() : null);
        InstallRateCalDTO installRateCal = getInstallRateCal(installProduct,installProduct.getTerm(), installDto.getInstallAmount(), installProduct.getFeeReceiveFlag(), installProduct.getFeeCodeId(),installDto.getOrganizationNumber());
        BigDecimal termdb = new BigDecimal(installProduct.getTerm());
        BigDecimal termAmount = BigDecimal.ZERO;
        BigDecimal firstTermAmount;
        BigDecimal eachFee = BigDecimal.ZERO;
        BigDecimal firstFee = BigDecimal.ZERO;
        logger.info("Calling installFeeCalculationService.installmentFeeCalculation");
        FeeTypeDTO feeTypeDTO = installFeeCalculationService.installmentFeeCalculation(installRateCal);
        logger.info("installFeeCalculationService.installmentFeeCalculation completed");
        if ("I".equals(installProduct.getFeeReceiveFlag())) {
            eachFee = feeTypeDTO.getEashPayment();
            firstFee = feeTypeDTO.getFirstPayment();
        }
        if ("F".equals(installProduct.getFeeReceiveFlag())) {
            eachFee = BigDecimal.ZERO;
            firstFee = feeTypeDTO.getTotalCost();
        }
        InstallTradingDTO installTradingDTO = new InstallTradingDTO();
        if ("A".equals(installProduct.getPaymentWay())) {
            if (termdb.compareTo(BigDecimal.ZERO) != 0) {
                termAmount = installDto.getInstallAmount().divide(termdb, 2, BigDecimal.ROUND_DOWN);
            } else {
                termAmount = installDto.getInstallAmount();
            }
            firstTermAmount = installDto.getInstallAmount()
                    .subtract(termAmount.multiply(termdb.subtract(BigDecimal.ONE)));
            installTradingDTO.setFirstRepay(firstTermAmount.add(firstFee));
            installTradingDTO.setRemainEachRepay(termAmount.add(eachFee));
        }
        else if ("F".equals(installProduct.getPaymentWay())) {
            termAmount = BigDecimal.ZERO;
            installTradingDTO.setFirstRepay(firstFee);
            installTradingDTO.setRemainEachRepay(eachFee);
        }
        installTradingDTO.setInstallAmount(installDto.getInstallAmount());
        installTradingDTO.setFeeRate(feeTypeDTO.getFeeRate());
        installTradingDTO.setFirstFee(firstFee);
        installTradingDTO.setRemainEachFee(eachFee);
        installTradingDTO.setEndRepay(termAmount.add(eachFee));
        installTradingDTO.setEndFee(eachFee);
        installTradingDTO.setFeeFlag(installProduct.getFeeReceiveFlag());
        installTradingDTO.setAccountManagementId(installDto.getAccountManagementId());
        installTradingDTO.setCardNumber(installDto.getCardNumber());
        installTradingDTO.setOriginTransactionId(installDto.getOriginTransactionId());
        return installTradingDTO;
    }


    /**
     * 分期手续费计算参数赋值
     * @return InstallRateCalDTO
     */
    public InstallRateCalDTO getInstallRateCal(InstallProductInfoResDTO installProduct,
                                               Integer term, BigDecimal installmentAmount,
                                               String feeReceiveFlag, String installmentFeeCodeBean,
                                               String organizationNumber) {
        InstallRateCalDTO installRateCalDTO = new InstallRateCalDTO();
        installRateCalDTO.setOrganizationNumber(organizationNumber);
        installRateCalDTO.setInstallmentPriceFlag("0");
        installRateCalDTO.setInstallmentTotalFee(BigDecimal.ZERO);
        installRateCalDTO.setInstallmentFeeRate(BigDecimal.ZERO);
        installRateCalDTO.setInstallmentDerateMethod("0");
        installRateCalDTO.setInstallmentDerateValue(BigDecimal.ZERO);
        installRateCalDTO.setInstallmentTerm(term);
        installRateCalDTO.setInstallmentAmount(installmentAmount);
        installRateCalDTO.setFeeFlag(feeReceiveFlag);
        installRateCalDTO.setInstallmentFeeCodeBean(installmentFeeCodeBean);
        installRateCalDTO.setInstallParameterDTO(new InstallParameterDTO());

        installRateCalDTO.getInstallParameterDTO().setInstallProInfo(installProduct);
        return installRateCalDTO;
    }

    /**
     * 校验字段
     */
    private void checkInstallTrading(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getOrganizationNumber())) {
            logger.error("Installable list query - organization number is required");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.RE_OR_IN);
        }
        if (installTradingSearchKeyDTO.getInstallAmount() == null) {
            logger.error("Installable list query - installment amount is required");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.RE_AM_IN);
        }
        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getType())) {
            logger.error("Installable list query - installment type is required");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.RE_TY_IN);
        }
    }

    public List<InstallProductInfoResDTO> getInstallProByOrgNumType(String organizationNumber, String prodType) {
        logger.info("Calling installProductInfoService.findByOrgNumType with organizationNumber={}, prodType={}", organizationNumber, prodType);
        List<InstallProductInfoResDTO> result = installProductInfoService.findByOrgNumType(organizationNumber, prodType);
        logger.info("installProductInfoService.findByOrgNumType completed");
        if (result == null){
            logger.error("Failed to query installment product by organization number and installment type: organizationNumber={}, prodType={}", organizationNumber, prodType);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }
    public InstallProductInfoResDTO getInstallProByOrgNumTypeTerm(String organizationNumber, String prodType, String term) {
        logger.info("Calling installProductInfoService.findProInfoByTermAndType with organizationNumber={}, prodType={}, term={}", organizationNumber, prodType, term);
        InstallProductInfoResDTO result = installProductInfoService.findProInfoByTermAndType(organizationNumber, prodType, Integer.valueOf(term));
        logger.info("installProductInfoService.findProInfoByTermAndType completed");
        if (result == null){
            logger.error("Failed to query installment product by organization number, installment type and term: organizationNumber={}, prodType={}, term={}", organizationNumber, prodType, term);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }


    /**
     * 提供给运营的接口，计算费用
     * @param installDto 分期相关参数
     * @return OperateInstallFeeResDto
     */
    @Override
    public OperateInstallFeeResDto calculateFeeForOperate(InstallTradingSearchKeyDTO installDto){
        //分期期数
        Integer term = installDto.getTerm();
        if(StringUtils.isEmpty(installDto.getFeeFlag())){
            logger.error("Installment fee collection method cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.IN_SE_E);
        }
        if(StringUtils.isEmpty(installDto.getPaymentWay())){
            logger.error("Installment repayment method cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.IN_RE_B);
        }
        InstallTradingDTO installTrading = calculateFee(installDto);
        OperateInstallFeeResDto freeInstallFee = new OperateInstallFeeResDto();
        //首期手续费
        BigDecimal firstFee = installTrading.getFirstFee();
        //剩余每期手续费
        BigDecimal remainEachFee = installTrading.getRemainEachFee();
        BigDecimal remainTerm = BigDecimal.valueOf(term - 1);
        //总费用
        BigDecimal totalFeeAmount = firstFee.add(remainEachFee.multiply(remainTerm));
        //首期应还
        BigDecimal firstRepay = installTrading.getFirstRepay();
        //剩余每期应还
        BigDecimal remainEachRepay = installTrading.getRemainEachRepay();
        //总金额
        BigDecimal totalAmount = firstRepay.add(remainEachRepay.multiply(remainTerm));
        freeInstallFee.setTermFee(firstFee);
        freeInstallFee.setTotalFeeAmount(totalFeeAmount);
        freeInstallFee.setTermAmount(firstRepay);
        freeInstallFee.setTotalAmount(totalAmount);
        return freeInstallFee;
    }
}
