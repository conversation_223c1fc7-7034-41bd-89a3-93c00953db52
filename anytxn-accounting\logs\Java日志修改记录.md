# anytxn-accounting-日志修改记录

## 1. 进度统计

| 模块名称 | 文件总数 | 已完成 | 已跳过 | 待处理 |
|---|---|----|----|----| 
| anytxn-accounting-sdk | 21 | 15 | 6 | 0 |
| anytxn-accounting-batch | 52 | 7 | 30 | 15 |
| anytxn-accounting-base | 0 | 0 | 0 | 0 |
| anytxn-accounting-server | 0 | 0 | 0 | 0 |
| **总计** | **73** | **19** | **35** | **19** |

## 2. 修改类清单

### 2.1 anytxn-accounting-sdk模块

#### 2.1.1 anytxn-accounting-sdk-controller模块

| 序号 | 类名 | 包路径 | 状态 |
|---|---|-----|---|
| 2 | AccountAbsStatusController | com.anytech.anytxn.accounting.controller | ✅已完成 |
| 3 | AccountantController | com.anytech.anytxn.accounting.controller | ✅已完成 |
| 4 | AccountSummaryController | com.anytech.anytxn.accounting.controller | ✅已完成 |
| 5 | GlAdjController | com.anytech.anytxn.accounting.controller | ✅已完成 |

#### 2.1.2 anytxn-accounting-sdk-service模块

| 序号 | 类名 | 包路径 | 状态 |
|---|---|-----|---|
| 6 | AccountsCheckServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 7 | AccountsOccurCheckServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 8 | AccountSummaryServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 9 | AmsGlamsServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 10 | BalServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 11 | FileWriteHandle | com.anytech.anytxn.accounting.service | ✅已完成 |
| 12 | GlAmsAcgdServiceImpl | com.anytech.anytxn.accounting.service | ❌已跳过 |
| 13 | GlAmsAcgmServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 14 | GlAmsAcgyServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 15 | GlvcherFileServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 16 | GlvcherSumServiceImpl | com.anytech.anytxn.accounting.service | ❌已跳过 |
| 17 | GlVoucherAdjServiceImpl | com.anytech.anytxn.accounting.service | ❌已跳过 |
| 18 | GlVoucherServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 19 | IllegalDataFileServiceImpl | com.anytech.anytxn.accounting.service | ❌已跳过 |
| 20 | OriRecordDetailCalcProcessRecordPartitionImpl | com.anytech.anytxn.accounting.service | ❌已跳过 |
| 21 | VoucherManageServiceImpl | com.anytech.anytxn.accounting.service | ✅已完成 |
| 22 | AccountantJdbcService | com.anytech.anytxn.accounting.service.jdbc | ❌已跳过 |

### 2.2 anytxn-accounting-batch模块

#### 2.2.1 anytxn-accounting-batch-main模块

| 序号 | 类名 | 包路径 | 状态 |
|---|---|-----|---|
| 23 | AnytxnAccountingBatchApplication | com.anytech.anytxn.accounting.batch | ❌已跳过 |

#### 2.2.2 anytxn-accounting-batch-config模块

| 序号 | 类名 | 包路径 | 状态 |
|---|---|-----|---|
| 24 | AccountantBatchConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 25 | AmountDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 26 | BaseConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 27 | BaseDMQYConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 28 | CardDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 29 | CardSettleReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 30 | CardTransReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 31 | CommonConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 32 | CustomerDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 33 | FileManagerConfig | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 34 | InstallBalanceReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 35 | InstallDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 36 | InstallFreeDayReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 37 | InstallFreeReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 38 | InstallMonthReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 39 | OrgAmountDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 40 | OutCustomerDetailReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 41 | TransactionDaySumReportConfigurer | com.anytech.anytxn.accounting.batch.config | ❌已跳过 |
| 42 | ThreadPoolConfig | com.anytech.anytxn.accounting.batch.config.file | ❌已跳过 |
| 43 | TxnRecordSumConfig | com.anytech.anytxn.accounting.batch.config.file | ✅已完成 |
| 44 | IllegalGlamsSumFileConfig | com.anytech.anytxn.accounting.batch.config.file.IrrgulardataFiles | ❌已跳过 |
| 45 | IllegalGlvcherFileConfig | com.anytech.anytxn.accounting.batch.config.file.IrrgulardataFiles | ❌已跳过 |
| 46 | OriRecordDetailNewConfig | com.anytech.anytxn.accounting.batch.config.file.newConfigs | ✅已完成 |
| 47 | OriRecordDetailConfig | com.anytech.anytxn.accounting.batch.config.file | ❌已跳过 |
| 48 | RecordSumInDBConfig | com.anytech.anytxn.accounting.batch.config.file.subjectSumFile | ✅已完成 |
| 49 | RecordSumInDBPartitioner | com.anytech.anytxn.accounting.batch.config.file.subjectSumFile | ❌已跳过 |
| 50 | RecordSumInDBProcessor | com.anytech.anytxn.accounting.batch.config.file.subjectSumFile | ✅已完成 |
| 51 | RecordSumInDBWriter | com.anytech.anytxn.accounting.batch.config.file.subjectSumFile | ❌已跳过 |
| 52 | RecordSumOutConfig | com.anytech.anytxn.accounting.batch.config.file.subjectSumFile | ❌已跳过 |

#### 2.2.3 anytxn-accounting-batch-job模块

| 序号 | 类名 | 包路径 | 状态 |
|---|---|-----|---|
| 53 | AcBalanceSnapshotConfig | com.anytech.anytxn.accounting.batch.job.acbalance.config | ❌已跳过 |
| 54 | AccountBalanceProcessor | com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance | ✅已完成 |
| 55 | AccountBalanceReader | com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance | ✅已完成 |
| 56 | AccountBalanceWriter | com.anytech.anytxn.accounting.batch.job.acbalance.step.accountbalance | ❌已跳过 |
| 57 | InstallOrderProcessor | com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder | ✅已完成 |
| 58 | InstallOrderReader | com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder | ✅已完成 |
| 59 | InstallOrderWriter | com.anytech.anytxn.accounting.batch.job.acbalance.step.installorder | ❌已跳过 |
| 60 | AccountCheckOccurJobConfig | com.anytech.anytxn.accounting.batch.job.accountcheckoccur.config | ❌已跳过 |
| 61 | AccountOccurCheckProcessor | com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step | ✅已完成 |
| 62 | AccountOccurCheckReader | com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step | ✅已完成 |
| 63 | AccountOccurCheckWriter | com.anytech.anytxn.accounting.batch.job.accountcheckoccur.step | ❌已跳过 |
| 64 | GeneGlVoucherJobConfig | com.anytech.anytxn.accounting.batch.job.generateglvoucher.config | ✅已完成 |
| 65 | VoucherPartitioner | com.anytech.anytxn.accounting.batch.job.generateglvoucher.partitioner | ❌已跳过 |
| 66 | AmsToVoucherProcessor | com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep | ✅已完成 |
| 67 | AmsToVoucherReader | com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep | ✅已完成 |
| 68 | AmsToVoucherWriter | com.anytech.anytxn.accounting.batch.job.generateglvoucher.step.amstovoucherstep | ❌已跳过 |
| 69 | GlamsFromAcquirerConfig | com.anytech.anytxn.accounting.batch.job.glamsfromacquirer.config | ❌已跳过 |
| 70 | GlamsFromAcquirerTasklet | com.anytech.anytxn.accounting.batch.job.glamsfromacquirer.step | ❌已跳过 |
| 71 | UpiGlamsConfig | com.anytech.anytxn.accounting.batch.job.glamsfromupi.config | ❌已跳过 |
| 72 | C602DZToLogTable | com.anytech.anytxn.accounting.batch.job.glamsfromupi.steps | ✅已完成 |
| 73 | GlvcherSumJobConfig | com.anytech.anytxn.accounting.batch.job.glvchersum.config | ✅已完成 |
| 74 | VoucherSumPartitioner | com.anytech.anytxn.accounting.batch.job.glvchersum.partitioner | ❌已跳过 |

### 2.3 anytxn-accounting-base模块

没有要修改的类

### 2.4 anytxn-accounting-server模块

没有要修改的类



