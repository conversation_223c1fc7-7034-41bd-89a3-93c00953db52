package com.anytech.anytxn.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.utils.JacksonUtils;
import com.anytech.anytxn.installment.base.domain.dto.FeeTypeDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallRateCalDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallmentInterestDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.BaseAmountFlagEnum;
import com.anytech.anytxn.installment.base.enums.ChargeOptionEnum;
import com.anytech.anytxn.installment.base.enums.FeeReceiveFagEnum;
import com.anytech.anytxn.installment.base.enums.FeeTypeEnum;
import com.anytech.anytxn.installment.base.enums.InstallDerateMethodEnum;
import com.anytech.anytxn.installment.base.enums.InstallPriceFlagEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.service.IInstallFeeCalculationService;
import com.anytech.anytxn.installment.service.interest.FixInterestRate;
import com.anytech.anytxn.installment.service.interest.TermLoanInterest;
import com.anytech.anytxn.installment.service.manager.InstallManager;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-06-13 16:21
 * 分期手续费计算
 **/
@Service
public class InstallFeeCalculationServicrImpl implements IInstallFeeCalculationService {
    private Logger logger = LoggerFactory.getLogger(InstallFeeCalculationServicrImpl.class);


    private BigDecimal hundred = new BigDecimal("100");

    @Autowired
    private IInstallFeeCodeInfoService installFeeCodeInfoService;


    /**
     *
     * @param installRateCalDTO 分期手续费
     * @return {@link FeeTypeDTO}
     *
     * 1.在基于参数配置计算出来的费用后 要加上gst费用
     * installFeeCode.setAnnualInterestRate(new BigDecimal("10"));
     * installFeeCode.setFeeRateCalculateType("F");
     * installFeeCode.setGstInterestRate(new BigDecimal("5"));
     */
    @Override
    public FeeTypeDTO installmentFeeCalculation(InstallRateCalDTO installRateCalDTO) {
        //费率代码
        InstallFeeCodeInfoResDTO installFeeCode = getInstallFeeCodeByOrgNumAndFeeCode(installRateCalDTO.getOrganizationNumber()
                , installRateCalDTO.getInstallmentFeeCodeBean());
        installRateCalDTO.getInstallParameterDTO().setInstallFeeCode(installFeeCode);

        //分期定价方式
        String installmentPriceFlag = installRateCalDTO.getInstallmentPriceFlag();

        //执行取基础定价逻辑
        if (Objects.equals(InstallPriceFlagEnum.BASICPRICING_FLAG.getCode(), installmentPriceFlag)) {
            FeeTypeDTO feeTypeBasePrice = casicPricinglogic(installRateCalDTO, installFeeCode);
            feeTypeBasePrice.setUnReceiveFeeTerm(Optional.ofNullable(installFeeCode.getUnReceiveFeeTerm()).orElse(0));
            return feeTypeBasePrice;
        }


        FeeTypeDTO feeTypeDTO = new FeeTypeDTO();
        feeTypeDTO.setUnReceiveFeeTerm(Optional.ofNullable(installFeeCode.getUnReceiveFeeTerm()).orElse(0));
        //分期定价方式 1-外围上送总费用
        if (Objects.equals(InstallPriceFlagEnum.TOTALCOST_FLAG.getCode(), installmentPriceFlag)) {
            //总费用
            BigDecimal totalCost = installRateCalDTO.getInstallmentTotalFee();
            //分期费用增加gst
            totalCost = totalCost.add(getGstFeeCost(totalCost,installFeeCode));

            //费用代码表的费率  用于计算
            BigDecimal rate = BigDecimal.ZERO;
            feeTypeDTO.setTotalCost(totalCost);
            feeTypeDTO.setFeeRate(rate);
            return differentPricing(installRateCalDTO, feeTypeDTO);
        }
        //分期定价方式2-外围上送总费率
        if (Objects.equals(InstallPriceFlagEnum.DELIVERYRATE_FLAG.getCode(), installmentPriceFlag)) {
            //总费用
            BigDecimal totalCost = installRateCalDTO.getInstallmentAmount().multiply(installRateCalDTO.getInstallmentFeeRate())
                    .divide(hundred);
            totalCost = totalCost.add(getGstFeeCost(totalCost,installFeeCode));

            //费用代码表的费率  用于计算
            BigDecimal rate = installRateCalDTO.getInstallmentFeeRate();
            feeTypeDTO.setTotalCost(totalCost);
            feeTypeDTO.setFeeRate(rate);
            return differentPricing(installRateCalDTO, feeTypeDTO);
        }
        return null;
    }


    private BigDecimal feeRateCalculate(InstallFeeCodeInfoResDTO installFeeCode, InstallRateCalDTO installRateCalDTO) {
        // 年化利率不能为null  可以为 0
        if (installFeeCode.getAnnualInterestRate() == null){
            logger.error("Annual interest rate cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ANNUAL_RATE_NULL);
        }

        InstallmentInterestDTO interestDTO = InstallmentInterestDTO.InstallmentInterestDTOBuilder.anInstallmentInterestDTO()
                .withAnnualInterestRate(installFeeCode.getAnnualInterestRate())
                .withCostAmount(installRateCalDTO.getInstallmentAmount())
                .withTerm(installRateCalDTO.getInstallmentTerm())
                .withBalanceMethod(installRateCalDTO.getInstallParameterDTO().getInstallProInfo().getBalanceMethod())
                .build();

        if (Objects.equals(ChargeOptionEnum.FLAT_RATE.getCode(),installFeeCode.getChargeOption())){
            return new FixInterestRate().getInterestResult(interestDTO).getInterestAmount();
        }else if (Objects.equals(ChargeOptionEnum.T_TERM.getCode(), installFeeCode.getChargeOption())){
            return new TermLoanInterest().getInterestResult(interestDTO).getInterestAmount();
        }

        logger.error("The calculation method for handling fees does not exist");
        throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ORDER_DATE_ERROR);
    }




    /**
     * 差异定价
     *
     * @param installRateCalDTO 分期费用计算参数
     * @param feeType 封装手续费计算涉及的参数
     * @return {@link FeeTypeDTO}
     */
    public FeeTypeDTO differentPricing(InstallRateCalDTO installRateCalDTO, FeeTypeDTO feeType) {
        //分期定价方式installmentPriceFlag="1","2"  取接口
        String derateMethod = installRateCalDTO.getInstallmentDerateMethod();
        BigDecimal derateValue = installRateCalDTO.getInstallmentDerateValue();
        //分期费用 总
        BigDecimal totalCost = feeType.getTotalCost();

        BigDecimal eashPayment = BigDecimal.ZERO;
        BigDecimal firstPayment = BigDecimal.ZERO;

        //手续费收取方式
        String feeFlag = installRateCalDTO.getFeeFlag();
        //获取分期期数
        Integer term = installRateCalDTO.getInstallmentTerm();
        BigDecimal termbd = new BigDecimal(term);
        //不收取手续费期数
        Integer unReceiveFeeTerm = feeType.getUnReceiveFeeTerm();
        if(null != unReceiveFeeTerm && unReceiveFeeTerm.compareTo(0) > 0){
            termbd = termbd.subtract(BigDecimal.valueOf(unReceiveFeeTerm));
        }

        BigDecimal[] array = new BigDecimal[term];

        if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeFlag)){
            firstPayment = totalCost;
            eashPayment = BigDecimal.ZERO;
        }

        if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeFlag)) {
            //手续费收取方式为I-分期收取 向下取整
            eashPayment = totalCost.divide(termbd, 0, BigDecimal.ROUND_DOWN);
            firstPayment = totalCost.subtract(eashPayment.multiply(termbd.subtract(BigDecimal.ONE)));
        }

        setEachTermFeeArray(array,totalCost,firstPayment,eashPayment,installRateCalDTO);
        feeType.setEachTermFeeAmount(array);
        feeType.setFirstPayment(firstPayment);
        feeType.setEashPayment(eashPayment);
        feeType.setEachTermFeeAmount(array);
        feeType.setFeeFlag(feeFlag);
        feeType.setDerateMethod(derateMethod);
        feeType.setDerateValue(derateValue);
        feeType.setTerm(termbd);
        return reliefProcess(feeType, derateValue,installRateCalDTO);
    }

    private void setEachTermFeeArray(BigDecimal[] array, BigDecimal totalCost, BigDecimal firstAmount,
                                     BigDecimal eachAmount,InstallRateCalDTO installRateCalDTO) {

        String feeFlag = installRateCalDTO.getFeeFlag();
        //期初、期末一次性收取 分期收取
        if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeFlag)) {
            Arrays.fill(array, BigDecimal.ZERO);
            array[0] = totalCost;
        } else if (Objects.equals(FeeReceiveFagEnum.END_ONE_TIME.getCode(), feeFlag)) {
            Arrays.fill(array, BigDecimal.ZERO);
            array[array.length - 1] = totalCost;
        } else if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeFlag)) {
            Arrays.fill(array, eachAmount);
            if (InstallManager.isFirstTerm(installRateCalDTO.getInstallParameterDTO().getInstallProInfo().getBalanceMethod())){
                array[0] = firstAmount;
            }
            if (InstallManager.isLastTerm(installRateCalDTO.getInstallParameterDTO().getInstallProInfo().getBalanceMethod())){
                array[array.length - 1] = firstAmount;
            }
        }
        logger.info("installment fee array is {}",JacksonUtils.toJsonStr(array));
    }

    /**
     * 执行取基础定价逻辑
     *
     * @param installRateCalDTO  费率计算参数
     * @param installFeeCode 分期费用代码
     * @return {@link FeeTypeDTO}
     */
    private FeeTypeDTO casicPricinglogic(InstallRateCalDTO installRateCalDTO, InstallFeeCodeInfoResDTO installFeeCode) {

        FeeTypeDTO feeType = new FeeTypeDTO();
        //分期金额
        BigDecimal installmentAmount = installRateCalDTO.getInstallmentAmount();
        //手续费收取方式
        String feeFlag = installRateCalDTO.getFeeFlag();
        //获取分期期数
        Integer term = installRateCalDTO.getInstallmentTerm();
        BigDecimal termbd = new BigDecimal(term);
        //减免值或者是减免期数
        BigDecimal derateValue = installFeeCode.getDerateValue();
        //减免方式
        feeType.setDerateMethod(installFeeCode.getDerateMethod());

        //临界金额 用于匹配基础定价逻辑中的收费阶梯
        BigDecimal poundage = BigDecimal.ZERO;
        if (Objects.equals(BaseAmountFlagEnum.TOTAL_AMOUNT.getCode(), installFeeCode.getBaseAmountFlag())) {
            //设置收费金额：
            poundage = installmentAmount;
        }
        if (Objects.equals(BaseAmountFlagEnum.PER_ISSUE_AMOUNT.getCode(), installFeeCode.getBaseAmountFlag())) {
            poundage = installmentAmount.divide(termbd, 2, BigDecimal.ROUND_DOWN);
        }
        //匹配收费阶梯
        BigDecimal thresholdAmount1 = installFeeCode.getThresholdAmount1();
        BigDecimal thresholdAmount2 = installFeeCode.getThresholdAmount2();
        BigDecimal thresholdAmount3 = installFeeCode.getThresholdAmount3();
        //费用代码表的费率  用于计算
        BigDecimal rate = BigDecimal.ZERO;
        if (poundage.compareTo(thresholdAmount1) < 1) {
            rate = installFeeCode.getFeeRate1();
        } else if (poundage.compareTo(thresholdAmount2) < 1) {
            rate = installFeeCode.getFeeRate2();
        } else if (poundage.compareTo(thresholdAmount3) < 1) {
            rate = installFeeCode.getFeeRate3();
        }
        feeType.setFeeRate(rate);
        feeType.setPoundage(poundage);
        feeType.setTerm(termbd);
        feeType.setFeeFlag(feeFlag);
        feeType.setDerateValue(derateValue);
        //计算费用
        feeType = calculateCost(installFeeCode, feeType,installRateCalDTO);

        feeType = reliefProcess(feeType, derateValue, installRateCalDTO);


        logger.info("Basic pricing calculation results is {}", JacksonUtils.toJsonStr(feeType));
        return feeType;
    }

    /**
     * 按照基础定价模式计算的费用
     *
     * @param installFeeCode  分期费用代码
     * @param feeType 手续费计算涉及的参数
     * @param installRateCalDTO 分期参数
     * @return FeeTypeDTO
     */
    public FeeTypeDTO calculateCost(InstallFeeCodeInfoResDTO installFeeCode, FeeTypeDTO feeType, InstallRateCalDTO installRateCalDTO) {
        //获取分期期数
        BigDecimal termbd = feeType.getTerm();

        //未收取手续费期数
        BigDecimal unReceiveFeeTerm = installFeeCode.getUnReceiveFeeTerm() == null ? BigDecimal.ZERO : BigDecimal.valueOf(installFeeCode.getUnReceiveFeeTerm());
        termbd = termbd.subtract(unReceiveFeeTerm);

        String feeFlag = feeType.getFeeFlag();
        //计算费用 R-固定金额  P-费率
        BigDecimal cost = BigDecimal.ZERO;

        /*
           1. 如果分期费用代码中设置了利率以及利率计算方式( F – Flat Rate固定利率 T – Term Loan定期)
           2. 则按照利率计算方式设置的值进行费用计算
         */
        if (StringUtils.equalsAny(installFeeCode.getChargeOption(), ChargeOptionEnum.FLAT_RATE.getCode(), ChargeOptionEnum.T_TERM.getCode())){
            cost = feeRateCalculate(installFeeCode, installRateCalDTO);
        }else {
            //套档金额按比例收
            if (Objects.equals(ChargeOptionEnum.PROPORTION.getCode(), installFeeCode.getChargeOption())) {
                cost = feeType.getPoundage().multiply(feeType.getFeeRate()).divide(hundred,2,BigDecimal.ROUND_DOWN);
            }else if (Objects.equals(ChargeOptionEnum.FIXED_FEE.getCode(), installFeeCode.getChargeOption())) {
                //区间金额按固定金额收
                cost = feeType.getFeeRate();
            }else if (Objects.equals(ChargeOptionEnum.APP_PROPORTION.getCode(), installFeeCode.getChargeOption())){
                cost = feeType.getPoundage().multiply(feeType.getFeeRate()).divide(hundred,2,BigDecimal.ROUND_UP);
            }
        }
        logger.info("fee total cost is {}", cost);
        //首期费用
        BigDecimal firstPayment = BigDecimal.ZERO;
        //每期手续费
        BigDecimal eashPayment = BigDecimal.ZERO;
        BigDecimal totalCost;
        BigDecimal[] array = new BigDecimal[termbd.intValue()];

        //如果需要计算再去计算
        if (Objects.equals(installFeeCode.getFeeComputeFlag(),"N")){
            Arrays.fill(array,BigDecimal.ZERO);
            feeType.setEachTermFeeAmount(array);
            //分期费用金额
            feeType.setTotalCost(BigDecimal.ZERO);
            //首期费用金额
            feeType.setFirstPayment(firstPayment);
            //每期费用金额
            feeType.setEashPayment(eashPayment);
            return feeType;
        }


        // 如果费用类型为A-总费用, 则cost指的是本次分期收取的总费用
        if (Objects.equals(FeeTypeEnum.TOTAL_FEE.getCode(), installFeeCode.getFeeType())) {
            //总费用 + gst费用
            totalCost = cost.add(getGstFeeCost(cost,installFeeCode));
            //手续费收取方式为F-期初一次性收取
            if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeFlag)) {
                //首期手续费
                firstPayment = totalCost;
                //每期手续费
                eashPayment = BigDecimal.ZERO;
            }

            //手续费收取方式为I-分期收取
            if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeFlag)) {
                //每期手续费
                eashPayment = totalCost.divide(termbd, 0, BigDecimal.ROUND_DOWN);
                //首期手续费   总费用 - (每期手续费*(期数-1))
                firstPayment = totalCost.subtract(eashPayment.multiply(termbd.subtract(BigDecimal.ONE))).setScale(2, RoundingMode.HALF_UP);

            }

            setEachTermFeeArray(array,totalCost,firstPayment,eashPayment, installRateCalDTO);
            feeType.setEachTermFeeAmount(array);
            //分期费用金额
            feeType.setTotalCost(totalCost);
            //首期费用金额
            feeType.setFirstPayment(firstPayment);
            //每期费用金额
            feeType.setEashPayment(eashPayment);
        }

        //费用类型为T-每期费用 则cost指的是每一期收取的手续费用, temrd = 总期数 - 免费期数
        if (Objects.equals(FeeTypeEnum.INSTALMENT_FEE.getCode(), installFeeCode.getFeeType())) {
            //总费用*分期期数
            totalCost = cost.multiply(new BigDecimal(installRateCalDTO.getInstallmentTerm()));
            //总费用 + gst金额
            totalCost = totalCost.add(getGstFeeCost(totalCost,installFeeCode));


            if (Objects.equals(FeeReceiveFagEnum.ONE_TIME.getCode(), feeFlag)) {
                //首期手续费 = 总手续费
                firstPayment = totalCost;
                //每期手续费 = 0
                eashPayment = BigDecimal.ZERO;
            }

            //如果是分期收取,分期费用取整 余数存放首期或者尾期
            if (Objects.equals(FeeReceiveFagEnum.INSTALMENT.getCode(), feeFlag)) {
                eashPayment = totalCost.divide(termbd, 0, BigDecimal.ROUND_DOWN);
                firstPayment =  totalCost.subtract(eashPayment.multiply(termbd.subtract(BigDecimal.ONE)))
                        .setScale(2, RoundingMode.HALF_UP);
            }

            setEachTermFeeArray(array, totalCost, firstPayment, eashPayment, installRateCalDTO);
            feeType.setEachTermFeeAmount(array);
            //分期费用金额
            feeType.setTotalCost(totalCost);
            //首期费用金额
            feeType.setFirstPayment(firstPayment);
            //每期费用金额
            feeType.setEashPayment(eashPayment);
        }
        return feeType;
    }

    /**
     * 减免处理
     * 基础定价逻辑时 减免方式以及减免值从分期费用代码中获取
     * 外围传送时,减免方式以及减免值从订单录入中获取
     *
     * @param feeType 手续费计算涉及的参数
     * @param derateValue 分期减免值
     * @param installRateCalDTO 分期相关参数
     * @return HashMap
     */
    private FeeTypeDTO reliefProcess(FeeTypeDTO feeType, BigDecimal derateValue, InstallRateCalDTO installRateCalDTO) {
        logger.info("installment derate organizationNumber {},derateValue{}", installRateCalDTO.getOrganizationNumber(),derateValue);

        String derateMethod = feeType.getDerateMethod();

        if (StringUtils.equalsAny("1","2","3") && derateValue == null){
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_DERATE_VALUE_NOT_NULL_FAULT);
        }

        if (Objects.equals(InstallDerateMethodEnum.NO_BREAKS.getCode(), derateMethod)) {
            return noBreaks(feeType);
        }
        //减免方式为1-减免期数
        if (Objects.equals(InstallDerateMethodEnum.BREAKS_TERM.getCode(), derateMethod)) {
            return breaksTerm(feeType, derateValue);

        } //减免方式为2-减免金额
        else if (Objects.equals(InstallDerateMethodEnum.BREAKS_AMOUNT.getCode(), derateMethod)) {
            return breaksAmount(feeType, derateValue);
        }//减免方式为3-每期打折
        else if (Objects.equals(InstallDerateMethodEnum.EACH_DISCOUNT.getCode(), derateMethod)) {
            return eachDiscount(feeType, derateValue);
        }

        throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_DERATE_VALUE_NOT_NULL_FAULT);
    }

    /**
     * 不减免
     *
     * @param feeType 手续费计算涉及的参数
     * @return {@link FeeTypeDTO}
     */
    private FeeTypeDTO noBreaks(FeeTypeDTO feeType) {
        BigDecimal[] array = new BigDecimal[1];
        array[0] = BigDecimal.ZERO;
        //减免费用金额
        feeType.setDerateFeeAmount(array);
        //减免期数
        feeType.setDerateTerm(0);
        //分期费用金额
        feeType.setTotalCost(feeType.getTotalCost());
        //首期费用金额
        feeType.setFirstPayment(feeType.getFirstPayment());
        //每期费用金额
        feeType.setEashPayment(feeType.getEashPayment());
        return feeType;
    }

    /**
     * 1. 由于分期订单中的手续费向下取整,因此手续费金额可以为0
     * 2. 减免期数,从第一期开始,如果本期为0 则减免手续费也为0, 减免期数阈值减少1;
     *
     * @param feeType 手续费计算涉及的参数
     * @param derateValue 减免值: 减免期数
     * @return {@link FeeTypeDTO}
     */
    private FeeTypeDTO breaksTerm(FeeTypeDTO feeType, BigDecimal derateValue) {
        int term = feeType.getTerm().intValue();
        if (derateValue == null) {
            logger.error("Derate value cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_DERATE_VALUE_NOT_NULL_FAULT);
        }

        BigDecimal[] array = new BigDecimal[derateValue.compareTo(feeType.getTerm()) >= 0 ? term : derateValue.intValue()];

        for (int i = 0,len = array.length; i < len; i++) {
            array[i] = feeType.getEachTermFeeAmount()[i];
        }

        //减免期数
        feeType.setDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //减免费用金额
        feeType.setDerateFeeAmount(array);
        return feeType;
    }

    /**
     * 减免金额
     * @param feeType 手续费计算涉及的参数
     * @param derateValue 分期减免值
     * @return {@link FeeTypeDTO}
     */
    public FeeTypeDTO breaksAmount(FeeTypeDTO feeType, BigDecimal derateValue) {
        //总费用
        BigDecimal totalCost = feeType.getTotalCost();

        if (derateValue == null) {
            logger.error("Derate value cannot be empty");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_DERATE_VALUE_NOT_NULL_FAULT);
        }

        BigDecimal[] array = new BigDecimal[feeType.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        BigDecimal[] eachTermFeeAmount = feeType.getEachTermFeeAmount();

        BigDecimal temp = new BigDecimal(derateValue.toString());

        for (int i = 0,len = eachTermFeeAmount.length; i < len; i++) {

            if (temp.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }

            if (eachTermFeeAmount[i].compareTo(BigDecimal.ZERO) == 0){
                array[i] = BigDecimal.ZERO;
            }else if (eachTermFeeAmount[i].compareTo(temp) >= 0){
                array[i] = temp;
                temp = temp.subtract(eachTermFeeAmount[i]);
            }else {
                array[i] = eachTermFeeAmount[i];
                temp = temp.subtract(eachTermFeeAmount[i]);
            }
        }

        //减免期数 = 减免金额 > 0 的期数
        feeType.setDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //减免费用金额
        feeType.setDerateFeeAmount(array);
        //分期费用金额
        feeType.setTotalCost(totalCost);
        return feeType;



        //单期费用大于等于减免费用,只需要在第一期设置减免金额
        /*if (eachCost.compareTo(derateValue) >= 0) {
            costList.add(derateValue);
        }else {
            //单期费用小于减免费用，需要考虑减免期数以及减免余额
            BigDecimal temp = new BigDecimal("0.00");
            //减免期数
            int term = termbd.intValue();
            int i = 1;
            BigDecimal sub;
            //合计已经超过减免值
            while (true) {
                sub = derateValue.subtract(temp);
                temp = temp.add(eachCost);
                //最多减免i期
                if (i > term || temp.compareTo(derateValue) > 0) {
                    break;
                }
                costList.add(eachCost);
                i++;
            }

            if (temp.compareTo(derateValue) > 0 && costList.size() < term) {
                costList.add(sub);
            }
        }*/


    }

    /**
     * 每期折扣
     *
     * @param feeType 手续费计算涉及的参数
     * @param derateValue 分期减免值
     * @return {@link FeeTypeDTO}
     */
    private FeeTypeDTO eachDiscount(FeeTypeDTO feeType, BigDecimal derateValue) {

        BigDecimal[] array = new BigDecimal[feeType.getTerm().intValue()];
        Arrays.fill(array,BigDecimal.ZERO);

        BigDecimal[] eachTermFeeAmount = feeType.getEachTermFeeAmount();

        for (int i = 0, len = eachTermFeeAmount.length; i < len; i++) {
            array[i] = eachTermFeeAmount[i].multiply(derateValue).divide(hundred, 4,BigDecimal.ROUND_DOWN);
        }

        feeType.setDerateTerm((int) Arrays.stream(array)
                .filter(t -> t.compareTo(BigDecimal.ZERO) > 0).count());
        //减免费用金额
        feeType.setDerateFeeAmount(array);
        return feeType;


       /* BigDecimal totalCost = feeType.getTotalCost();
        String feeFlag = feeType.getFeeFlag();
        BigDecimal firstPayment = BigDecimal.ZERO;
        BigDecimal eashPayment = BigDecimal.ZERO;
        totalCost = totalCost.;
        if (FeeReceiveFagEnum.ONE_TIME.getCode().equals(feeFlag)) {
            //首期手续费
            firstPayment = totalCost;
            //每期手续费
            eashPayment = BigDecimal.ZERO;
        }
        if (FeeReceiveFagEnum.INSTALMENT.getCode().equals(feeFlag)) {
            eashPayment = totalCost.divide(feeType.getTerm(), RoundingMode.HALF_UP);
            firstPayment = totalCost.subtract(eashPayment.multiply(feeType.getTerm().subtract(BigDecimal.ONE)));
        }
        //分期费用金额
        feeType.setTotalCost(totalCost);
        //首期费用金额
        feeType.setFirstPayment(firstPayment);
        //每期费用金额
        feeType.setEashPayment(eashPayment);*/
        //减免期数

    }

    private InstallFeeCodeInfoResDTO getInstallFeeCodeByOrgNumAndFeeCode(String organizationNumber, String feeCode) {
        logger.info("Calling installFeeCodeInfoService.getByIndex: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getByIndex(organizationNumber, feeCode);
        logger.info("installFeeCodeInfoService.getByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null){
            logger.error("Failed to query installment fee parameters by organization number and fee code: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        return result;
    }

    /**
     *  gst金额 = 分期费用 * gst费用百分比
     *
     * @param feeCost 分期费用
     * @param installFeeCode 分期费用代码表
     * @return gst费用金额
     */
    private BigDecimal getGstFeeCost(BigDecimal feeCost,InstallFeeCodeInfoResDTO installFeeCode){

        if (installFeeCode.getGstInterestRate() == null){
            return BigDecimal.ZERO;
        }

        return feeCost.multiply(installFeeCode.getGstInterestRate()).divide(hundred,2,BigDecimal.ROUND_DOWN);
    }


}
