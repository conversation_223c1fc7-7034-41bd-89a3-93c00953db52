package com.anytech.anytxn.accounting.batch.job.glvchersum.config;

import com.anytech.anytxn.accounting.batch.job.glvchersum.partitioner.VoucherSumPartitioner;
import com.anytech.anytxn.accounting.batch.job.glvchersum.steps.GlvcherSumTasklat2;
import com.anytech.anytxn.accounting.batch.job.glvchersum.steps.GlvcherSumWriter;
import com.anytech.anytxn.accounting.batch.job.glvchersum.steps.GlvcherSumProcessor;
import com.anytech.anytxn.accounting.batch.job.glvchersum.steps.GlvcherSumReader;
import com.anytech.anytxn.common.core.constants.PropsConstant;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.partition.PartitionHandler;
import org.springframework.batch.core.partition.support.TaskExecutorPartitionHandler;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/4/27
 * 会计传票汇总任务
 */
@Configuration
@Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
public class GlvcherSumJobConfig {

    private static final Logger logger = LoggerFactory.getLogger(GlvcherSumJobConfig.class);

    @Resource
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private JobBuilderFactory jobBuilderFactory;

    @Autowired
    private StepBuilderFactory stepBuilderFactory;


    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.glvcherSumJob.amsToVoucherStep.chunk-limit:500}")
    private Integer chunkLimit;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.glvcherSumJob.amsToVoucherStep.grid-size:10000}")
    private Integer pageSize;

    @Value("${" + PropsConstant.PROPS_PREFIX + ".batch.glvcherSumJob.amsToVoucherPartition.grid-size:15}")
    private Integer masterVoucherSumBatchGridSize;

    @Bean
    public Job glvcherSumJob(@Qualifier("masterVoucherSumBatchStep") Step masterVoucherSumBatchStep,
                             @Qualifier("glvcherSumStep2") Step glvcherSumStep2) {
        return jobBuilderFactory.get("glvcherSumJob")
                .start(masterVoucherSumBatchStep)
                .next(glvcherSumStep2)
                .build();
    }

    @Bean
    public Step glvcherSumStep(@Qualifier("glvcherSumReader") GlvcherSumReader glvcherSumReader,
                                 @Qualifier("glvcherSumProcessor") GlvcherSumProcessor glvcherSumProcessor,
                                 @Qualifier("glvcherSumWriter") GlvcherSumWriter glvcherSumWriter) {
        return stepBuilderFactory.get("glvcherSumStep").<AccountantGlvcher, AccountantGlvcher>chunk(chunkLimit)
                .reader(glvcherSumReader)
                .processor(glvcherSumProcessor)
                .writer(glvcherSumWriter)
                .build();
    }

    @Bean
    public Step masterVoucherSumBatchStep(@Qualifier("glvcherSumStep") Step glvcherSumStep,
                                       @Qualifier("voucherSumPartitioner") VoucherSumPartitioner voucherSumPartitioner) {
        return stepBuilderFactory.get("masterVoucherSumBatchStep")
                .partitioner(glvcherSumStep.getName(), voucherSumPartitioner)
                .partitionHandler(voucherBatchMasterSlaveHandler(glvcherSumStep))
                .build();
    }

    /*@Bean
    public Step glvcherSumStep() {
        return stepBuilderFactory.get("glvcherSumStep")
                .tasklet(glvcherSumTasklet())
                .allowStartIfComplete(true)
                .build();
    }*/

    @Bean
    public Step glvcherSumStep2() {
        return stepBuilderFactory.get("glvcherSumStep2")
                .tasklet(glvcherSumTasklet2())
                .allowStartIfComplete(true)
                .build();
    }

   /* @Bean("glvcherSumTasklet")
    @StepScope
    public Tasklet glvcherSumTasklet(){
        return new GlvcherSumTasklat();
    }*/

    @Bean("glvcherSumTasklet2")
    @StepScope
    public Tasklet glvcherSumTasklet2(){
        return new GlvcherSumTasklat2();
    }

    @Bean
    @StepScope
    public VoucherSumPartitioner voucherSumPartitioner() {
        return new VoucherSumPartitioner();
    }


    @Bean
    @StepScope
    public GlvcherSumReader glvcherSumReader(@Qualifier("businessDataSource") DataSource dataSource,
                                             @Value("#{stepExecutionContext['fromId']}") String fromId,
                                             @Value("#{stepExecutionContext['endId']}") String endId) {
        logger.info("Calling organizationInfoService.findOrganizationInfo: orgNumber={}", OrgNumberUtils.getOrg());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(OrgNumberUtils.getOrg());
        logger.info("organizationInfoService.findOrganizationInfo completed: result={}", organizationInfo != null ? "found" : "not found");
        GlvcherSumReader reader = new GlvcherSumReader(dataSource,organizationInfo,fromId,endId);
        reader.setDataSource(dataSource);
        // 设置页码大小
        reader.setPageSize(pageSize);
        Map<String, Object> parameters = new HashMap<>(4);
        parameters.put("fromId", fromId);
        parameters.put("endId", endId);
        reader.setParameterValues(parameters);
        // 记录断点
        reader.setSaveState(true);
        return reader;
    }

    @Bean
    @StepScope
    public GlvcherSumProcessor glvcherSumProcessor() {
        return new GlvcherSumProcessor();
    }

    @Bean
    @StepScope
    public GlvcherSumWriter glvcherSumWriter() {
        return new GlvcherSumWriter();
    }

    /**
     * 生成传票任务分区处理器
     * @param step
     * @return
     */
    private PartitionHandler voucherBatchMasterSlaveHandler(Step step) {
        TaskExecutorPartitionHandler handler = new TaskExecutorPartitionHandler();
        handler.setGridSize(masterVoucherSumBatchGridSize);
        handler.setTaskExecutor(taskExecutor());
        handler.setStep(step);
        try {
            handler.afterPropertiesSet();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return handler;
    }

    public TaskExecutor taskExecutor() {
        return new SimpleAsyncTaskExecutor("spring_batch");
    }

}
