<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>anytxn-file-manager</artifactId>
        <groupId>com.anytech</groupId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-file-manager-server</artifactId>

    <dependencies>

        <!-- 内部模块引用START -->
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-file-manager-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.anytech</groupId>
            <artifactId>anytxn-common-sharding</artifactId>
        </dependency>
        <!-- 内部模块引用END -->
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc</artifactId>
            <version>${shardingsphere.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>shardingsphere-test-util</artifactId>
                    <groupId>org.apache.shardingsphere</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 去掉tomcat容器使用undertow -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.shardingsphere</groupId>-->
        <!--            <artifactId>shardingsphere-cluster-mode-repository-nacos</artifactId>-->
        <!--            <version>5.2.1</version>-->
        <!--        </dependency>-->
        <!-- 缓存Start -->
        <!--        <dependency>-->
        <!--            <groupId>com.alicp.jetcache</groupId>-->
        <!--            <artifactId>jetcache-anno</artifactId>-->
        <!--            <version>2.5.16</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.alicp.jetcache</groupId>-->
        <!--            <artifactId>jetcache-autoconfigure</artifactId>-->
        <!--            <version>2.5.16</version>-->
        <!--        </dependency>-->
        <!-- 缓存End -->

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>2.3.2</version> <!-- 推荐版本 -->
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <!--outputDirectory>output</outputDirectory-->
                            <descriptors>
                                <descriptor>src/assembly/package.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-docker</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <skipAssembly>false</skipAssembly>
                            <finalName>${project.artifactId}</finalName>
                            <descriptors>
                                <descriptor>src/assembly/package-docker.xml</descriptor> <!-- Assembly 描述符文件 -->
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>

            </plugin>
        </plugins>
    </build>
</project>