package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.common.service.PartitionKeyInitService;
import com.anytech.anytxn.business.dao.transaction.mapper.SettlementLogMapper;
import com.anytech.anytxn.business.dao.transaction.model.SettlementLog;
import com.anytech.anytxn.common.core.enums.TransactionSourceEnum;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.installment.base.constants.InstallmentConstant;
import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.enums.*;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.business.base.transaction.domain.dto.SettlementLogDTO;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.installment.base.service.IInstallNormalAccountingService;
import com.anytech.anytxn.installment.base.service.IInstallProductEnquiryService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallAccountingTransParmService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallFeeCodeInfoService;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.transaction.base.enums.ReverseFeeIndicatorEnum;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分期正常入账
 *
 * <AUTHOR>
 * @date 2019-06-03
 **/
@Service
public class InstallNormalAccountingServiceImpl implements IInstallNormalAccountingService {

    private static final Logger logger = LoggerFactory.getLogger(InstallNormalAccountingServiceImpl.class);

    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private IInstallProductEnquiryService installProductEnquiryService;
    @Autowired
    private IOrganizationInfoService organizationInfoService;

    @Autowired
    private ITransactionCodeService transactionCodeService;

    @Autowired
    private IInstallAccountingTransParmService installAccountingTransParmService;
    @Autowired
    private IInstallProductInfoService installProductInfoService;

    @Autowired
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private SettlementLogMapper settlementLogMapper;
    @Autowired
    private IInstallFeeCodeInfoService installFeeCodeInfoService;

    @Autowired
    private IGlAmsService glAmsService;
    @Resource
    private PartitionKeyInitService partitionKeyInitService;

    /**
     * 分期正常入账
     *
     * @param installOrderDTO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnInstallException.class)
    public Boolean installNormalAccounting(InstallOrderDTO installOrderDTO) {
        logger.info("Installment normal accounting started, orderId: [{}]", installOrderDTO.getOrderId());
        if (Objects.equals(InstallmentOrderStatusEnum.NORMAL_STATUS.getCode(), installOrderDTO.getStatus())) {
            List<SettlementLogDTO> settlementLogList = installOrderNormalAccounting(installOrderDTO, false);
//            settlementLogService.addBatch(settlementLogList);
            List<SettlementLog> list = settlementLogList.stream().map(x -> {
                x.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());

                if (x.getTxnTransactionAmount() == null) {
                    x.setTxnTransactionAmount(BigDecimal.ZERO);
                }

                if (x.getTxnBillingAmount() == null) {
                    x.setTxnBillingAmount(BigDecimal.ZERO);
                }

                if (x.getTxnSettlementAmount() == null) {
                    x.setTxnSettlementAmount(BigDecimal.ZERO);
                }

                if (x.getTxnExchangeRate() == null) {
                    x.setTxnExchangeRate(BigDecimal.ZERO);
                }

                if (x.getTxnOutstandingAmount() == null) {
                    x.setTxnOutstandingAmount(BigDecimal.ZERO);
                }

                if (x.getPartitionKey() == null) {
                    x.setPartitionKey(0);
                }

                if (x.getTxnInstallmentTotalFee() == null) {
                    x.setTxnInstallmentTotalFee(BigDecimal.ZERO);
                }

                if (x.getTxnInstallmentFeeRate() == null) {
                    x.setTxnInstallmentFeeRate(BigDecimal.ZERO);
                }

                if (x.getTxnInstallmentDerateValue() == null) {
                    x.setTxnInstallmentDerateValue(BigDecimal.ZERO);
                }

                return BeanMapping.copy(x, SettlementLog.class);
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(settlementLogList)) {
                int i = settlementLogMapper.insertBatch(list);
                if (i < list.size()) {
                    logger.error("Failed to add settlement log table for orderId: {}", installOrderDTO.getOrderId());
                }
            }

            logger.info("Installment normal accounting completed, orderId: [{}]", installOrderDTO.getOrderId());
        }
        return true;
    }

    /**
     * @param installOrderDTO    分期订单数据
     * @param isFirstAutoInstall 是否是第一期自动分期抛账
     * @return List<SettlementLogDTO>
     */
    public List<SettlementLogDTO> installOrderNormalAccounting(InstallOrderDTO installOrderDTO, boolean isFirstAutoInstall) {
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", installOrderDTO.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(installOrderDTO.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}", organizationInfo != null ? organizationInfo.getOrganizationNumber() : null);
        if (organizationInfo == null) {
            logger.error("Organization parameters do not exist, organizationNumber: {}", installOrderDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ORG_INFO_PARAM_NOT_EXIST_FAULT);

        }
        LocalDate nextProcessingDay = organizationInfo.getNextProcessingDay();
        LocalDate today = organizationInfo.getToday();
        //如果是自动账单分期的第一期抛账
        LocalDate endSelectDay = isFirstAutoInstall ? today : nextProcessingDay;
        LocalDate lastProcessingDay = organizationInfo.getLastProcessingDay();
        //下账日期小于等于系统下一处理日并且大于系统上一处理日，且还未下账
        //如果是自动账单分期的第一期抛账,下账日期小于等于当前处理日并且大于系统上一处理日，且还未下账
        logger.info("endSelectDay is {}, lastProcessingDay is {}", endSelectDay, lastProcessingDay);
        List<InstallPlan> installPlanList = installPlanSelfMapper.selectNotAccounting(installOrderDTO.getOrderId(), "N",
                installOrderDTO.getOrganizationNumber(), lastProcessingDay, endSelectDay);
        List<InstallPlanDTO> installPlans = BeanMapping.copyList(installPlanList, InstallPlanDTO.class);
        // 批量检查计划
        if (CustAccountBO.isBatch()) {
            List<InstallPlanDTO> installPlanDtoS = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(installPlans, installOrderDTO.getOrderId());
            // 缓存数据重新判断，防止新插入的数据不符合题哦键
            installPlans = installPlanDtoS.stream().filter(x -> x.getOrderId().equals(installOrderDTO.getOrderId()) && "N".equals(x.getTermStutus()) && x.getTermPostDate().isAfter(lastProcessingDay) && !x.getTermPostDate().isAfter(endSelectDay)).collect(Collectors.toList());
        }

        Integer postedTermCount = 0;
        Integer postedFeeTermCount = 0;
        BigDecimal termAmount = BigDecimal.ZERO;
        BigDecimal feeAmount = BigDecimal.ZERO;
        BigDecimal derateAmount = BigDecimal.ZERO;
        BigDecimal principalDerateAmount = BigDecimal.ZERO;


        List<SettlementLogDTO> settlementLogList = new ArrayList<>();
        Set<Integer> termSet = new HashSet<>();
        //参数表集合
        logger.info("Calling installProductEnquiryService.installmentProductEnquiry: organizationNumber={}, productCode={}", installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
        InstallParameterDTO installTransEntrys = installProductEnquiryService.installmentProductEnquiry(
                installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
        logger.info("installProductEnquiryService.installmentProductEnquiry completed: productCode={}", installTransEntrys != null ? installOrderDTO.getProductCode() : null);

        InstallFeeCodeInfoResDTO installFeeCodeInfoResDTO = getInstallFeeCodeInfoResDTO(installOrderDTO);

        for (InstallPlanDTO installPlan : installPlans) {

            //F-固定利率 T-Loan定期贷款 抛账合并
            if (StringUtils.equalsAny(installFeeCodeInfoResDTO.getChargeOption(),
                    ChargeOptionEnum.T_TERM.getCode(), ChargeOptionEnum.FLAT_RATE.getCode())){
                //费用减免 + 本金减免
                BigDecimal sumDerateAmount = installPlan.getDerateAmount().add(Optional.ofNullable(installPlan.getPrincipalDerateFeeAmount()).orElse(BigDecimal.ZERO));
                //计算合并 : 本金金额 + 费用金额 - 费用减免 - 本金减免
                BigDecimal accountAmount = installPlan.getTermAmount().add(Optional.ofNullable(installPlan.getFeeAmount()).orElse(BigDecimal.ZERO))
                        .subtract(sumDerateAmount);

                if (accountAmount.compareTo(BigDecimal.ZERO) > 0){
                    //本金金额
                    termAmount = termAmount.add(installPlan.getTermAmount());
                    feeAmount = feeAmount.add(installPlan.getFeeAmount());

                    //入账金额
                    installPlan.setTermAmount(accountAmount);
                    // 构建settlementLog
                    SettlementLogDTO settlementLog = buildSettlementLog(installOrderDTO, installPlan,
                            InstallmentConstant.PRINCIPAL_ACCOUNT, nextProcessingDay);
                    // 修改settlementLog资产
                    editSettlementLogAbsForPrincipal(installPlan, settlementLog);
                    settlementLogList.add(settlementLog);
                    postedTermCount++;

                    termSet.add(installPlan.getTerm());
                }
            }else {
                //	本金下账：如果分期本金（term_amount）大于0，分期本金下账
                if (installPlan.getTermAmount() != null && installPlan.getTermAmount().compareTo(BigDecimal.ZERO) > 0) {
                    // 构建settlementLog
                    SettlementLogDTO settlementLog = buildSettlementLog(installOrderDTO, installPlan,
                            InstallmentConstant.PRINCIPAL_ACCOUNT, nextProcessingDay);
                    // 修改settlementLog资产
                    editSettlementLogAbsForPrincipal(installPlan, settlementLog);
                    settlementLogList.add(settlementLog);
                    postedTermCount++;
                    termAmount = termAmount.add(installPlan.getTermAmount());
                    termSet.add(installPlan.getTerm());
                }

                // 本金减免 :如果本金减免金额 PRINCIPAL_DERATE_FEE_AMOUNT 大于0, 本金减免下账
                if (installPlan.getPrincipalDerateFeeAmount() != null && installPlan.getPrincipalDerateFeeAmount().compareTo(BigDecimal.ZERO) > 0){
                    SettlementLogDTO settlementLog = buildSettlementLog(installOrderDTO, installPlan,
                            InstallmentConstant.PRINCIPAL_FEE_FREE_ACCOUNT, nextProcessingDay);
                    editSettlementLogAbsForFee(installPlan, settlementLog, installTransEntrys);
                    settlementLogList.add(settlementLog);
                    principalDerateAmount = principalDerateAmount.add(installPlan.getPrincipalDerateFeeAmount());
                    termSet.add(installPlan.getTerm());
                }


                //	分期手续费下账：如果分期费用（fee_amount）大于0，分期手续费下账
                if (installPlan.getFeeAmount() != null && installPlan.getFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    SettlementLogDTO settlementLog = buildSettlementLog(installOrderDTO, installPlan,
                            InstallmentConstant.FEE_ACCOUNT, nextProcessingDay);
                    editSettlementLogAbsForFee(installPlan, settlementLog, installTransEntrys);
                    settlementLogList.add(settlementLog);
                    postedFeeTermCount++;
                    feeAmount = feeAmount.add(installPlan.getFeeAmount());
                    termSet.add(installPlan.getTerm());
                }
                //	分期手续费减免：如果分期费用减免金额（DERATE_AMOUNT）大于0，分期手续费减免下账
                if (installPlan.getDerateAmount() != null && installPlan.getDerateAmount().compareTo(BigDecimal.ZERO) > 0) {
                    SettlementLogDTO settlementLog = buildSettlementLog(installOrderDTO, installPlan,
                            InstallmentConstant.FEE_FREE_ACCOUNT, nextProcessingDay);
                    editSettlementLogAbsForFee(installPlan, settlementLog, installTransEntrys);
                    settlementLogList.add(settlementLog);
                    derateAmount = derateAmount.add(installPlan.getDerateAmount());
                    termSet.add(installPlan.getTerm());
                }

            }

            //光大POC追加会计
            //分期每期抛账时，如果AMORTIZE_FEE金额大于0，需要生成一笔会计流水，写入会计流水表中
            if (installPlan.getAmortizeInterest() != null && installPlan.getAmortizeInterest().compareTo(BigDecimal.ZERO) > 0) {
                if (installTransEntrys != null) {
                    InstallAccountingTransParmResDTO accountTran = installTransEntrys.getInstallAccountTran();
                    String code = accountTran.getInterestAmortizeTransactionCode();
                    String accountId = installOrderDTO.getAccountManagementId();
                    AccountManagementInfoDTO accountManagementInfoDTO = getManagementById(accountId);
                    carryOver(accountManagementInfoDTO, installOrderDTO, installPlan, code);
                }
            }

        }

        Integer postedTerm = installOrderDTO.getPostedTerm() + postedTermCount;
        Integer postedFeeTerm = installOrderDTO.getPostedFeeTerm() + postedFeeTermCount;
        if (postedTerm > installOrderDTO.getTerm()) {
            logger.error("Order plan table principal posting installment terms accumulated greater than order table principal installment terms, orderId: {}", installOrderDTO.getOrderId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_UPDATE_FAULT, InstallRepDetailEnum.CU_NU);
        }
        if (postedFeeTerm > installOrderDTO.getFeeTerm()) {
            logger.error("Order plan table fee posting installment terms accumulated greater than order table fee installment terms, orderId: {}", installOrderDTO.getOrderId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_UPDATE_FAULT, InstallRepDetailEnum.CU_NU2);
        }
        if (termSet.isEmpty()) {
            logger.info("Installment normal accounting completed, no accounting needed, orderId: [{}]", installOrderDTO.getOrderId());
            return Collections.emptyList();
        }
        // 更新计划
        updateInstallPlan(InstallmentIndicatorEnum.ALREADY_INSTALL.getCode(), installOrderDTO.getOrderId(), termSet);

        InstallOrder installOrder = new InstallOrder();
        if (CustAccountBO.isBatch()) {
            BeanMapping.copy(installOrderDTO, installOrder);
        }
        installOrder.setOrderId(installOrderDTO.getOrderId());
        installOrder.setPostedTerm(postedTerm);
        installOrder.setPostedFeeTerm(postedFeeTerm);
        installOrder.setUnpostedAmount(installOrderDTO.getUnpostedAmount().subtract(termAmount));
        installOrder.setUnpostedFeeAmount(installOrderDTO.getUnpostedFeeAmount().subtract(feeAmount));
        installOrder.setTotalReceivedFee(feeAmount.add(installOrderDTO.getTotalReceivedFee() == null ?
                BigDecimal.ZERO : installOrderDTO.getTotalReceivedFee()));
        installOrder.setTotalDerateFee(derateAmount.add(installOrderDTO.getTotalDerateFee() == null ?
                BigDecimal.ZERO : installOrderDTO.getTotalDerateFee()));

        installOrder.setPrincipalDerateAmount(principalDerateAmount.add(Optional.ofNullable(
                installOrder.getPrincipalDerateAmount()).orElse(BigDecimal.ZERO)));

        if (BigDecimal.ZERO.compareTo(installOrder.getUnpostedAmount()) == 0 && BigDecimal.ZERO.compareTo(installOrder.getUnpostedFeeAmount()) == 0) {
            installOrder.setStatus(InstallmentOrderStatusEnum.NORMALEND_STATUS.getCode());
        }
        installOrder.setLastMaintainDate(nextProcessingDay);
        installOrder.setUpdateTime(LocalDateTime.now());
        // 更新订单
        updateInstallOrder(installOrder);
        return settlementLogList;
    }


    /**
     * 结转处理
     *
     * @param managementInfoDTO 管理账户信息
     */
    private void carryOver(AccountManagementInfoDTO managementInfoDTO, InstallOrderDTO installOrderDTO,
                           InstallPlanDTO installPlan, String txnCode) {
        AccountantGlamsDTO accountantGlamsDTO = new AccountantGlamsDTO();
        accountantGlamsDTO.setAccountManagementId(managementInfoDTO.getAccountManagementId());
        accountantGlamsDTO.setAcctLogo(managementInfoDTO.getProductNumber());
        accountantGlamsDTO.setCrdNumber(installOrderDTO.getCardNumber());
        accountantGlamsDTO.setPostingDate(installPlan.getTermPostDate());
        accountantGlamsDTO.setTxnSource(TransactionSourceEnum.LOCAL.getCode());
        accountantGlamsDTO.setTxnInd("4");
        accountantGlamsDTO.setCurrencyRate("1");


        accountantGlamsDTO.setTxnAmt(installPlan.getAmortizeInterest());
        accountantGlamsDTO.setTxnCurrency(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setPostingAmt(installPlan.getAmortizeInterest());
        accountantGlamsDTO.setPostingCurrencyCode(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setSettleAmt(installPlan.getAmortizeInterest());
        accountantGlamsDTO.setSettleCurrency(installOrderDTO.getInstallmentCcy());

        accountantGlamsDTO.setTxnCode(txnCode);
        accountantGlamsDTO.setTxnCodeOrig(txnCode);

        logger.info("Calling glAmsService.buildInstallmentGlAms: accountManagementId={}, txnCode={}", accountantGlamsDTO.getAccountManagementId(), txnCode);
        glAmsService.buildInstallmentGlAms(accountantGlamsDTO,installOrderDTO);
        logger.info("glAmsService.buildInstallmentGlAms completed");
    }


    private InstallFeeCodeInfoResDTO getInstallFeeCodeInfoResDTO(InstallOrderDTO installOrderDTO){
        InstallProductInfoResDTO installProConf = getInstallProByOrgAndCode(installOrderDTO.getOrganizationNumber(),
                installOrderDTO.getProductCode());

        //分期费用代码
        return getInstallFeeCodeByOrgNumAndFeeCode(installOrderDTO.getOrganizationNumber()
                , installProConf.getFeeCodeId());
    }

    /**
     * @param installPlan 分期订单
     * @return SettlementLog 清算流水
     */
    private SettlementLogDTO buildSettlementLog(InstallOrderDTO installOrderDTO, InstallPlanDTO installPlan, String flag,
                                                LocalDate nextProcessingDay) {
        SettlementLogDTO settlementLog = new SettlementLogDTO();
        //入账方式,1=批量入账
        //因为批量入账移到日切前了,需要赋值入账方式为实时入账
        settlementLog.setTxnPostMethod(PostMethodEnum.REAL_TIME.getCode());
        //拒绝重入账标志，0=普通交易
        settlementLog.setTxnRepostFromSuspend(RepostFromSuspendEnum.NORMAL_TRANS.getCode());
        //冲减交易费用标识，1=否
        settlementLog.setTxnReverseFeeIndicator(ReverseFeeIndicatorEnum.NO.getCode());
        //交易来源,1=本行外部输入
        settlementLog.setTxnTransactionSource(TransactionSourceEnum.LOCAL.getCode());
        //授权匹配标志,0=未匹配授
        settlementLog.setTxnAuthMatchIndicator(AuthMatchIndicatorEnum.NOT_MATCH_AUTH.getCode());
        //是否恢复授权占用额度标志,Y=需要
        settlementLog.setTxnReleaseAuthAmount(ReleaseAuthAmountEnum.RECOVER.getCode());
        //分期期数
        settlementLog.setTxnInstallmentTerm(String.valueOf(installPlan.getTerm()));
        //交易币种
        settlementLog.setTxnTransactionCurrency(installOrderDTO.getInstallmentCcy());
        //入账币种
        settlementLog.setTxnBillingCurrency(installOrderDTO.getInstallmentCcy());
        //清算币种
        settlementLog.setTxnSettlementCurrency(installOrderDTO.getInstallmentCcy());
        //0-本金入账,1手续费入账,2分期手续费减免,3本金减免入账
        InstallProductInfoResDTO installProConf = getInstallProByOrgAndCode(installOrderDTO.getOrganizationNumber(),
                installOrderDTO.getProductCode());
        String postingTransactionParmId = installProConf.getPostingTransactionParmId();
        InstallAccountingTransParmResDTO installAccountTranConf = getInstallAccountTranByOrgNumAndTableId(installOrderDTO.getOrganizationNumber(),
                postingTransactionParmId);
        //分期费用代码
        InstallFeeCodeInfoResDTO installFeeCode = getInstallFeeCodeByOrgNumAndFeeCode(installOrderDTO.getOrganizationNumber()
                , installProConf.getFeeCodeId());
        settlementLog.setTxnTransactionDescription(installOrderDTO.getTransactionDesc());
        if (InstallmentConstant.PRINCIPAL_ACCOUNT.equals(flag)) {
            //MCC
            settlementLog.setTxnMerchantCategoryCode(installOrderDTO.getMcc());
            //交易金额
            settlementLog.setTxnTransactionAmount(installPlan.getTermAmount());
            //入账金额
            settlementLog.setTxnBillingAmount(installPlan.getTermAmount());
            //清算金额
            settlementLog.setTxnSettlementAmount(installPlan.getTermAmount());
            String transactionCode = installAccountTranConf.getPrincipalTransactionCode();
            //交易码
            settlementLog.setTxnTransactionCode(transactionCode);
            //分期单笔
            settlementLog.setTxnInstallmentIndicator("2");
        } else if (InstallmentConstant.FEE_ACCOUNT.equals(flag)) {
            //MCC
            settlementLog.setTxnMerchantCategoryCode("0");
            //交易金额
            settlementLog.setTxnTransactionAmount(installPlan.getFeeAmount());
            //入账金额
            settlementLog.setTxnBillingAmount(installPlan.getFeeAmount());
            //清算金额
            settlementLog.setTxnSettlementAmount(installPlan.getFeeAmount());
            String transactionCode = installAccountTranConf.getFeeTransactionCode();
            //交易码
            settlementLog.setTxnTransactionCode(transactionCode);
            settlementLog.setTxnTransactionDescription(settlementLog.getTxnTransactionDescription() + " Processing Fee");

        } else if (InstallmentConstant.FEE_FREE_ACCOUNT.equals(flag)) {
            //MCC
            settlementLog.setTxnMerchantCategoryCode("0");
            //交易金额
            settlementLog.setTxnTransactionAmount(installPlan.getDerateAmount());
            //入账金额
            settlementLog.setTxnBillingAmount(installPlan.getDerateAmount());
            //清算金额
            settlementLog.setTxnSettlementAmount(installPlan.getDerateAmount());
            String transactionCode = installAccountTranConf.getFeeReversalTransactionCode();
            //交易码
            settlementLog.setTxnTransactionCode(transactionCode);

        }else if (InstallmentConstant.PRINCIPAL_FEE_FREE_ACCOUNT.equals(flag)){
            //MCC
            settlementLog.setTxnMerchantCategoryCode("0");
            //交易金额
            settlementLog.setTxnTransactionAmount(installPlan.getPrincipalDerateFeeAmount());
            //入账金额
            settlementLog.setTxnBillingAmount(installPlan.getPrincipalDerateFeeAmount());
            //清算金额
            settlementLog.setTxnSettlementAmount(installPlan.getPrincipalDerateFeeAmount());
            //本金撤销产生的本金撤销交易码
            String transactionCode = installAccountTranConf.getPrincipalReversalTransCode();
            //交易码
            settlementLog.setTxnTransactionCode(transactionCode);
        }

        //卡号
        settlementLog.setTxnCardNumber(installOrderDTO.getCardNumber());
        //管理账号
        settlementLog.setTxnAccountManageId(installOrderDTO.getAccountManagementId());
        //参考号
        settlementLog.setTxnReferenceNumber(installOrderDTO.getAcquireReferenceNo());
        //分期订单号
        settlementLog.setTxnInstallmentOrderId(String.valueOf(installPlan.getOrderId()));
        //交易日期
        settlementLog.setTxnTransactionDate(installOrderDTO.getTransactionDate());
        //入账日期
        settlementLog.setTxnBillingDate(nextProcessingDay);
        //商户号
        settlementLog.setTxnMerchantId(installOrderDTO.getMerchantId());
        //授权额度占用金额
        settlementLog.setTxnOutstandingAmount(BigDecimal.ZERO);
        //汇率
        settlementLog.setTxnExchangeRate(BigDecimal.ZERO);
        settlementLog.setTxnGlobalFlowNumber(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        settlementLog.setCreateTime(LocalDateTime.now());
        settlementLog.setUpdateTime(LocalDateTime.now());
        settlementLog.setUpdateBy("admin");
        settlementLog.setVersionNumber(1L);
        settlementLog.setTxnMerchantName(installOrderDTO.getMerchantName());
        settlementLog.setTxnReferenceNumber(installOrderDTO.getRetrievalReferenceNumber());

        String customerId = null;
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoMapper.selectByPrimaryKey(settlementLog.getTxnCardNumber(),settlementLog.getOrganizationNumber());
        if (InstallmentConstant.PRIMARY_INDICATOR.equals(cardAuthorizationInfo.getRelationshipIndicator())) {
            //主卡
            customerId = cardAuthorizationInfo.getPrimaryCustomerId();
        } else {
            //附卡
            customerId = cardAuthorizationInfo.getSupplementaryCustomerId();
        }
        settlementLog.setCustomerId(customerId);

        CardAuthorizationDTO cardAuthorizationDTO = BeanMapping.copy(cardAuthorizationInfo, CardAuthorizationDTO.class);
        int partitionKey = partitionKeyInitService.partitionKeyGenerator(cardAuthorizationDTO, null);
        settlementLog.setPartitionKey(partitionKey);


        return settlementLog;

    }

    /**
     * 本金入账会计相关字段的赋值
     *
     * @param installPlan
     * @param settlementLog
     */
    @Override
    public void editSettlementLogAbsForPrincipal(InstallPlanDTO installPlan, SettlementLogDTO settlementLog) {
        //光大POC
        if (installPlan.getAbsProductCode() == null || "N".equals(installPlan.getAbsStatus())) {
            settlementLog.setAbsStatus("N");
            settlementLog.setAbsProductCode(null);
            settlementLog.setBalFlag("0");
        } else {
            settlementLog.setAbsStatus(installPlan.getAbsStatus());
            settlementLog.setAbsProductCode(installPlan.getAbsProductCode());
            settlementLog.setBalFlag("1");
        }
    }

    /**
     * 费用入账,会计相关字段的赋值
     *
     * @param installPlan
     * @param settlementLog
     * @param installTransEntrys
     */
    @Override
    public void editSettlementLogAbsForFee(InstallPlanDTO installPlan,
                                           SettlementLogDTO settlementLog,
                                           InstallParameterDTO installTransEntrys) {
        String feeReceiveFlag = null;
        if (installTransEntrys != null) {
            feeReceiveFlag = installTransEntrys.getInstallProInfo().getFeeReceiveFlag();
        }
        /**
         * 如果本次下账分期计划的分期订单的手续费收取方式等于分期收取，并且该分期计划的资产包状态为A:已出表，并且资产包编号为非空
         *      该笔分期计划的abs状态和资产包编号，编辑到清算接口
         *      余额结转标志编辑为2：未科目结转
         */
        if ("I".equals(feeReceiveFlag) && "A".equals(installPlan.getAbsStatus()) && installPlan.getAbsProductCode() != null) {
            settlementLog.setAbsStatus(installPlan.getAbsStatus());
            settlementLog.setAbsProductCode(installPlan.getAbsProductCode());
            settlementLog.setBalFlag("2");
        } else {
            settlementLog.setAbsStatus("N");
            settlementLog.setBalFlag("0");
        }
    }

    private InstallProductInfoResDTO getInstallProByOrgAndCode(String organizationNumber, String productCode) {
        logger.info("Calling installProductInfoService.findByIndex: organizationNumber={}, productCode={}", organizationNumber, productCode);
        InstallProductInfoResDTO result = installProductInfoService.findByIndex(organizationNumber, productCode);
        logger.info("installProductInfoService.findByIndex completed: productCode={}", result != null ? result.getProductCode() : null);
        if (result == null) {
            logger.error("Failed to query installment product parameters by organization number and product code: organizationNumber={}, productCode={}", organizationNumber, productCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }

    private InstallFeeCodeInfoResDTO getInstallFeeCodeByOrgNumAndFeeCode(String organizationNumber, String feeCode) {
        logger.info("Calling installFeeCodeInfoService.getByIndex: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getByIndex(organizationNumber, feeCode);
        logger.info("installFeeCodeInfoService.getByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null){
            logger.error("Failed to query installment fee parameters by organization number and fee code: organizationNumber={}, feeCode={}", organizationNumber, feeCode);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_FEE_PRAM_NOT_EXIST_FAULT);
        }
        return result;
    }

    private InstallAccountingTransParmResDTO getInstallAccountTranByOrgNumAndTableId(String organizationNumber, String tableId) {
        logger.info("Calling installAccountingTransParmService.selectByIndex: organizationNumber={}, tableId={}", organizationNumber, tableId);
        InstallAccountingTransParmResDTO result = installAccountingTransParmService.selectByIndex(organizationNumber, tableId);
        logger.info("installAccountingTransParmService.selectByIndex completed: result={}", result != null ? "found" : "null");
        if (result == null) {
            logger.error("Failed to query installment accounting transaction parameters by organization number and table ID: organizationNumber={}, tableId={}", organizationNumber, tableId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ACCT_TRANS_PARAM_NOT_EXIST_FAULT);
        }
        return result;
    }


    private AccountManagementInfoDTO getManagementById(String managementId) {
        if (CustAccountBO.isBatch()) {
            return CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(managementId);
        } else {
            return BeanMapping.copy(accountManagementInfoMapper.selectByPrimaryKey(managementId), AccountManagementInfoDTO.class);
        }
    }

    private void updateInstallOrder(InstallOrder installOrder) {
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallOrder(BeanMapping.copy(installOrder, InstallOrderDTO.class));
        } else {
            int i = installOrderMapper.updateByPrimaryKeySelective(installOrder);
            if (i == 1) {
                logger.info("Installment normal posting completed, installment orderId: [{}]", installOrder.getOrderId());
            } else {
                logger.error("Installment normal posting failed to update installment order table [INSTALL_ORDER], orderId: [{}]", installOrder.getOrderId());
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT, InstallRepDetailEnum.NULL, installOrder.getOrderId());
            }
        }
    }

    private void updateInstallPlan(String termStutus, String orderId, Set<Integer> terms) {
        if (CustAccountBO.isBatch()) {
            List<InstallPlan> installPlanList = installPlanSelfMapper.selectPlansByOrderId(orderId);

            List<InstallPlanDTO> installPlans = CollectionUtils.isEmpty(installPlanList) ? Lists.newArrayList() : BeanMapping.copyList(installPlanList, InstallPlanDTO.class);
            // 检查缓存中的计划
            installPlans = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(installPlans, orderId);
            if (CollectionUtils.isNotEmpty(installPlans)) {
                installPlans.stream().filter(x -> terms.contains(x.getTerm())).forEach(x -> {
                    x.setTermStutus(termStutus);
                    CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallPlan(BeanMapping.copy(x, InstallPlanDTO.class));
                });
            }
        } else {
            int effectNum = installPlanSelfMapper.updateStatusByOrderIdAndTerm(termStutus, orderId, terms);
            if (effectNum != terms.size()) {
                logger.error("Normal posting: installment plan table update count does not match actual update count, expected: {}, actual: {}", terms.size(), effectNum);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PLAN_UPDATE_FAULT);
            }
        }
    }
}
