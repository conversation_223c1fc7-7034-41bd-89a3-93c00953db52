package com.anytech.anytxn.notification;

import com.anytech.anytxn.notification.config.ExecutorConfig;
import com.anytech.anytxn.business.dao.EnableBisCoreDao;
import com.anytech.anytxn.notification.config.NotificationConfig;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;

import java.lang.annotation.*;


/**
 * sms dao
 * <AUTHOR> @date 
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({EnableNotificationService.SmsDaoConfigurer.class, NotificationConfig.class, ExecutorConfig.class})
@EnableAutoConfiguration(exclude = RocketMQAutoConfiguration.class)
@EnableAsync
public @interface EnableNotificationService {

    @Configuration
    @EnableBisCoreDao
//    @MapperScan(basePackages={"com.anytech.anytxn.sms.mapper"},sqlSessionTemplateRef= BusinessDbConfiguration.SQLSESSIONTEMPLATE)
//    @MapperScan(basePackages={"com.anytech.anytxn.sms.cm.mapper"},sqlSessionTemplateRef= CommonDbConfiguration.SQLSESSIONTEMPLATE)
    @MapperScan(basePackages={"com.anytech.anytxn.notification.mapper"})
    @ComponentScan(basePackages = {"com.anytech.anytxn.notification.*"})
    @EnableFeignClients(basePackages = {"com.anytech.anytxn.notification.client"})
    class SmsDaoConfigurer {
    }
}
