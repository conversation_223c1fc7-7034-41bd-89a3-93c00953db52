package com.anytech.anytxn.file.service;

import com.anytech.anytxn.common.core.constants.MessageHandle;
import com.anytech.anytxn.common.core.constants.ShardingConstant;
import com.anytech.anytxn.common.core.utils.BaseContextHandler;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.file.config.FileMainConfig;
import com.anytech.anytxn.file.constants.FileConstant;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.domain.model.FileManagerScanProcess;
import com.anytech.anytxn.file.enums.ScanFileProcessStatusEnum;
import com.anytech.anytxn.file.mapper.FileManagerScanParamMapper;
import com.anytech.anytxn.file.mapper.FileManagerScanProcessMapper;
import com.anytech.anytxn.file.mapper.FileManagerScanProcessSelfMapper;
import com.anytech.anytxn.file.domain.model.FileManagerScanParam;
import com.anytech.anytxn.file.domain.model.FileManagerScanProcess;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @description: FileManagerScanProcess处理Service
 * @author: zhangnan
 * @create: 2021-03-25
 **/
@Service
public class FileManagerScanProcessService {

    private static final Logger logger = LoggerFactory.getLogger(FileManagerScanProcessService.class);

    @Autowired
    private FileManagerScanProcessMapper fileManagerScanProcessMapper;
    @Autowired
    private FileManagerScanParamMapper fileManagerScanParamMapper;
    @Autowired
    private FileManagerScanProcessSelfMapper fileManagerScanProcessSelfMapper;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    public FileManagerScanProcess selectByPrimaryKey(String id){
        return fileManagerScanProcessMapper.selectByPrimaryKey(id);
    }

    /**
     * 每次扫描更新结果
     *
     * @param message：提示结果信息
     */
    public void updateParamStatus(MessageHandle message, FileManagerScanParam param) {

        param.setLastScanResultDes(message.message(FileMainConfig.localLanguage));
        param.setLastScanTime(LocalDateTime.now());
        fileManagerScanParamMapper.updateScanStatus(param);
    }

    /**
     * 查询库中是否有重复的md5值
     * @param param
     * @param startDate
     * @param md5String
     * @param lastScanProcessId: 本次检查的文件id，为了排除已经插入的文件的自身的记录
     * @return
     */
    public FileManagerScanProcess selectByMD5(FileManagerScanParam param, LocalDate startDate, String md5String,String lastScanProcessId) {
        return fileManagerScanProcessSelfMapper.selectByMD5(
                md5String, startDate, param.getFileType(), lastScanProcessId, param.getOrganizationNumber());
    }

    /**
     * 执行调度后，插入一条记录
     */
    public FileManagerScanProcess insertOne(File file, String scheduleSequenceNumber,
                                            ScanFileProcessStatusEnum enums,
                                            FileManagerScanParam param,String md5String) {
        String tenantId = (String) BaseContextHandler.get(ShardingConstant.TENANT_ID);
        LocalDateTime now = LocalDateTime.now();

        FileManagerScanProcess fileManagerScanProcess = new FileManagerScanProcess();
        fileManagerScanProcess.setId(sequenceIdGen.generateId(tenantId));
        fileManagerScanProcess.setFileName(file.getName());
        fileManagerScanProcess.setFilePath(param.getScanPath());
        fileManagerScanProcess.setOrganizationNumber(OrgNumberUtils.getOrg());
        fileManagerScanProcess.setFileType(param.getFileType());
        fileManagerScanProcess.setScanParamId(param.getId());
        fileManagerScanProcess.setScanStatus(enums.getcode());
        fileManagerScanProcess.setScheduleSequenceNumber(scheduleSequenceNumber);
        fileManagerScanProcess.setTriggerTime(now);
        fileManagerScanProcess.setCreateTime(now);
        fileManagerScanProcess.setUpdateTime(now);
        fileManagerScanProcess.setUpdateBy(FileConstant.DEFAULT_USER);
        fileManagerScanProcess.setVersionNumber(1);
        fileManagerScanProcess.setOrganizationNumber(param.getOrganizationNumber());
        fileManagerScanProcess.setMd5(md5String);
        fileManagerScanProcessMapper.insertSelective(fileManagerScanProcess);
        return fileManagerScanProcess;

    }

    /**
     * 重新执行调度后，更新process表的状态
     * @param enums
     * @param fileManagerScanProcess
     */
    public void updateScanProcess(ScanFileProcessStatusEnum enums, FileManagerScanProcess fileManagerScanProcess) {
        fileManagerScanProcess.setScanStatus(enums.getcode());
        fileManagerScanProcess.setUpdateTime(LocalDateTime.now());
        fileManagerScanProcessMapper.updateByPrimaryKeySelective(fileManagerScanProcess);
    }
}
