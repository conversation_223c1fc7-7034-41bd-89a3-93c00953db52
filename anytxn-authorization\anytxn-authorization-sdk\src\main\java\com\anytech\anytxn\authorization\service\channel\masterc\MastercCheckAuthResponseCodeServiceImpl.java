package com.anytech.anytxn.authorization.service.channel.masterc;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.service.auth.IAuthPrePostLogService;
import com.anytech.anytxn.authorization.service.auth.AuthPrePostInfoModifyService;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.base.domain.dto.TransVelocityStatisticsDTO;
import com.anytech.anytxn.authorization.mapper.transvelocitystatistics.TransVelocityStatisticsSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.TransVelocityStatistics;
import com.anytech.anytxn.authorization.base.service.auth.IAuthMatchRuleService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.service.batch.TransVelocityStatisticsJdbcService;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.authorization.service.channel.upi.UpiAuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.authorization.service.channel.upi.preauth.UpiPreAuthDataUpdateServiceImpl;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.authorization.base.service.transaction.ITransVelocityLogService;
import com.anytech.anytxn.authorization.base.service.transaction.ITransVelocityStatisticsService;
import com.anytech.anytxn.authorization.base.utils.DateUtils;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.TransVelocityLogDTO;
import com.anytech.anytxn.business.base.card.constants.CardBusinessConstant;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceCodeEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.card.service.ICardMdesNotificationService;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.customer.domain.dto.BlockCodeMaintenanceLogDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.BlockCodeMaintenanceLogMapper;
import com.anytech.anytxn.business.dao.customer.model.BlockCodeMaintenanceLog;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitTrialResDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.common.enums.ServerTypeEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mastercard
 * @ClassName AuthDataUpdate
 * @Description 未并账交易数据更新
 * <AUTHOR>
 * @date  2018/12/14 4:39 PM
 * Version 1.0
 **/
@Service
public class MastercCheckAuthResponseCodeServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(MastercCheckAuthResponseCodeServiceImpl.class);

    private static final String AUTHORIZED_FLAG = "1";
    private static final String UN_AUTHORIZED_FLAG = "0";

    @Resource
    private MastercAuthDetailDataModifyServiceImpl mastercAuthDetailDataModifyService;
    @Autowired
    private ITransVelocityStatisticsService transVelocityStatisticsService;
    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private ITransVelocityLogService transVelocityLogService;
    @Autowired
    private UpiPreAuthDataUpdateServiceImpl upiPreAuthDataUpdateService;
    @Resource
    private BlockCodeMaintenanceLogMapper blockCodeMaintenanceLogMapper;
    @Autowired
    private LimitRequestPrepareService limitRequestPrepareService;
    @Resource
    private AuthCheckItemManager authCheckItemManager;
    @Autowired
    private TransVelocityStatisticsJdbcService transVelocityStatisticsJdbcService;
    @Autowired
    private TransVelocityStatisticsSelfMapper transVelocityStatisticsSelfMapper;
    @Autowired
    private IAuthMatchRuleService authMatchRuleService;
    @Autowired
    private IOutstandingTransService outstandingTransService;
    @Autowired
    private ICardMdesNotificationService cardMdesNotificationService;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    @Autowired
    private IAuthPrePostLogService authPrePostLogService;
    @Autowired
    private AuthPrePostInfoModifyService authPrePostInfoService;
    /**
     * auth approve
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @return 异常:-2 拒绝:-1 通过成功:0
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicB(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        logger.info("Authorization approved");
        //数据获取
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        CardAuthorizationDTO cardAuthorizationInfo = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManagementInfoDTO = authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = authorizationCheckProcessingPayload.getCustomerAuthorizationInfoDTO();
        List<TransVelocityLogDTO> addTransVelocityLogList = authorizationCheckProcessingPayload.getTransVelocityLogDtoS();
        List<ParmAuthCheckControlDTO> authCheckControlDTOList = authorizationCheckProcessingPayload.getParmAuthCheckControlDTOList();
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        //如果是大莱 本行， 并且是还款交易则不需要调用额度管控单元
        boolean sourceFlag = (AuthTransactionSourceCodeEnum.THE_BANK.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())
                    /*|| AuthTransactionSourceCodeEnum.MASTERCARD.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())*/)
                    && AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode());
        //管控单元的获取
        boolean normalTransFlag = AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        boolean limitUpdateFlag = normalTransFlag && authorizationCheckProcessingPayload.getLimitCheckResult() == null;

        logger.warn("Card number: {}, limit update result: {}, transaction identification result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), limitUpdateFlag, sourceFlag);
        if(!sourceFlag && limitUpdateFlag){
            //如果是普通非还款交易，调用交易路由规则获取交易管控单元参数->进而获取一组额度管控单元，注意如果配了额度检查项就不会走这一段逻辑
            logger.info("Calling authMatchRuleService.limitCtrlUnitOccupiedResult: cardNumber={}", 
                    DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            authMatchRuleService.limitCtrlUnitOccupiedResult(orgInfo,
                    cardAuthorizationInfo, Optional.ofNullable(accountManagementInfoDTO).map(AccountManagementInfoDTO::getAccountManagementId).orElse(""), authRecordedDTO, customerAuthorizationInfoDTO);
            logger.info("Called authMatchRuleService.limitCtrlUnitOccupiedResult: success");
        }

        //流量流水表新增
        int res = transVelocityLogBatchInsert(addTransVelocityLogList);
        if(res != AuthConstans.ZERO){
            logger.error("transVelocityLogBatchInsert failed: res={}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.AFM);
        }
        //撤销交易、冲正交易、撤销冲正交易、退货，流量检查表数据更新处理
        boolean velocityFlag = AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())||
                AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())||
                AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        if(velocityFlag){
            res = velocityStatisticsProcess(authRecordedDTO);
            if(res != AuthConstans.ZERO){
                logger.error("velocityStatisticsProcess failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.UFM);
            }
        }
        //卡首次使用日期更新
        res = cardFirstDateUpdate(cardAuthorizationInfo,authRecordedDTO);
        if(res != AuthConstans.ZERO){
            logger.error("cardFirstDateUpdate failed: res={}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.CA_FU);
        }
        //sysflag判断
        if(systemInfo == null){
            logger.error("systemInfo is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.SE);
        }
        final boolean flagCheck = (systemInfo.getLimitUpdateFlag() == null) || (systemInfo.getOutstandingLogFlag() == null)
                || (systemInfo.getAuthorizationLogFlag() == null) || (systemInfo.getCardAuthorizationUpdateFlag() == null);
        if(flagCheck){
            logger.error("flagCheck is true");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.ST_E);
        }
        //更新未并账交易信息表
        if(AuthConstans.I.equals(systemInfo.getOutstandingLogFlag())){
            logger.info("Calling mastercAuthDetailDataModifyService.outStandingTransModify: cardNumber={}", 
                    DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            res = mastercAuthDetailDataModifyService.outStandingTransModify(authorizationCheckProcessingPayload);
            if(res != AuthConstans.ZERO){
                logger.error("Called mastercAuthDetailDataModifyService.outStandingTransModify: failed");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_AB_TR);
            }
            logger.info("Called mastercAuthDetailDataModifyService.outStandingTransModify: success");
        }
        //只有卡服务记录授权预入账登记表
        if (ServerTypeEnum.CARD_SERVER.getCode().equals(authRecordedDTO.getServerType())) {
            logger.info("Calling authPrePostInfoService.modifyAuthPrePostInfo: cardNumber={}", 
                    DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            int authPrePostRes = authPrePostInfoService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);
            if (authPrePostRes != AuthConstans.ZERO) {
                logger.info("Insert AuthPrePostLog error, cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_AB_TR);
            }
            logger.info("Called authPrePostInfoService.modifyAuthPrePostInfo: success");
        }
        //额度接口调用
        mastercAuthDetailDataModifyService.limitUpdate(authorizationCheckProcessingPayload);
        //授权流水表更新
        if(AuthConstans.I.equals(systemInfo.getAuthorizationLogFlag())){
            if(StringUtils.isBlank(authRecordedDTO.getAuthAuthCode())){
                authRecordedDTO.setAuthAuthCode(RandomStringUtils.randomNumeric(6));
            }
            if (StringUtils.isEmpty(authRecordedDTO.getAuthRetrievalReferenceNumber())) {
                authRecordedDTO.setAuthRetrievalReferenceNumber(DateUtils.getYear4AndDayOfYear()+RandomStringUtils.randomNumeric(8));
            }
            res = mastercAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
            if(res != AuthConstans.ONE){
                logger.error("mastercAuthDetailDataModifyService.modifyAuthorizationLog failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AU_FL);
            }
        }

        //更新过度用卡的相关字段
        buildCardAuthByCardOverUse(authorizationCheckProcessingPayload);

        //更新卡片授权信息(limit_check_indicator = 1) 满足限额检查标志和限额检查项
        if(AuthConstans.I.equals(systemInfo.getCardAuthorizationUpdateFlag())){
            if(StringUtils.isEmpty(cardAuthorizationInfo.getLimitCheckIndicator())){
                logger.error("cardAuthorizationInfo.getLimitCheckIndicator is null");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_CA_AU);
            }
            final boolean cardAuthorizationCheckFlag = (AuthConstans.AUTH_LIMIT_CHECK_INDICATOR_YES
                    .equals(cardAuthorizationInfo.getLimitCheckIndicator()))
                    && authCheckControlDTOList.stream().anyMatch(parmAuthCheckControlDTO ->
                    AuthCheckItemEnum.CARD_LIMIT.getCheckItem().equals(parmAuthCheckControlDTO.getCheckItem()));
            if (cardAuthorizationCheckFlag) {
                mastercAuthDetailDataModifyService.buildCardAutByAuthLlimit(authorizationCheckProcessingPayload);
            }
        }
        //卡片授权信息表set pin cvv 密码检查清0
        MastercAuthDetailDataModifyServiceImpl.cardAuthorizationSet(cardAuthorizationInfo,authCheckControlDTOList);
        UpiAuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(cardAuthorizationInfo,authRecordedDTO,orgInfo,true);
        res = mastercAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase(authorizationCheckProcessingPayload);
        if(res != AuthConstans.ZERO){
            logger.error("mastercAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase failed: res={}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AB_AIT);
        }

        //流量检查表数据更新
        List<TransVelocityStatisticsDTO> transVelocityStatisticsDtos = authorizationCheckProcessingPayload.getTransVelocityStatisticsDtoS();
        if(!CollectionUtils.isEmpty(transVelocityStatisticsDtos)){
            res = transVelocityStatisticsService.addOrUpdatePatch(transVelocityStatisticsDtos);
            if(res != AuthConstans.ZERO){
                logger.error("transVelocityStatisticsService.addOrUpdatePatch failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AB_UC);
            }
        }
        // 最小可用额度用于人工授权或强制授权
        String  detailCode =authRecordedDTO.getAuthTransactionTypeDetailCode();
        if(detailCode.endsWith("199")||detailCode.endsWith("299")){
            CalLimitTrialResDTO calLimitTrialResDTO =  limitRequestPrepareService.getLimitAvailable(authorizationCheckProcessingPayload);
            logger.info("Limit trial minAvailableAmount: {}", calLimitTrialResDTO.getMinAvailableAmount());
            authRecordedDTO.setAuthAvailableLimit(calLimitTrialResDTO.getMinAvailableAmount());
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * transvelocitylog
     * @param addTransVelocityLogList {@link TransVelocityLogDTO}
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int transVelocityLogBatchInsert(List<TransVelocityLogDTO> addTransVelocityLogList){
        if(!CollectionUtils.isEmpty(addTransVelocityLogList)){
            //批量插入
            addTransVelocityLogList.forEach(transVelocityLogDTO ->
                    transVelocityLogDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId())));
            if (CustAccountBO.isBatch()) {
                addTransVelocityLogList.forEach(CustAccountBO.threadCustAccountBO.get().getAuthBO()::insertTransVelocityLog);
            } else {
                int res = transVelocityLogService.insertBatch(addTransVelocityLogList);
                if(res < AuthConstans.ONE){
                    logger.error("transVelocityLogService.insertBatch failed: res={}", res);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_INSERT_TRANS_VELOCITY_LOG_FAIL, AuthRepDetailEnum.BI_FC);
                }
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 撤销交易、冲正交易、撤销冲正交易，流量检查数据更新处理
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @return int
     */
    private int velocityStatisticsProcess(AuthRecordedDTO authRecordedDTO){
        if(StringUtils.isEmpty(authRecordedDTO.getAuthOriginalGlobalFlowNumber())){
            return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }
        String globalFlowNumber=authRecordedDTO.getAuthOriginalGlobalFlowNumber();
        List<TransVelocityLogDTO> transVelocityLogList = transVelocityLogService.selectByAuthLogId(globalFlowNumber);
        if(CollectionUtils.isEmpty(transVelocityLogList)){
            //为空不再操作数据
           return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }
        List<String> bizKeyList = transVelocityLogList.stream().map(
                TransVelocityLogDTO::getBizKey).collect(Collectors.toList());
        List<TransVelocityStatistics> velocityStatisticsList = CollectionUtils.isEmpty(bizKeyList) ? Collections.emptyList() :
            transVelocityStatisticsSelfMapper.selectByBizKeyList(bizKeyList);

        List<TransVelocityStatistics> transVelocityStatisticsList = new ArrayList<>(4);
        for(TransVelocityLogDTO transVelocityLog : transVelocityLogList) {
            TransVelocityStatistics transVelocityStatistics = null;
            String bizKey = transVelocityLog.getBizKey();
            String velocityCode = transVelocityLog.getVelocityCde();
            String authTxnTopCode = transVelocityLog.getAuthTxnTopCode();
            String postingTransactionCode = transVelocityLog.getPostingTransactionCode();
            LocalDate startDate = transVelocityLog.getStartDate();
            BigDecimal deltaAmount = transVelocityLog.getDeltaAmount();
            //visa部分撤销处理
           /* if(authRecordedDTO.getAuthReplaceAmountActualVisa() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualVisa();
            }
            //mc部分撤销处理
            if(authRecordedDTO.getAuthReplaceAmountActualMc() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualMc();
            }
            //jcb部分撤销处理
            if(authRecordedDTO.getAuthReplaceAmountActualJcb() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualJcb();
            }*/

            if(StringUtils.isEmpty(bizKey)||StringUtils.isAllEmpty(velocityCode,postingTransactionCode)||startDate == null){
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL);
            }
            if (!CollectionUtils.isEmpty(velocityStatisticsList)){
                List<TransVelocityStatistics> velocityStatistics = velocityStatisticsList.stream().filter(x -> x.getBizKey().equals(bizKey)
                        && (x.getVelocityCde().equals(velocityCode) || x.getPostingTransactionCode().equals(postingTransactionCode)) && x.getStartDate().equals(startDate)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(velocityStatistics)){
                    transVelocityStatistics = velocityStatistics.get(0);
                }
            }
            if(transVelocityStatistics == null){
                logger.error("transVelocityStatistics is null");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_TRANS_VELOCITY_STATISTICS_FAIL);
            }
            if(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
                transVelocityStatistics.setPeriodTxnCount(transVelocityStatistics.getPeriodTxnCount() + 1);
                transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().add(deltaAmount));
            }else {
                OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                        globalFlowNumber, authRecordedDTO.getOrganizationNumber());
                BigDecimal billingAmount = Optional.ofNullable(outstandingTransactionDTO.getBillingAmount())
                        .orElse(BigDecimal.ZERO);

                BigDecimal amountReturned = Optional.ofNullable(outstandingTransactionDTO.getAmountReturned())
                        .orElse(BigDecimal.ZERO);
                //已经撤销的 + 本次需要撤销的  = 原交易金额 @todo 考虑增量的多重撤销
                int count = billingAmount.compareTo(authRecordedDTO.getAuthCardholderBillingAmount()
                        .add(amountReturned));

                if (count == 0) {
                    logger.info("Reversal all amounts, cumulative transaction count minus 1");
                    transVelocityStatistics.setPeriodTxnCount(transVelocityStatistics.getPeriodTxnCount() - 1);
                    transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().subtract(authRecordedDTO.getAuthTransactionAmount()));
                }else {
                    transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().subtract(authRecordedDTO.getAuthTransactionAmount()));
                }
            }
            transVelocityStatisticsList.add(transVelocityStatistics);
        }
        if (!CollectionUtils.isEmpty(transVelocityStatisticsList)){
            transVelocityStatisticsJdbcService.batchUpdateTransVelocityStatistics(transVelocityStatisticsList);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 首次用卡更新
     * @param cardAuthorizationDTO {@link CardAuthorizationDTO }
     * @return int
     */
    @BatchSharedAnnotation
    private int cardFirstDateUpdate(CardAuthorizationDTO cardAuthorizationDTO, AuthRecordedDTO authRecordedDTO){
        boolean flag = (cardAuthorizationDTO.getFstUsgDte() == null
                || AuthConstans.FST_USG_DTE
                .equals(new SimpleDateFormat("yyyy-MM-dd").format( Date.from(cardAuthorizationDTO.getFstUsgDte().atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        if (flag) {
            cardAuthorizationDTO.setFstUsgDte(LocalDate.now());

            CardAuthorizationInfo authorizationInfo = BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class);
            authorizationInfo.setUpdateTime(LocalDateTime.now());

            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
            } else {
                int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(authorizationInfo);
                if(res != 1){
                    logger.error("cardAuthorizationInfoMapper.updateByPrimaryKeySelective failed: res={}", res);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.DUF);
                }
            }
            authRecordedDTO.setFirstUsed(true);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }


    /**
     * 建立委托 无卡自助支付开关更新
     * @param cardAuthorizationDTO {@link CardAuthorizationDTO }
     * @return int
     */
    public int updateCardNotPresentSwitch(CardAuthorizationDTO cardAuthorizationDTO, boolean isAuthorized){
        logger.info("Cardless self-service switch judgment, cardNumber: {}, cardAuthorizationDTO: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()), cardAuthorizationDTO);
        String isAuthorizedNow = cardAuthorizationDTO.getCardNotPresentSwitch();
        if (isAuthorized) {
            if(!AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                cardAuthorizationDTO.setCardNotPresentSwitch(AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }else{
            cardAuthorizationDTO.setCardNotPresentSwitch(UN_AUTHORIZED_FLAG);
            this.updateCardAuthInfoNomal(cardAuthorizationDTO);
        }

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 建立委托冲正 无卡自助支付开关更新
     * @param authorizationCheckProcessingPayload {@link CardAuthorizationDTO }
     * @return int
     */
    public int updateCardNotPresentSwitchReverse(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,boolean isAuthorized){
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        String isAuthorizedNow = cardAuthorizationDTO.getCardNotPresentSwitch();
        if (isAuthorized){
            if (AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                cardAuthorizationDTO.setCardNotPresentSwitch(UN_AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }else{
            if (!AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            }else {
                cardAuthorizationDTO.setCardNotPresentSwitch(AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 修改卡片授权信息通用处理
     */
    @BatchSharedAnnotation
    private int updateCardAuthInfoNomal(CardAuthorizationDTO cardAuthorizationDTO){
        CardAuthorizationInfo authorizationInfo = BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class);
        authorizationInfo.setUpdateTime(LocalDateTime.now());
        authorizationInfo.setUpdateBy("SYSTERM");
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
        } else {
            int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(authorizationInfo);
            if(res != 1){
                logger.error("cardAuthorizationInfoMapper.updateByPrimaryKeySelective failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.DUF);
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * auth reject
     * @return int
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicC(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        logger.info("Authorization rejected, response code: {}", authRecordedDTO.getAuthResponseCode());
        //cardAuthorizationDTO 不存在
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        if(cardAuthorizationDTO == null){
            logger.error("Card authorization information does not exist");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        List<ParmAuthCheckControlDTO> authCheckControlDTOList = authorizationCheckProcessingPayload.getParmAuthCheckControlDTOList();
        AccountManagementInfoDTO accountManageInfo = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        //拆箱时候判断空
        int pinTriesCount = cardAuthorizationDTO.getPinTriesCount() == null ? 0:cardAuthorizationDTO.getPinTriesCount();
        int cvvTriesCount = cardAuthorizationDTO.getCvvTriesCount() == null ? 0:cardAuthorizationDTO.getCvvTriesCount();
        int cvv2TriesCount = cardAuthorizationDTO.getCvv2TriesCount() == null ? 0:cardAuthorizationDTO.getCvv2TriesCount();
        //获取授权检查参数
        AuthorizationRuleDTO authorizationRuleDTO = authorizationCheckProcessingPayload.getAuthorizationRuleDTO();
        int pinErrCnt = 0;
        int cvv2ErrCnt = 0;
        int cvvErrCnt = 0;
        if(authorizationRuleDTO != null){
            pinErrCnt  = authorizationRuleDTO.getMaxPinErrCnt();
            logger.info("PIN error count: {}", pinErrCnt);
            cvv2ErrCnt = authorizationRuleDTO.getMaxCvv2ErrCnt();
            logger.info("CVV2 error count: {}", cvv2ErrCnt);
            cvvErrCnt  = authorizationRuleDTO.getMaxCvvErrCnt();
            logger.info("CVV error count: {}", cvvErrCnt);
        }
        //撤销交易
        if(AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
            LocalDate lastStateDate = accountManageInfo.getLastStatementDate();
            LocalDate cycleSttDte;
            if (lastStateDate != null) {
                cycleSttDte = lastStateDate.plusDays(1);
            } else {
                cycleSttDte = accountManageInfo.getOpenDate();
            }
            LocalDate cleCtrSttDte = cardAuthorizationDTO.getCleCtrSttDte();
            LocalDate dailyCtrSttDte = cardAuthorizationDTO.getDlyCtrSttDte();
            Integer dcrAthCleNbr = cardAuthorizationDTO.getDceAthCleNbr();
            Integer dceAthDlyNbr = cardAuthorizationDTO.getDceAthDlyNbr();
            if(cycleSttDte.equals(cleCtrSttDte)) {
                cardAuthorizationDTO.setDceAthCleNbr(dcrAthCleNbr-1);
            }
            if(Objects.equals(dailyCtrSttDte, orgInfo.getNextProcessingDay())){
                cardAuthorizationDTO.setDceAthDlyNbr(dceAthDlyNbr - 1);
            }
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
            } else {
                cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class));
            }
        }
        //密码、CVV、CVV2，检查 如果变为N0插入卡片封锁码维护历史表
        BlockCodeMaintenanceLog blockCodeMaintenanceLog = new BlockCodeMaintenanceLog();
        blockCodeMaintenanceLog.setLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        blockCodeMaintenanceLog.setKey(cardAuthorizationDTO.getCardNumber());
        blockCodeMaintenanceLog.setKeyType(CardBusinessConstant.KEY_TYPE_B);
        blockCodeMaintenanceLog.setBranchNumber(cardAuthorizationDTO.getOrganizationNumber());
        blockCodeMaintenanceLog.setBlockCodeBefore(cardAuthorizationDTO.getBlockCode());
        blockCodeMaintenanceLog.setBlockCodeDateBefore(cardAuthorizationDTO.getBlockCodeDate());
        blockCodeMaintenanceLog.setPreviousBlockCodeBefore(cardAuthorizationDTO.getPreviousBlock());
        blockCodeMaintenanceLog.setPreviousBlockDateBefore(cardAuthorizationDTO.getPreviousBlockDate());
        blockCodeMaintenanceLog.setPreBlockStopDateBefore(cardAuthorizationDTO.getPreviousBlockStopDate());
        //密码、CVV、CVV2，检查
        if (!CollectionUtils.isEmpty(authCheckControlDTOList)) {
            logger.info("Auth check control DTO list: {}", 
                authCheckControlDTOList.stream()
                    .map(dto -> String.format("checkItem=%s, checkResult=%s", dto.getCheckItem(), dto.getCheckResult()))
                    .collect(Collectors.joining("; ")));
            for (ParmAuthCheckControlDTO parmAuthCheckControlDto : authCheckControlDTOList) {
                if (parmAuthCheckControlDto != null && parmAuthCheckControlDto.getCheckItem() != null) {
                    AuthCheckItemEnum authCheckItemEnum = AuthCheckItemEnum.getEnum(parmAuthCheckControlDto.getCheckItem());
                    Objects.requireNonNull(authCheckItemEnum,"authCheckItemEnum不能为空");
                    switch (authCheckItemEnum) {
                        case CVV:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setCvvTriesCount(cvvTriesCount+1);
                                if(cvvTriesCount+1 >= cvvErrCnt){
                                    logger.info("CVV with block code: maxCvvErrBlk={}", 
                                        authorizationRuleDTO != null ? authorizationRuleDTO.getMaxCvvErrBlk() : "null");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxCvvErrBlk());
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                    }
                                }
                            }
                            break;
                        case CVV2:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setCvv2TriesCount(cvv2TriesCount+1);
                                if(cvv2TriesCount+1 >= cvv2ErrCnt){
                                    logger.info("CVV2 with block code: maxCvv2ErrBlk={}", 
                                        authorizationRuleDTO != null ? authorizationRuleDTO.getMaxCvv2ErrBlk() : "null");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxCvv2ErrBlk());
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                    }
                                }
                            }
                            break;
                        case PASS_WORD:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setPinTriesCount(pinTriesCount+1);
                                if(pinTriesCount+1 > 3){
                                    logger.info("PIN with block code: maxPinErrBlk={}", 
                                        authorizationRuleDTO != null ? authorizationRuleDTO.getMaxPinErrBlk() : "null");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxPinErrBlk());
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                    }
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        //过度用卡检查拒绝
        //更新过度用卡的相关字段
        buildCardAuthByCardOverUse(authorizationCheckProcessingPayload);
//        logger.info("授权拒绝,cardAuthorizationDTO:{}", DesensitizedUtils.getJson(cardAuthorizationDTO));
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
        } else {
            cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class));
        }
        blockCodeMaintenanceLog.setBlockCodeAfter(cardAuthorizationDTO.getBlockCode());
        blockCodeMaintenanceLog.setBlockCodeDateAfter(orgInfo.getToday());
        blockCodeMaintenanceLog.setPreviousBlockCodeAfter(cardAuthorizationDTO.getPreviousBlock());
        blockCodeMaintenanceLog.setPreBlockDateAfter(cardAuthorizationDTO.getPreviousBlockDate());
        blockCodeMaintenanceLog.setPreBlockStopDateAfter(cardAuthorizationDTO.getPreviousBlockStopDate());
        blockCodeMaintenanceLog.setCreateTime(LocalDateTime.now());
        blockCodeMaintenanceLog.setUpdateTime(LocalDateTime.now());
        blockCodeMaintenanceLog.setUpdateBy("SYSTEM");
        blockCodeMaintenanceLog.setVersionNumber(1);
        try{
            if(AuthConstans.BLOCKCODE.equals(cardAuthorizationDTO.getBlockCode())){
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().insertBlockCodeMaintenanceLogList(BeanMapping.copy(blockCodeMaintenanceLog, BlockCodeMaintenanceLogDTO.class));
                } else {
                    blockCodeMaintenanceLogMapper.insertSelective(blockCodeMaintenanceLog);
                }
            }
        }catch (Exception e){
            logger.error("Authorization check password, CVV, CVV2 insert block code maintenance table failed", e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR,e);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    public void authDataUpdateLogicD(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        authDataUpdateLogicD(authorizationCheckProcessingPayload, true);
    }

    /**
     * mastercard 逻辑D ATM/POS查询
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * <AUTHOR>
     * @date 2019/04/09
     */
    @BatchSharedAnnotation
    public void authDataUpdateLogicD(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,
                                     Boolean authLogFlag) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        CardAuthorizationDTO cardAuthorizationInfo = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        logger.info("POS/ATM query function processing started");
        // 调用额度试算接口
        CalLimitTrialResDTO calLimitTrialResDTO =  limitRequestPrepareService.getLimitAvailable(authorizationCheckProcessingPayload);
        logger.info("Limit trial minAvailableAmount: {}", calLimitTrialResDTO.getMinAvailableAmount().toString());
        authRecordedDTO.setAuthAvailableLimit(calLimitTrialResDTO.getMinAvailableAmount());
        UpiAuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(cardAuthorizationInfo,authRecordedDTO,orgInfo,true);
        mastercAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase(authorizationCheckProcessingPayload);
        // 卡片限额开启
        if(LimitCheckIndicatorEnum.OPEN.getCode().equals(cardAuthorizationInfo.getLimitCheckIndicator())){
            //计算卡层面可用额度
            BigDecimal availableRetailAmountCard = cardAuthorizationDTO.getBillingCycleRetailLimit()
                    .subtract(cardAuthorizationDTO.getBillingCycleRetailAmount());
            BigDecimal availableCashAmountCard = cardAuthorizationDTO.getBillingCycleCashLimit()
                    .subtract(cardAuthorizationDTO.getBillingCycleCashAmount());
            //附加金额（authAdditionalAmounts）字段
            StringBuilder authAdditionalAmounts = new StringBuilder();
//            authAdditionalAmounts.append(authRecordedDTO.getAuthProcessingCode().substring(2, 4)
            authAdditionalAmounts.append("30"
                    + AuthConstans.ZERO_TWO + authRecordedDTO.getAuthBillingCurrencyCode());
            // POS查询
            if (TranTypeDetailEnum.POS.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
               /* if (!authRecordedDTO.getAuthTransactionCurrencyCode().equals(cardAuthorizationDTO.getAccount1Currency())) {
                    // 汇率转换逻辑
                    availableRetailAmountCard = lmtCommon.getCurrencyRate(cardAuthorizationInfo.getOrganizationNumber(),cardAuthorizationDTO.getAccount1Currency(),
                            authRecordedDTO.getAuthTransactionCurrencyCode(),availableRetailAmountCard);
                }*/
                //卡层可用额度与客户层额度比较 哪个小取哪个
                BigDecimal availableRetailAmount = calLimitTrialResDTO.getMinAvailableAmount().compareTo(availableRetailAmountCard) < 0 ? calLimitTrialResDTO.getMinAvailableAmount(): availableRetailAmountCard;
                //正值为C负值为D
                if (availableRetailAmount.compareTo(BigDecimal.ZERO) < 0) {
                    authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
                } else {
                    authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
                }
                //赋值最小可用金额
                authAdditionalAmounts.append(format12Amount(availableRetailAmount.abs()));
                //附加金额（authAdditionalAmounts）字段
                authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
            } else if (TranTypeDetailEnum.ATM.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
               /* if (!authRecordedDTO.getAuthTransactionCurrencyCode().equals(cardAuthorizationDTO.getAccount1Currency())) {
                    //汇率 转换
                    availableCashAmountCard = lmtCommon.getCurrencyRate(AuthConstans.DEFAULT_ORG_NUMBER,cardAuthorizationDTO.getAccount1Currency(),
                            authRecordedDTO.getAuthTransactionCurrencyCode(),availableCashAmountCard);
                }*/
                //卡层可用额度与客户层额度比较 哪个小取哪个
                BigDecimal availableCashAmount = calLimitTrialResDTO.getMinAvailableAmount().compareTo(availableCashAmountCard) < 0 ? calLimitTrialResDTO.getMinAvailableAmount(): availableCashAmountCard;
                //正值为C负值为D
                if (availableCashAmount.compareTo(BigDecimal.ZERO) < 0) {
                    authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
                } else {
                    authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
                }
                //赋值最小可用金额
                authAdditionalAmounts.append(format12Amount(availableCashAmount.abs()));
                //附加金额（authAdditionalAmounts）字段
                authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
            }
        }else {
            //附加金额（authAdditionalAmounts）字段
            StringBuilder authAdditionalAmounts = new StringBuilder();
//            authAdditionalAmounts.append(authRecordedDTO.getAuthProcessingCode().substring(2, 4)
            authAdditionalAmounts.append("30"
                    + AuthConstans.ZERO_TWO + authRecordedDTO.getAuthBillingCurrencyCode());
            //正值为C负值为D
            if (calLimitTrialResDTO.getMinAvailableAmount().compareTo(BigDecimal.ZERO) < 0) {
                authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
            } else {
                authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
            }
            //赋值最小可用金额
            authAdditionalAmounts.append(format12Amount(calLimitTrialResDTO.getMinAvailableAmount().abs()));
            //附加金额（authAdditionalAmounts）字段
            authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
        }
        //恢复大类为查询交易
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.QUERY_TRANS.getCode());
        logger.info("POS/ATM query function processing completed");


        if (authLogFlag){
            //插入授权流水表
            mastercAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
        }
    }

    /**
     * 查询类冲正交易更新数据
     * @param authorizationCheckProcessingPayload
     * @return
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicInquiry(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        int res = 0;
        //sysflag判断
        if(systemInfo == null){
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.SE);
        }
        final boolean flagCheck = (systemInfo.getLimitUpdateFlag() == null) || (systemInfo.getOutstandingLogFlag() == null)
                || (systemInfo.getAuthorizationLogFlag() == null) || (systemInfo.getCardAuthorizationUpdateFlag() == null);
        if(flagCheck){
            logger.error("flagCheck is true");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.ST_E);
        }
        //授权流水表更新
        if(AuthConstans.I.equals(systemInfo.getAuthorizationLogFlag())){
            res = mastercAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
            if(res != AuthConstans.ONE){
                logger.error("mastercAuthDetailDataModifyService.modifyAuthorizationLog failed: res={}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AU_FL);
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 金额12位格式化
     * @param amount
     * @return
     */
    private String format12Amount(BigDecimal amount) {
        BigDecimal addAmount = amount.multiply(new BigDecimal(AuthConstans.DIVIDE_100));
        return String.valueOf(String.format("%012d",addAmount.intValue()));
    }

    /**
     * 预授权数据更新
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @return int
     */
    @BatchSharedAnnotation
    public int preAuthUpdate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        //预授权请求,撤销,冲正,撤销冲正
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        if(authRecordedDTO.getPreAuth()){
            return upiPreAuthDataUpdateService.preAuthRequestDataUpdate(authorizationCheckProcessingPayload);
        }
        //预授权完成,撤销,冲正,撤销冲正
        if(authRecordedDTO.getPreAuthComplete()){
            return  upiPreAuthDataUpdateService.preAuthFinishDataUpdate(authorizationCheckProcessingPayload);
        }
        return 0;
    }


    /**
     * 构建过度用卡相关字段
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    private void buildCardAuthByCardOverUse(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        if (ServerTypeEnum.CARD_SERVER.getCode().equals(authRecordedDTO.getServerType())){
            logger.info("Card Server Not buildCardAuthByCardOverUse: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            return;
        }
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManageInfo = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        LocalDate cleCtrSttDte = cardAuthorizationDTO.getCleCtrSttDte();
        LocalDate dlyCtrSttDte = cardAuthorizationDTO.getDlyCtrSttDte();
        LocalDate cycleSttDte;
        LocalDate lastStateDate = accountManageInfo.getLastStatementDate();
        if (lastStateDate != null) {
            cycleSttDte = lastStateDate.plusDays(1);
        } else {
            cycleSttDte = accountManageInfo.getOpenDate();
        }
        LocalDate dailySttDte = orgInfo.getNextProcessingDay();
        int aprAthCleNbr = cardAuthorizationDTO.getAprAthCleNbr()==null
                ? 0:cardAuthorizationDTO.getAprAthCleNbr();
        int dceAthCleNbr = cardAuthorizationDTO.getDceAthCleNbr()==null
                ? 0:cardAuthorizationDTO.getDceAthCleNbr();
        int aprAthDlyNbr = cardAuthorizationDTO.getAprAthDlyNbr()==null
                ? 0:cardAuthorizationDTO.getAprAthDlyNbr();
        int dceAthDlyNbr = cardAuthorizationDTO.getDceAthDlyNbr()==null
                ? 0:cardAuthorizationDTO.getDceAthDlyNbr();
        if (AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_300.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_400.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_600.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.APPROVED_BY_XPRESS.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_800.getCode().equals(authRecordedDTO.getAuthResponseCode())) {
            if (cycleSttDte.equals(cleCtrSttDte)){
                cardAuthorizationDTO.setAprAthCleNbr(aprAthCleNbr + AuthConstans.ONE);
            }else {
                cardAuthorizationDTO.setAprAthCleNbr(1);
                cardAuthorizationDTO.setCleCtrSttDte(cycleSttDte);
            }
            if (dailySttDte.equals(dlyCtrSttDte)){
                cardAuthorizationDTO.setAprAthDlyNbr(aprAthDlyNbr + AuthConstans.ONE);
            }else {
                cardAuthorizationDTO.setAprAthDlyNbr(1);
                cardAuthorizationDTO.setDlyCtrSttDte(dailySttDte);
            }
        }else{
            if (cycleSttDte.equals(cleCtrSttDte)){
                cardAuthorizationDTO.setDceAthCleNbr(dceAthCleNbr + 1);
            }else {
                cardAuthorizationDTO.setDceAthCleNbr(1);
                cardAuthorizationDTO.setCleCtrSttDte(cycleSttDte);
            }
            if (dailySttDte.equals(dlyCtrSttDte)){
                cardAuthorizationDTO.setDceAthDlyNbr(dceAthDlyNbr + 1);
            }else {
                cardAuthorizationDTO.setDceAthDlyNbr(1);
                cardAuthorizationDTO.setDlyCtrSttDte(dailySttDte);
            }
        }
    }
}
