package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountStatementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountStatementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatementInfo;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.dao.customer.mapper.CustomerAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.customer.model.CustomerAuthorizationInfo;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.limit.mapper.LimitCustCreditInfoMapper;
import com.anytech.anytxn.business.dao.limit.model.LimitCustCreditInfo;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionMapper;
import com.anytech.anytxn.business.dao.transaction.mapper.PostedTransactionSelfMapper;
import com.anytech.anytxn.business.dao.transaction.model.PostedTransaction;
import com.anytech.anytxn.installment.base.domain.dto.*;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.business.base.installment.enums.InstallmentTypeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.installment.base.service.IInstallSingleAppEntryService;
import com.anytech.anytxn.installment.base.service.IInstallSingleEntryAppService;
import com.anytech.anytxn.installment.base.service.IInstallStagingListServiceApp;
import com.anytech.anytxn.installment.service.manager.InstallmentSmsManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单笔分期业务逻辑实现
 *
 * <AUTHOR>
 * @date 2024/01/05
*/
@Service
public class InstallSingleAppEntryServiceImpl implements IInstallSingleAppEntryService {

    private static final Logger logger = LoggerFactory.getLogger(InstallSingleAppEntryServiceImpl.class);

    @Autowired
    private IInstallStagingListServiceApp installStagingListServiceApp;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private PostedTransactionMapper postedTransactionMapper;

    @Autowired
    private AccountStatementInfoSelfMapper accountStatementInfoSelfMapper;

    @Autowired
    private InstallOrderMapper installOrderMapper;

    @Autowired
    private LimitCustCreditInfoMapper limitCustCreditInfoMapper;

    @Autowired
    private CustomerAuthorizationInfoSelfMapper customerAuthorizationInfoSelfMapper;

    @Autowired
    private PostedTransactionSelfMapper postedTransactionSelfMapper;

    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;

    @Autowired
    private InstallmentSmsManager installmentSmsManager;

    @Autowired
    private IInstallSingleEntryAppService installSingleEntryAppService;

    @Autowired
    private StatementManager statementManager;

    private void checkInstallTrading(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getOriginTransactionId())) {
            logger.error("Transaction ID cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.TR_IN);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getAccountManagementId())) {
            logger.error("Management account ID cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_QM);
        }

        if (StringUtils.isEmpty(installTradingSearchKeyDTO.getCardNumber())) {
            logger.error("Card number cannot be empty or blank");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_CA);
        }

        if (installTradingSearchKeyDTO.getInstallAmount() == null) {
            logger.error("Install amount cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_AM);
        }
    }
    @Override
    public List<InstallTradingDTO> findInstallSingleListApp(InstallTradingSearchKeyDTO installTradingSearchKeyDTO) {
        logger.info("APP single installment detail query interface - request parameters: originTransactionId={}, accountManagementId={}, cardNumber={}",
                installTradingSearchKeyDTO != null ? installTradingSearchKeyDTO.getOriginTransactionId() : null,
                installTradingSearchKeyDTO != null ? installTradingSearchKeyDTO.getAccountManagementId() : null,
                installTradingSearchKeyDTO != null ? installTradingSearchKeyDTO.getCardNumber() : null);
        if (installTradingSearchKeyDTO == null) {
            logger.error("Single installment list query parameter cannot be null");
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.SI_IN_Q);
        }
        //必输项检查
        checkInstallTrading(installTradingSearchKeyDTO);
        installTradingSearchKeyDTO.setOrganizationNumber(installTradingSearchKeyDTO.getOrganizationNumber());
        if (InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode());
        }
        if (InstallmentTypeEnum.RESET_INSTALL.getCode().equals(installTradingSearchKeyDTO.getInstallFlag())){
            installTradingSearchKeyDTO.setType(InstallmentTypeEnum.RESET_INSTALL.getCode());
        }
        //installTradingSearchKeyDTO.setPaymentWay(InstallPaymentWayEnum.WAIT_FEE.getCode())
        //installTradingSearchKeyDTO.setFeeFlag(InstallFeeReceiveFlagEnum.HIRE_CHARGE.getCode())
        installTradingSearchKeyDTO.setTerm(0);
        installTradingSearchKeyDTO.setInstallAmount(installTradingSearchKeyDTO.getInstallAmount()
                .setScale(2, BigDecimal.ROUND_HALF_UP));
        // 交易日期和最后还款日赋值
        String originTransactionId = installTradingSearchKeyDTO.getOriginTransactionId();
        PostedTransaction postedTransaction = postedTransactionMapper.selectByPrimaryKey(originTransactionId);
        installTradingSearchKeyDTO.setTransDate(postedTransaction.getTransactionDate().toLocalDate());
        logger.info("Calling installStagingListServiceApp.findInstallTradingByOptions with originTransactionId={}", originTransactionId);
        List<InstallTradingDTO> installTradingByOptions = installStagingListServiceApp.findInstallTradingByOptions(installTradingSearchKeyDTO);

        logger.info("APP single installment detail query interface - returned {} records", installTradingByOptions != null ? installTradingByOptions.size() : 0);
        return installTradingByOptions;
    }
    private List<AccountStatementInfoDTO> findLastedStatementInfo(String accountManageInfoId) {
        try {
            List<AccountStatementInfo> infos = this.accountStatementInfoSelfMapper.selectLastedStatementInfoByAccountManagementIdAndDate(accountManageInfoId);

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(infos)){
                return Collections.emptyList();
            }

            return BeanMapping.copyList(infos, AccountStatementInfoDTO.class);
        } catch (Exception e) {
            logger.error("accountManageInfoId is {} select data error ", accountManageInfoId, e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
    }
    @Override
    public InstallEntryAppResDTO singleInstallmentApp(InstallEntryAppDTO installEntryAppDTO) {
        long l = System.currentTimeMillis();
        logger.info("APP single installment confirmation request parameters: cardNumber={}, originTransactionId={}, installAmount={}",
                installEntryAppDTO != null ? installEntryAppDTO.getCardNumber() : null,
                installEntryAppDTO != null ? installEntryAppDTO.getOriginTransactionId() : null,
                installEntryAppDTO != null ? installEntryAppDTO.getInstallAmount() : null);
        InstallEntryDTO installEntryDTO = BeanMapping.copy(installEntryAppDTO, InstallEntryDTO.class);
        InstallEntryAppResDTO result = null;
        try {
            if (installmentSmsManager.verifyOtpToEai(installEntryAppDTO.getCardNumber(), installEntryAppDTO.getOpt())) {
                logger.info("SMS verification successful - proceeding with single installment");
            } else {
                logger.error("Single installment SMS verification failed");
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.SMS_SEND_FAILED);
            }
        } catch (Exception e) {
            logger.error("Single installment SMS verification failed", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.SMS_SEND_FAILED);
        }
        try {
                AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByAccountManagementIdAndOrganizationNumber(installEntryDTO.getAccountManagementId(), OrgNumberUtils.getOrg());
                installEntryDTO.setStatementDate(accountManagementInfo==null?null:accountManagementInfo.getLastStatementDate());
                long a2 = System.currentTimeMillis();
                logger.info("Calling installSingleEntryAppService.singleInstallment with accountManagementId={}", installEntryDTO.getAccountManagementId());
                InstallEntryResDTO installEntryResDTO = installSingleEntryAppService.singleInstallment(installEntryDTO);
                logger.info("Single installment processing completed in {} milliseconds", System.currentTimeMillis()-a2);
                result = BeanMapping.copy(installEntryResDTO, InstallEntryAppResDTO.class);
                String orderId = installEntryResDTO.getOrderId();
                InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
                BigDecimal installmentAmount = installOrder.getInstallmentAmount();
                LocalDateTime applyTime = installOrder.getCreateTime();
                result.setApplyDateTime(applyTime);
                result.setInstallmentAmount(installmentAmount);
        }catch (Exception e) {
            logger.error("Single installment processing failed", e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_ENTRY_FAIL_FAULT);
        }
        logger.info("Single installment total processing time: {} milliseconds", System.currentTimeMillis()-l);
        logger.info("APP single installment confirmation response: orderId={}, installmentAmount={}",
                result != null ? result.getOrderId() : null,
                result != null ? result.getInstallmentAmount() : null);
        return result;
    }

    @Override
    public List<SingleInstallAppDTO> singleInstallmentApp(String cardNumber, String installFlag) {
        logger.info("APP single installment list query request parameters: cardNumber={}, installFlag={}", cardNumber, installFlag);
        CardAuthorizationInfo cardAuthorizationInfo = cardAuthorizationInfoSelfMapper.selectByCardNumber(cardNumber);
        if (null == cardAuthorizationInfo){
            logger.error("Card authorization info not found for cardNumber={}", cardNumber);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_CARD_AUTH_INFO_NOT_EXIST_FAULT);
        }
        AccountManagementInfo accountManagementInfo = accountManagementInfoSelfMapper.selectByCusIdProNumAndOrg(cardAuthorizationInfo.getPrimaryCustomerId(),cardAuthorizationInfo.getProductNumber(),OrgNumberUtils.getOrg());
        if (null == accountManagementInfo){
            logger.error("Account management info not found for customerId={}, productNumber={}", cardAuthorizationInfo.getPrimaryCustomerId(), cardAuthorizationInfo.getProductNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCOUNT_MANAGEMENT_RECORDED_NOT_EXIST_FAULT);
        }
        logger.info("Calling installSingleEntryAppService.findByAccountManagementId with accountManagementId={}", accountManagementInfo.getAccountManagementId());
        List<SingleInstallAppDTO> singleInstallDTOList = installSingleEntryAppService.findByAccountManagementId(accountManagementInfo.getAccountManagementId(), InstallmentTypeEnum.APP_SINGLE_INSTALL.getCode());
        List<SingleInstallAppDTO> singleInstallAppDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(singleInstallDTOList)){
            List<PostedTransaction> postedTransactions = postedTransactionSelfMapper.selectByOrgAndPostingDateAndAcctMid(OrgNumberUtils.getOrg(), accountManagementInfo.getLastStatementDate(), accountManagementInfo.getAccountManagementId());
            for (SingleInstallDTO singleInstallDTO : singleInstallDTOList) {
                SingleInstallAppDTO copy = BeanMapping.copy(singleInstallDTO, SingleInstallAppDTO.class);
                dataFillingInstalment(copy, accountManagementInfo,cardAuthorizationInfo,postedTransactions);
                singleInstallAppDTOList.add(copy);
            }
        }
        logger.info("APP single installment list returned {} records", singleInstallAppDTOList.size());
        return singleInstallAppDTOList;
    }
    /**
     *单笔分期
     * @param singleInstallAppDTO
     */
    public void dataFillingInstalment(SingleInstallAppDTO singleInstallAppDTO, AccountManagementInfo accountManagementInfo,CardAuthorizationInfo cardAuthorizationInfo, List<PostedTransaction> postedTransactions) {
        List<PostedTransaction> postedInterestTransactionList = postedTransactions.stream().filter(t -> StringUtils.equalsAny(t.getPostingTransactionCode(),"FE006","FE005")).collect(Collectors.toList());
        //1 账单里里有FE006交易码的交易不进行账单分期
        if (!CollectionUtils.isEmpty(postedInterestTransactionList)){
            singleInstallAppDTO.setFe006Flag(true);
        }
        BigDecimal totalDueAmount = accountManagementInfo.getTotalDueAmount();//最小还款额
        //账单分期完成最低还款额才能进行账单分期
        singleInstallAppDTO.setTotalDueAmount(totalDueAmount);
        List<AccountStatementInfoDTO> statementInfos = this.findLastedStatementInfo(accountManagementInfo.getAccountManagementId());
        //获取账单金额
        if (!CollectionUtils.isEmpty(statementInfos)){
            singleInstallAppDTO.setBillAmount(statementInfos.get(0).getCloseBalance());
        }
        //账户，活跃的卡是否有封锁码
        singleInstallAppDTO.setAccountBlockCode(accountManagementInfo.getBlockCode());
        if (null != cardAuthorizationInfo){
            singleInstallAppDTO.setCardBlockCode(cardAuthorizationInfo == null?null:cardAuthorizationInfo.getBlockCode());
            //D-lite卡
            singleInstallAppDTO.setpCardProductNumber(cardAuthorizationInfo==null?null:cardAuthorizationInfo.getProductNumber());
            // todo 方法不存在临时调整
            LimitCustCreditInfo limitCustCreditInfo = null;//limitCustCreditInfoMapper.selectByCdOrgAndLimitTypeCodeAndProduct(cardAuthorizationInfo.getPrimaryCustomerId(),
//                    OrgNumberUtils.getOrg(), LimitTypeEnum.SC01.getCode(), accountManagementInfo.getProductNumber());
            singleInstallAppDTO.setCreditLimitAmount(limitCustCreditInfo==null?null:limitCustCreditInfo.getFixLimitAmount());
        }
        // 持卡人手机号
        CustomerAuthorizationInfo customerAuthorizationInfo = customerAuthorizationInfoSelfMapper.selectByCustomerId(OrgNumberUtils.getOrg(), cardAuthorizationInfo.getPrimaryCustomerId());
        singleInstallAppDTO.setPhoneNumber(customerAuthorizationInfo ==null?null:customerAuthorizationInfo.getMobilePhone());
        //商户名称获取
       // List<PostedTransaction> postedTransactionList = postedTransactions.stream().filter(t -> t.getPostedTransactionId().equals(singleInstallAppDTO.getOriginTransactionId())).collect(Collectors.toList());
       // singleInstallAppDTO.setMerchantName(CollectionUtils.isEmpty(postedTransactionList)?null:postedTransactionList.get(0).getMerchantName());
    }
}
