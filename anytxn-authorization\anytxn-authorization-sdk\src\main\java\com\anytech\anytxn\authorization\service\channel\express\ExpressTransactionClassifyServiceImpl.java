package com.anytech.anytxn.authorization.service.channel.express;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.service.auth.IAuthCheckProcessService;
import com.anytech.anytxn.authorization.base.service.express.IExpressOriginTransMatchProcessService;
import com.anytech.anytxn.authorization.base.service.express.IExpressTransactionClassifyService;
import com.anytech.anytxn.authorization.service.auth.AuthDataUpdateServiceImpl;
import com.anytech.anytxn.authorization.service.auth.AuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.authorization.base.enums.AuthMatchResultEnum;
import com.anytech.anytxn.authorization.base.enums.TranTypeDetailEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTransTypeEnum;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthorizationLogExpressDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: sukang
 * @Date: 2020/10/23 22:59
 */
@Service
public class ExpressTransactionClassifyServiceImpl implements IExpressTransactionClassifyService {

    private static final Logger logger = LoggerFactory.getLogger(ExpressTransactionClassifyServiceImpl.class);

    @Resource
    private IAuthCheckProcessService authCheckProcessService;

    @Resource
    private IExpressOriginTransMatchProcessService expressOriginTransMatchProcessService;

    @Resource
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Resource
    private AuthDataUpdateServiceImpl authDataUpdateService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    @Override
    public int processExpressAuthTrans(AuthRecordedDTO authRecordedDTO) throws IOException {
        //结果码
        int res = 0;
        //校验情况 应答码
        AuthTransTypeEnum authTransTypeEnum;
        if (authRecordedDTO.getAuthTransactionTypeCode() != null) {
            authTransTypeEnum = AuthTransTypeEnum.getEnum(authRecordedDTO.getAuthTransactionTypeCode());
            Objects.requireNonNull(authTransTypeEnum);

            switch (authTransTypeEnum) {
                case NORMAL_TRANS:
                    logger.info("Calling authCheckProcessService.authCheck");
                    res = authCheckProcessService.authCheck(authRecordedDTO);
                    logger.info("Completed authCheckProcessService.authCheck");
                    break;
                case REVOCATION_TRANS:
                case REVERSAL_TRANS:
                    cancelAndReverse(authRecordedDTO);
                    break;
                case REVOCATION_REVERSAL_TRANS:
                    expressCancelReversalProcess(authRecordedDTO);
                    break;
                default:
                    break;
            }
        }
        if (res == AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode()) {
            authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.EXCEPTION.getCode());
        }
        return res;
    }



    private void expressCancelReversalProcess(AuthRecordedDTO authRecordedDTO) {
        logger.info("Express cancel reversal transaction begin");
        AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();

        //调用匹配接口
        logger.info("Calling expressOriginTransMatchProcessService.matchOriginTrans");
        Map<String, AuthorizationLogExpressDTO> originalMatchSignMap = expressOriginTransMatchProcessService.matchOriginTrans(authRecordedDTO);
        logger.info("Completed expressOriginTransMatchProcessService.matchOriginTrans");

        originalMatchSignMap.forEach((key,value)->{
            if(AuthConstans.I.equals(key)){
                //授权接口赋值
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                authRecordedDTO.setAuthGlobalFlowNumber(value.getGlobalFlowNumber());
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(value.getOriginalGlobalFlowNumber());
                authRecordedDTO.setAuthTransactionTypeTopCode(value.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(value.getTransactionTypeDetailCode());
                authRecordedDTO.setAuthAuthCode(value.getAuthCode());
                authRecordedDTO.setAuthCustomerId(value.getCustomerId());
                authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
                //数据更新
                try {
                    logger.info("Calling authCheckProcessService.authCheck");
                    authCheckProcessService.authCheck(authRecordedDTO);
                    logger.info("Completed authCheckProcessService.authCheck");
                } catch (IOException e) {
                    logger.error("Express authorization data preparation update exception", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION,e);
                }
            }
            if(AuthConstans.II.equals(key)||AuthConstans.III.equals(key)){
                authRecordedDTO.setErrorDetail(AuthResponseCodeEnum.ORIGINAL_TRANS_ERROR);
                logger.info("Calling authDataUpdateService.authDataUpdate");
                authDataUpdateService.authDataUpdate(authorizationCheckProcessingPayload);
                logger.info("Completed authDataUpdateService.authDataUpdate");
            }
        });
        logger.info("Express cancel reversal transaction end");

    }




    /**
     * @description 撤销和冲正处理
     *
     * 1:匹配到原交易,并且原交易授权成功;
     * 2:匹配到原交易，并且原交易授权失败;
     * 3:未匹配到原交易
     */
    public void cancelAndReverse(AuthRecordedDTO authRecordedDTO) {
        //1:匹配到原交易，并且原交易授权成功;
        // 2:匹配到原交易，并且原交易授权失败 ;
        // 3:未匹配到原交易
        logger.info("Calling expressOriginTransMatchProcessService.matchOriginTrans");
        Map<String, AuthorizationLogExpressDTO> matchResult = expressOriginTransMatchProcessService.matchOriginTrans(authRecordedDTO);
        logger.info("Completed expressOriginTransMatchProcessService.matchOriginTrans");

        matchResult.forEach((key, expressLog) -> {
            if (AuthMatchResultEnum.MATCH_SUCCESS.getCode().equals(key)) {
                authRecordedDTO.setAuthCardholderBillingAmount(expressLog.getCardholderBillingAmount());
                authRecordedDTO.setAuthBillingCurrencyCode(expressLog.getBillingCurrencyCode());
                authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
                authRecordedDTO.setAuthTrancactionStatus(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode());
                if (AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
                    authRecordedDTO.setAuthOriginalGlobalFlowNumber(expressLog.getGlobalFlowNumber());
                }
                if (AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())) {
                    authRecordedDTO.setAuthGlobalFlowNumber(expressLog.getGlobalFlowNumber());
                    authRecordedDTO.setAuthOriginalGlobalFlowNumber(expressLog.getGlobalFlowNumber());
                }
                authRecordedDTO.setAuthTransactionTypeTopCode(expressLog.getTransactionTypeTopCode());
                authRecordedDTO.setAuthTransactionTypeDetailCode(expressLog.getTransactionTypeDetailCode());
                //返回系统自动生成的授权码
                authRecordedDTO.setAuthAuthCode(expressLog.getAuthCode());
                authRecordedDTO.setAuthCustomerId(expressLog.getCustomerId());
                //实时入账交易码,实时入账校验码(撤销或者冲正用)
                authRecordedDTO.setPostingTransactionCode(expressLog.getPostingTransactionCode());
                authRecordedDTO.setPostingTransactionCodeRev(expressLog.getPostingTransactionCodeRev());
                //数据更新
                try {
                    logger.info("Calling authCheckProcessService.authCheck");
                    authCheckProcessService.authCheck(authRecordedDTO);
                    logger.info("Completed authCheckProcessService.authCheck");
                    return;
                } catch (Exception e) {
                    logger.error("Express authorization exception", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION,e);
                }
            }

            //联机退货未匹配到
            if(AuthMatchResultEnum.NOMATCH.getCode().equals(key) && "0220".equals(authRecordedDTO
                    .getAuthMessageTypeId())){
                logger.info("Online refund not found, using normal logic");
                //临时方案
                authRecordedDTO.setAuthTransactionTypeTopCode("R");
                authRecordedDTO.setAuthTransactionTypeDetailCode("R002");
                authRecordedDTO.setPostingTransactionCode("40000");
                authRecordedDTO.setPostingTransactionCodeRev("40100");
                //
                authRecordedDTO.setAuthOriginalGlobalFlowNumber(sequenceIdGen.generateId(TenantUtils.getTenantId()));
                authRecordedDTO.setAuthAuthCode(authRecordedDTO.getAuthAuthIdentificationResponse());
                try {
                    logger.info("Calling authCheckProcessService.authCheck");
                    authCheckProcessService.authCheck(authRecordedDTO);
                    logger.info("Completed authCheckProcessService.authCheck");
                    return;
                } catch (IOException e) {
                    logger.error("Online refund match original transaction exception", e);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.R_ISO_EXCEPTION,e);
                }
            }

            if(AuthMatchResultEnum.MATCH_REVACATED.getCode().equals(key)){
                logger.info("Original transaction already refunded, returning 00");
                authRecordedDTO.setAuthResponseCode("00");
                authRecordedDTO.setAuthTrancactionStatus("1");
                authRecordedDTO.setAuthAuthCode(expressLog.getAuthCode());
            }else{
                logger.info("Original transaction not matched or in error status");
                authRecordedDTO.setAuthResponseCode("25");
                authRecordedDTO.setAuthTrancactionStatus("4");
                //贷记交易退货处理
                if(TranTypeDetailEnum.DEBIT_TXN.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())){
                    authRecordedDTO.setAuthResponseCode("95");
                    authRecordedDTO.setAuthResponseReasonCode("21");
                    authRecordedDTO.setAuthTrancactionStatus("4");
                }
            }
            logger.info("Calling authDetailDataModifyService.addAuthorizationLog");
            authDetailDataModifyService.addAuthorizationLog(authRecordedDTO);
            logger.info("Completed authDetailDataModifyService.addAuthorizationLog");
        });
    }
}

