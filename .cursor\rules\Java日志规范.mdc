---
description: 
globs: 
alwaysApply: false
---
# Java日志记录规范与最佳实践 v2.0

## 1. 核心原则

### 1.1 基本原则

1. **隔离性**：日志输出不能影响系统正常运行；
2. **安全性**：日志打印本身不能存在逻辑异常或漏洞，导致产生安全问题；
3. **数据安全**：不允许输出机密、敏感信息，如用户联系方式、身份证号码、token 等；
4. **可监控分析**：日志可以提供给监控进行监控，分析系统进行分析；
5. **可定位排查**：日志信息输出需有意义，需具有可读性，可供日常开发排查线上问题；
6. **业务逻辑不变性**：不得为了记录日志而修改原有业务逻辑结构。

## 2. 日志框架选择

### 2.1 核心原则

- 使用 SLF4J 作为日志门面，搭配 Logback 或 Log4j2 作为具体实现。
- 避免直接使用原生日志 API（如 System.out、java.util.logging、Log4j 1.x）。

### 2.2 示例代码

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ExampleClass {
    private static final Logger logger = LoggerFactory.getLogger(ExampleClass.class);
}
```

## 3. 日志级别规范

### 3.1 级别定义（从低到高）

| 级别  | 用途描述                                   | 使用场景示例                                                 |
| ----- | ------------------------------------------ | ------------------------------------------------------------ |
| TRACE | 开发调试详细信息（仅开发环境）             | `logger.trace("Entering method: {}, params: {}", methodName, params)` |
| DEBUG | 开发 / 测试阶段关键信息                    | `logger.debug("Database query elapsed time: {}ms", elapsedTime)`          |
| INFO  | 系统正常运行的关键状态（生产环境默认级别） | `logger.info("User {} login successful", username)`                  |
| WARN  | 潜在问题（不影响系统运行，但需关注）       | `logger.warn("Configuration file {} not found, using default values", configPath)`  |
| ERROR | 非致命错误（不影响系统继续运行）           | `logger.error("File write failed, path: {}", filePath, e)`        |
| FATAL | 致命错误（导致系统崩溃）                   | `logger.fatal("Application initialization failed, exiting", e); System.exit(1);` |

### 3.2 注意事项

1. 不能为了加日志，而去修改业务代码，尤其是不能加try-catch。
2. 如果检查的类之前没有记录日志，该类也没有服务间调用的逻辑，直接跳过（注意，Controller类除外），不做任何处理，如果这个类在日志修改记录文档中，请在日志修改记录文档中把这个类的状态标记为已跳过。
3. 服务间调用要用info级别，并且出口和入口都要记录日志，一定要注意调用Mapper不需要记录日志。
4. 要在抛异常的逻辑前添加异常日志，级别为ERROR, 但是如果原来抛异常前有日志的记录，只需要把该日志给规范化即可，不能再加异常日志也不要修改原来异常日志的级别。
5. 严禁在记录日志时候使用不存在的属性和方法，尤其在使用DTO字段和PageResultDTO字段时，一定先去查看该DTO类是否存在该字段，避免使用不存在的字段，导致编译错误。
6. 日志严禁打印整个JSON和对象，建议选取实际存在重要的一至三个重要的字段，一定要是实际存在，避免编译错误。
7. 严禁添加或者修改注释。
8. 严禁修改原来的日志级别。
9. 严禁删除原来的日志。

## 4. 日志记录的时机

### 4.1 应该记录日志的场景

1. **异常处理**：捕获异常时根据业务影响选择 ERROR 级别；
2. **外部服务调用**：记录第三方服务调用的请求和响应关键信息；

### 4.2 不应该记录日志的场景

❌ **禁止在每个方法开始和结束位置添加日志**
❌ **禁止为了记录日志而修改原有业务逻辑结构**
❌ **禁止记录过于频繁的循环操作**
❌ **禁止记录无意义的调试信息到生产环境**

### 4.3 应该跳过的场景
1. **跳过原则**：如果检查的类之前没有记录日志，该类也没有服务间调用的逻辑，直接跳过，不做任何处理，如果这个类在writer_log.md文件记录，请在writer_log.md文件中的这个类的状态标记为已跳过。

## 5. 日志等级设置规范

### 主要使用等级

- **DEBUG**：开发、测试阶段输出调试信息（参数、调试细节、返回值等），生产环境关闭。
- **INFO**：记录系统关键信息（初始化配置、业务状态变化），生产环境默认级别，用于运维和错误回溯。
- **WARN**：输出可预知的警告信息（如方法入参为空），需详尽记录以便分析。
- **ERROR**：记录不可预知的异常（如数据库连接失败、OOM），需输出方法入参、异常对象及堆栈。

### 示例：WARN/ERROR 区分

| 常见 WARN 级别异常                       | 常见 ERROR 级别异常            |
| ---------------------------------------- | ------------------------------ |
| 用户输入参数错误                         | 程序启动失败                   |
| 非核心组件初始化失败                     | 核心组件初始化失败             |
| 后端任务处理最终失败（无重试或重试失败） | 连不上数据库                   |
| 数据插入幂等                             | 核心业务依赖的外部系统持续失败 |
|                                          | OOM、程序技术异常              |

**注意**：避免滥用 ERROR 日志，防止重要问题被噪音掩盖。

## 6. 日志内容基本规范

1. **禁止使用具体日志实现类**
   ❌ 反例：

   ```java
   import org.apache.logging.log4j.LogManager;
   import org.apache.logging.log4j.Logger;
   
   public class UserService {
       private static final Logger logger = LogManager.getLogger(UserService.class);
       public void createUser(User user) {
           logger.info("User created: {}", user.getName()); // 违反规范（直接使用 Log4j2 实现类）
       }
   }
   ```

   ✅ 正例：

   ```java
   import org.slf4j.Logger;
   import org.slf4j.LoggerFactory;
   
   public class UserService {
       private static final Logger logger = LoggerFactory.getLogger(UserService.class);
       public void createUser(User user) {
           logger.info("User created: {}", user.getName()); // 正确（使用 SLF4J 门面）
       }
   }
   ```

2. **禁止打印 JSON 或对象**
   ❌ 反例：

   ```java
   public void processOrder(Order order) {
       logger.info("Order processed: {}", order.toJson()); // 违反规范（直接打印 JSON）
       logger.info("User info: {}", user.toString());      // 违反规范（直接打印对象）
   }
   ```

   ✅ 正例：

   ```java
   public void processOrder(Order order) {
       logger.info("Order processed: orderId={}, amount={}, status={}", 
                   order.getId(), order.getAmount(), order.getStatus()); // 拆分对象属性
   }
   ```

3. **敏感信息脱敏**
   ❌ 反例：

   ```java
   public void userLogin(String username, String password) {
       logger.info("Login attempt: username={}, password={}", username, password); // 违反规范（明文密码）
       logger.info("Card info: {}", creditCard.getNumber());                        // 违反规范（明文卡号）
   }
   ```

   ✅ 正例：

   ```java
   public void userLogin(String username, String password) {
       logger.info("Login attempt: username={}, password=******", username); // 密码脱敏
       logger.info("Card info: last4={}", maskCardNumber(creditCard.getNumber())); // 卡号脱敏
   }
   
   private String maskCardNumber(String cardNumber) {
       return cardNumber.length() > 4 
           ? "************" + cardNumber.substring(cardNumber.length() - 4) 
           : cardNumber;
   }
   ```


4. **避免字符串拼接**
   ❌ 反例：

   ```java
   public void processPayment(Payment payment) {
       logger.info("Payment processed: " + payment.getId() + ", amount: " + payment.getAmount()); // 字符串拼接
   }
   ```

   ✅ 正例：

   ```java
   public void processPayment(Payment payment) {
       logger.info("Payment processed: id={}, amount={}", payment.getId(), payment.getAmount()); // 使用占位符
   }
   ```

5. **使用英文打印**
   ❌ 反例：

   ```java
   logger.info("创建产品: {}", product.getName()); // 违反规范（中文日志）
   ```

   ✅ 正例：

   ```java
   logger.info("Product created: name={}, category={}", product.getName(), product.getCategory()); // 英文日志
   ```

6. **日志清晰明确**
   ❌ 反例：

   ```java
   logger.info("Something happened"); // 违反规范（描述模糊）
   logger.error("Operation failed");   // 违反规范（缺少上下文）
   ```

   ✅ 正例：

   ```java
   logger.info("Database connection established successfully"); // 明确描述
   logger.error("Failed to process order: orderId={}, error={}", orderId, ex.getMessage()); // 包含上下文
   ```

7. **日志配置（轮转机制）**
   ❌ 反例（log4j2.xml）：

   ```xml
   <Appenders>
       <File name="AppLog" fileName="/var/log/app.log">
           <PatternLayout pattern="%d %p %c{1.} [%t] %m%n"/>
       </File>
   </Appenders>
   ```

   ✅ 正例（log4j2.xml）：

   ```xml
   <Appenders>
       <RollingFile name="AppLog" fileName="/var/log/app.log" 
                    filePattern="/var/log/app-%d{yyyy-MM-dd}.log.gz">
           <PatternLayout pattern="%d %p %c{1.} [%t] %m%n"/>
           <Policies>
               <TimeBasedTriggeringPolicy/> <!-- 按时间轮转 -->
               <SizeBasedTriggeringPolicy size="100 MB"/> <!-- 单文件最大 100MB -->
           </Policies>
           <DefaultRolloverStrategy max="30"/> <!-- 最多保留 30 天日志 -->
       </RollingFile>
   </Appenders>
   ```


8. **异常信息记录**
    ❌ 反例：

    ```java
    try {
        processFile(filePath);
    } catch (IOException e) {
        logger.error("Failed to process file: " + e.getMessage()); // 违反规范（缺少堆栈）
    }
    ```

    ✅ 正例：

    ```java
    try {
        processFile(filePath);
    } catch (IOException e) {
        logger.error("Failed to process file: path={}", filePath, e); // 记录完整异常（含堆栈）
    }
    ```



9. **强烈禁止为了记录日志而修改业务逻辑**
      ❌ 反例：

   ```java
   // 原来的逻辑
   public String getUserInfo(Long userId) {
       return userService.findById(userId);;
   }
   
   public String getUserInfo(Long userId) {
       // 原本直接返回的逻辑被修改为了记录日志
       String result = userService.findById(userId);
       logger.info("User info retrieved: userId={}, result={}", userId, result); // 违反规范
       return result;
   }
   ```


10. **强烈禁止为了记录日志而进行try-catch，尤其是Controller类**
      ❌ 反例：

   ```java
   // 原来的逻辑
   public void processData() {
       businessService.process();
   }

   public void processData() {
       // 原本没有try-catch的逻辑被修改
       try {
           businessService.process();
           logger.info("Business process completed"); // 违反规范（为了日志添加try-catch）
       } catch (Exception e) {
           logger.error("Business process failed", e);
           throw e;
       }
   }
   ```


11. **禁止在每个方法开始和结束添加日志**
      ❌ 反例：

   ```java
   public void calculateTotal(List<Item> items) {
       logger.debug("Method calculateTotal started"); // 违反规范（方法开始日志）
       
       BigDecimal total = BigDecimal.ZERO;
       for (Item item : items) {
           total = total.add(item.getPrice());
       }
       
       logger.debug("Method calculateTotal finished"); // 违反规范（方法结束日志）
       return total;
   }
   ```

## 7. 日志修改实施规范

### 7.1 修改前准备

1. **统计需要修改的类清单**：
   - 列出所有需要添加或修改日志的类
   - 标注每个类的修改类型（新增日志/翻译中文）

### 7.2 修改内容分类

1. **中文日志翻译**：
   - 将现有中文日志使用 SLF4J 门面做标准化处理，删除@Slf4j注解
   - 将现有中文日志准确翻译为英文
   - 保持日志含义和上下文不变

2. **新增日志**：
   - 在异常处理位置添加日志
   - 在服务间调用处添加日志

### 7.3 修改约束

1. **严格约束**：
   - 无论原逻辑是否正确，都不能修改任何业务逻辑
   - 不能删除原有代码逻辑
   - 只能调整日志的输出内容和格式
   - 不得修改类和方法上的注释
   - 不得修改 // 或 /** */ 中的中文注释
   - 不能为了加日志而修改业务代码结构
   - 不能添加 try-catch 仅为了记录日志
   - 严禁添加或者修改注释

2. **特殊处理**：
   - 超过1000行的大文件先忽略，但记录到文档中
   - 复杂业务逻辑的日志添加需要额外评估

### 7.4 修改记录文档格式


```markdown
# 项目名称-日志修改记录

## 1. 进度统计

| 模块名称 | 文件总数 | 已完成 | 已跳过 | 待处理 |
|---------|---------|-------|-------|-------|
| 模块1 | 1 | 0 | 1 | 0 |
| 模块2 | 4 | 1 | 3 | 0 |
| 模块3 | 1 | 0 | 1 | 0 |
| 模块4 | 253 | 243 | 10 | 0 |
| **总计** | **259** | **244** | **15** | **0** |

## 2. 修改类清单

### 2.1 模块名-模块

#### 2.1.1 子模块名-模块

| 序号 | 类名 | 包路径 | 状态 |
|------|------|--------|------| 
| 1 | UserController | com.example.controller | ✅已完成 | 
| 2 | OrderController | com.example.controller | ❌已跳过 |
| 3 | CommonController | com.example.controller | ⏳待处理 |
...

```







































 