package com.anytech.anytxn.authorization.service.channel.visa;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ParmAuthCheckControlDTO;
import com.anytech.anytxn.authorization.base.domain.dto.TransVelocityStatisticsDTO;
import com.anytech.anytxn.authorization.mapper.transvelocitystatistics.TransVelocityStatisticsSelfMapper;
import com.anytech.anytxn.authorization.base.domain.model.TransVelocityStatistics;
import com.anytech.anytxn.authorization.base.service.auth.IAuthMatchRuleService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.service.batch.TransVelocityStatisticsJdbcService;
import com.anytech.anytxn.authorization.service.auth.AuthPrePostInfoModifyService;
import com.anytech.anytxn.authorization.service.auth.LimitRequestPrepareService;
import com.anytech.anytxn.authorization.service.channel.upi.preauth.UpiPreAuthDataUpdateServiceImpl;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.authorization.base.service.transaction.ITransVelocityLogService;
import com.anytech.anytxn.authorization.base.service.transaction.ITransVelocityStatisticsService;
import com.anytech.anytxn.authorization.base.utils.DateUtils;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.TransVelocityLogDTO;
import com.anytech.anytxn.business.base.card.constants.CardBusinessConstant;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.card.enums.CardMdesCustomerServiceCodeEnum;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.business.base.card.service.ICardMdesNotificationService;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.customer.domain.dto.BlockCodeMaintenanceLogDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.dao.customer.mapper.BlockCodeMaintenanceLogMapper;
import com.anytech.anytxn.business.dao.customer.model.BlockCodeMaintenanceLog;
import com.anytech.anytxn.limit.base.domain.dto.payload.CalLimitTrialResDTO;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.common.enums.ServerTypeEnum;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Visa  ---NEW
 * @ClassName AuthDataUpdate
 * @Description 未并账交易数据更新
 * <AUTHOR>
 * @date  2023/4/13 18:39
 * Version 1.0
 **/
@Service
public class VisaCheckAuthResponseCodeServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(VisaCheckAuthResponseCodeServiceImpl.class);

    private static final String AUTHORIZED_FLAG = "1";
    private static final String UN_AUTHORIZED_FLAG = "0";

    @Resource
    private VisaAuthDetailDataModifyServiceImpl visaAuthDetailDataModifyService;
    @Autowired
    private ITransVelocityStatisticsService transVelocityStatisticsService;
    @Resource
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    @Autowired
    private ITransVelocityLogService transVelocityLogService;
    @Autowired
    private UpiPreAuthDataUpdateServiceImpl upiPreAuthDataUpdateService;
    @Resource
    private BlockCodeMaintenanceLogMapper blockCodeMaintenanceLogMapper;
    @Autowired
    private LimitRequestPrepareService limitRequestPrepareService;
    @Resource
    private AuthCheckItemManager authCheckItemManager;
    @Autowired
    private TransVelocityStatisticsJdbcService transVelocityStatisticsJdbcService;
    @Autowired
    private TransVelocityStatisticsSelfMapper transVelocityStatisticsSelfMapper;
    @Autowired
    private IAuthMatchRuleService authMatchRuleService;
    @Autowired
    private IOutstandingTransService outstandingTransService;
    @Autowired
    private ICardMdesNotificationService cardMdesNotificationService;
    @Autowired
    private SequenceIdGen sequenceIdGen;
    @Autowired
    private AuthPrePostInfoModifyService authPrePostInfoService;

    /**
     * auth approve
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @return 异常:-2 拒绝:-1 通过成功:0
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicB(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        CardAuthorizationDTO cardAuthorizationInfo = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManagementInfoDTO = authorizationCheckProcessingPayload.getAccountManagementInfoDTO();
        CustomerAuthorizationInfoDTO customerAuthorizationInfoDTO = authorizationCheckProcessingPayload.getCustomerAuthorizationInfoDTO();
        List<TransVelocityLogDTO> addTransVelocityLogList = authorizationCheckProcessingPayload.getTransVelocityLogDtoS();
        List<ParmAuthCheckControlDTO> authCheckControlDTOList = authorizationCheckProcessingPayload.getParmAuthCheckControlDTOList();
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        //如果是大莱 本行， 并且是还款交易则不需要调用额度管控单元
        boolean sourceFlag = (AuthTransactionSourceCodeEnum.THE_BANK.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())
                    /*|| AuthTransactionSourceCodeEnum.VISA.getCode().equals(authRecordedDTO.getAuthTransactionSourceCode())*/)
                    && AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeTopCode());
        //管控单元的获取
        boolean normalTransFlag = AuthTransTypeEnum.NORMAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        boolean limitUpdateFlag = normalTransFlag && authorizationCheckProcessingPayload.getLimitCheckResult() == null;

        logger.info("Card number: {}, limit update result: {}, transaction identification result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), limitUpdateFlag, sourceFlag);
        if(!sourceFlag && limitUpdateFlag){
            //如果是普通非还款交易，调用交易路由规则获取交易管控单元参数->进而获取一组额度管控单元，注意如果配了额度检查项就不会走这一段逻辑
            logger.info("Calling authMatchRuleService.limitCtrlUnitOccupiedResult for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            authMatchRuleService.limitCtrlUnitOccupiedResult(orgInfo,
                    cardAuthorizationInfo, Optional.ofNullable(accountManagementInfoDTO).map(AccountManagementInfoDTO::getAccountManagementId).orElse(""), authRecordedDTO, customerAuthorizationInfoDTO);
            logger.info("authMatchRuleService.limitCtrlUnitOccupiedResult completed for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        }

        //流量流水表新增
        int res = transVelocityLogBatchInsert(addTransVelocityLogList);
        if(res != AuthConstans.ZERO){
            logger.error("Transaction velocity log batch insert failed, result: {}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR, AuthRepDetailEnum.AFM);
        }
        //撤销交易、冲正交易、撤销冲正交易、退货，流量检查表数据更新处理
        boolean velocityFlag = AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())||
                AuthTransTypeEnum.REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())||
                AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode());
        if(velocityFlag){
            res = velocityStatisticsProcess(authRecordedDTO);
            if(res != AuthConstans.ZERO){
                logger.error("Velocity statistics process failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.UFM);
            }
        }
        //卡首次使用日期更新
        res = cardFirstDateUpdate(cardAuthorizationInfo,authRecordedDTO);
        if(res != AuthConstans.ZERO){
            logger.error("Card first date update failed, result: {}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.CA_FU);
        }
        //sysflag判断
        if(systemInfo == null){
            logger.error("System info is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.SE);
        }
        final boolean flagCheck = (systemInfo.getLimitUpdateFlag() == null) || (systemInfo.getOutstandingLogFlag() == null)
                || (systemInfo.getAuthorizationLogFlag() == null) || (systemInfo.getCardAuthorizationUpdateFlag() == null);
        if(flagCheck){
            logger.error("System info flag check failed, limitUpdateFlag: {}, outstandingLogFlag: {}, authorizationLogFlag: {}, cardAuthorizationUpdateFlag: {}", 
                    systemInfo.getLimitUpdateFlag(), systemInfo.getOutstandingLogFlag(), systemInfo.getAuthorizationLogFlag(), systemInfo.getCardAuthorizationUpdateFlag());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.ST_E);
        }
        //更新未并账交易信息表
        if(AuthConstans.I.equals(systemInfo.getOutstandingLogFlag())){
            logger.info("Calling visaAuthDetailDataModifyService.outStandingTransModify for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            res = visaAuthDetailDataModifyService.outStandingTransModify(authorizationCheckProcessingPayload);
            logger.info("visaAuthDetailDataModifyService.outStandingTransModify completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), res);
            if(res != AuthConstans.ZERO){
                logger.error("Outstanding transaction modify failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_AB_TR);
            }
        }
        //只有卡服务记录授权预入账登记表
        if (ServerTypeEnum.CARD_SERVER.getCode().equals(authRecordedDTO.getServerType())) {
            logger.info("Calling authPrePostInfoService.modifyAuthPrePostInfo for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            int authPrePostRes = authPrePostInfoService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);
            logger.info("authPrePostInfoService.modifyAuthPrePostInfo completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), authPrePostRes);
            if (authPrePostRes != AuthConstans.ZERO) {
                logger.info("insert AuthPrePostLog error,cardNumber:{}", authRecordedDTO.getAuthCardNumber());
                logger.error("Auth pre post info modify failed, result: {}", authPrePostRes);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_AB_TR);
            }
        }
        //额度接口调用
        logger.info("Calling visaAuthDetailDataModifyService.limitUpdate for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        visaAuthDetailDataModifyService.limitUpdate(authorizationCheckProcessingPayload);
        logger.info("visaAuthDetailDataModifyService.limitUpdate completed for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        //授权流水表更新
        if(AuthConstans.I.equals(systemInfo.getAuthorizationLogFlag())){
            if(StringUtils.isBlank(authRecordedDTO.getAuthAuthCode())){
                authRecordedDTO.setAuthAuthCode(RandomStringUtils.randomNumeric(6));
            }
            if (StringUtils.isEmpty(authRecordedDTO.getAuthRetrievalReferenceNumber())) {
                authRecordedDTO.setAuthRetrievalReferenceNumber(DateUtils.getYear4AndDayOfYear()+RandomStringUtils.randomNumeric(8));
            }
            logger.info("Calling visaAuthDetailDataModifyService.modifyAuthorizationLog for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            res = visaAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
            logger.info("visaAuthDetailDataModifyService.modifyAuthorizationLog completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), res);
            if(res != AuthConstans.ONE){
                logger.error("Authorization log modify failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AU_FL);
            }
        }

        //更新过度用卡的相关字段
        buildCardAuthByCardOverUse(authorizationCheckProcessingPayload);

        //更新卡片授权信息(limit_check_indicator = 1) 满足限额检查标志和限额检查项
        if(AuthConstans.I.equals(systemInfo.getCardAuthorizationUpdateFlag())){
            if(StringUtils.isEmpty(cardAuthorizationInfo.getLimitCheckIndicator())){
                logger.error("Card authorization limit check indicator is empty");
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.U_CA_AU);
            }
            final boolean cardAuthorizationCheckFlag = (AuthConstans.AUTH_LIMIT_CHECK_INDICATOR_YES
                    .equals(cardAuthorizationInfo.getLimitCheckIndicator()))
                    && authCheckControlDTOList.stream().anyMatch(parmAuthCheckControlDTO ->
                    AuthCheckItemEnum.CARD_LIMIT.getCheckItem().equals(parmAuthCheckControlDTO.getCheckItem()));
            if (cardAuthorizationCheckFlag) {
                logger.info("Calling visaAuthDetailDataModifyService.buildCardAutByAuthLlimit for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
                visaAuthDetailDataModifyService.buildCardAutByAuthLlimit(authorizationCheckProcessingPayload);
                logger.info("visaAuthDetailDataModifyService.buildCardAutByAuthLlimit completed for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            }
        }
        //卡片授权信息表set pin cvv 密码检查清0
        VisaAuthDetailDataModifyServiceImpl.cardAuthorizationSet(cardAuthorizationInfo,authCheckControlDTOList);
//        UpiAuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(cardAuthorizationInfo,authRecordedDTO,orgInfo,true);
        logger.info("Calling visaAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        res = visaAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase(authorizationCheckProcessingPayload);
        logger.info("visaAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), res);
        if(res != AuthConstans.ZERO){
            logger.error("Card authorization modify in auth phase failed, result: {}", res);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AB_AIT);
        }

        //流量检查表数据更新
        List<TransVelocityStatisticsDTO> transVelocityStatisticsDtos = authorizationCheckProcessingPayload.getTransVelocityStatisticsDtoS();
        if(!CollectionUtils.isEmpty(transVelocityStatisticsDtos)){
            logger.info("Calling transVelocityStatisticsService.addOrUpdatePatch for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            res = transVelocityStatisticsService.addOrUpdatePatch(transVelocityStatisticsDtos);
            logger.info("transVelocityStatisticsService.addOrUpdatePatch completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), res);
            if(res != AuthConstans.ZERO){
                logger.error("Transaction velocity statistics update failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AB_UC);
            }
        }
        // 最小可用额度用于人工授权或强制授权
        String  detailCode =authRecordedDTO.getAuthTransactionTypeDetailCode();
        if(detailCode.endsWith("199")||detailCode.endsWith("299")){
            logger.info("Calling limitRequestPrepareService.getLimitAvailable for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            CalLimitTrialResDTO calLimitTrialResDTO =  limitRequestPrepareService.getLimitAvailable(authorizationCheckProcessingPayload);
            logger.info("limitRequestPrepareService.getLimitAvailable completed for cardNumber: {}, minAvailableAmount: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), calLimitTrialResDTO.getMinAvailableAmount());
            logger.info("Quota trial calculation, minAvailableAmount: {}", calLimitTrialResDTO.getMinAvailableAmount());
            authRecordedDTO.setAuthAvailableLimit(calLimitTrialResDTO.getMinAvailableAmount());
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * transvelocitylog
     * @param addTransVelocityLogList {@link TransVelocityLogDTO}
     * @return int approve:0,exception:-2
     */
    @BatchSharedAnnotation
    private int transVelocityLogBatchInsert(List<TransVelocityLogDTO> addTransVelocityLogList){
        if(!CollectionUtils.isEmpty(addTransVelocityLogList)){
            //批量插入
            addTransVelocityLogList.forEach(transVelocityLogDTO ->
                    transVelocityLogDTO.setId(sequenceIdGen.generateId(TenantUtils.getTenantId())));
            if (CustAccountBO.isBatch()) {
                addTransVelocityLogList.forEach(CustAccountBO.threadCustAccountBO.get().getAuthBO()::insertTransVelocityLog);
            } else {
                logger.info("Calling transVelocityLogService.insertBatch with {} records", addTransVelocityLogList.size());
                int res = transVelocityLogService.insertBatch(addTransVelocityLogList);
                logger.info("transVelocityLogService.insertBatch completed, result: {}", res);
                if(res < AuthConstans.ONE){
                    logger.error("Transaction velocity log batch insert failed, result: {}", res);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_INSERT_TRANS_VELOCITY_LOG_FAIL, AuthRepDetailEnum.BI_FC);
                }
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 撤销交易、冲正交易、撤销冲正交易，流量检查数据更新处理
     * @param authRecordedDTO {@link AuthRecordedDTO}
     * @return int
     */
    private int velocityStatisticsProcess(AuthRecordedDTO authRecordedDTO){
        if(StringUtils.isEmpty(authRecordedDTO.getAuthOriginalGlobalFlowNumber())){
            return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }
        String globalFlowNumber=authRecordedDTO.getAuthOriginalGlobalFlowNumber();
        logger.info("Calling transVelocityLogService.selectByAuthLogId for globalFlowNumber: {}", globalFlowNumber);
        List<TransVelocityLogDTO> transVelocityLogList = transVelocityLogService.selectByAuthLogId(globalFlowNumber);
        logger.info("transVelocityLogService.selectByAuthLogId completed, found {} records", transVelocityLogList.size());
        if(CollectionUtils.isEmpty(transVelocityLogList)){
            //为空不再操作数据
           return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
        }
        List<String> bizKeyList = transVelocityLogList.stream().map(
                TransVelocityLogDTO::getBizKey).collect(Collectors.toList());
        List<TransVelocityStatistics> velocityStatisticsList = CollectionUtils.isEmpty(bizKeyList) ? Collections.emptyList() :
            transVelocityStatisticsSelfMapper.selectByBizKeyList(bizKeyList);

        List<TransVelocityStatistics> transVelocityStatisticsList = new ArrayList<>(4);
        for(TransVelocityLogDTO transVelocityLog : transVelocityLogList) {
            TransVelocityStatistics transVelocityStatistics = null;
            String bizKey = transVelocityLog.getBizKey();
            String velocityCode = transVelocityLog.getVelocityCde();
            String authTxnTopCode = transVelocityLog.getAuthTxnTopCode();
            String postingTransactionCode = transVelocityLog.getPostingTransactionCode();
            LocalDate startDate = transVelocityLog.getStartDate();
            BigDecimal deltaAmount = transVelocityLog.getDeltaAmount();
            //visa部分撤销处理
            if(authRecordedDTO.getAuthReplaceAmountActualVisa() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualVisa();
            }
            //mc部分撤销处理
            if(authRecordedDTO.getAuthReplaceAmountActualMc() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualMc();
            }
            //jcb部分撤销处理
            if(authRecordedDTO.getAuthReplaceAmountActualJcb() != null){
                deltaAmount = authRecordedDTO.getAuthReplaceAmountActualJcb();
            }

            if(StringUtils.isEmpty(bizKey)||StringUtils.isAllEmpty(velocityCode,postingTransactionCode)||startDate == null){
                logger.error("Velocity statistics process parameters are null, bizKey: {}, velocityCode: {}, postingTransactionCode: {}, startDate: {}", bizKey, velocityCode, postingTransactionCode, startDate);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL);
            }
            if (!CollectionUtils.isEmpty(velocityStatisticsList)){
                List<TransVelocityStatistics> velocityStatistics = velocityStatisticsList.stream().filter(x -> x.getBizKey().equals(bizKey)
                        && (x.getVelocityCde().equals(velocityCode) || x.getPostingTransactionCode().equals(postingTransactionCode)) && x.getStartDate().equals(startDate)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(velocityStatistics)){
                    transVelocityStatistics = velocityStatistics.get(0);
                }
            }
            if(transVelocityStatistics == null){
                logger.error("Transaction velocity statistics not found for bizKey: {}", bizKey);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_SELECT_TRANS_VELOCITY_STATISTICS_FAIL);
            }
            if(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
                transVelocityStatistics.setPeriodTxnCount(transVelocityStatistics.getPeriodTxnCount() + 1);
                transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().add(deltaAmount));
            }else {
                logger.info("Calling outstandingTransService.getOutstandingTransactionByGlobalFlowNumber for globalFlowNumber: {}", globalFlowNumber);
                OutstandingTransactionDTO outstandingTransactionDTO = outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(
                        globalFlowNumber, authRecordedDTO.getOrganizationNumber());
                logger.info("outstandingTransService.getOutstandingTransactionByGlobalFlowNumber completed for globalFlowNumber: {}", globalFlowNumber);
                BigDecimal billingAmount = outstandingTransactionDTO.getBillingAmount() == null? BigDecimal.ZERO : outstandingTransactionDTO.getBillingAmount();
                BigDecimal amountReturned = outstandingTransactionDTO.getAmountReturned() == null? BigDecimal.ZERO : outstandingTransactionDTO.getAmountReturned();
                int count = billingAmount.compareTo(
                        authRecordedDTO.getAuthTransactionAmount().add(amountReturned));
                //冲正、退货的金额和原交易相同则次数减1，扣除金额，不相同则只扣除金额
                if (count == 0) {
                    logger.info("Reverse all amounts and subtract 1 from the cumulative transaction count.");
                    transVelocityStatistics.setPeriodTxnCount(transVelocityStatistics.getPeriodTxnCount() - 1);
                    transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().subtract(authRecordedDTO.getAuthTransactionAmount()));
                }else {
                    transVelocityStatistics.setPeriodTxnAmount(transVelocityStatistics.getPeriodTxnAmount().subtract(authRecordedDTO.getAuthTransactionAmount()));
                }
            }
            transVelocityStatisticsList.add(transVelocityStatistics);
        }
        if (!CollectionUtils.isEmpty(transVelocityStatisticsList)){
            transVelocityStatisticsJdbcService.batchUpdateTransVelocityStatistics(transVelocityStatisticsList);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 首次用卡更新
     * @param cardAuthorizationDTO {@link CardAuthorizationDTO }
     * @return int
     */
    @BatchSharedAnnotation
    private int cardFirstDateUpdate(CardAuthorizationDTO cardAuthorizationDTO, AuthRecordedDTO authRecordedDTO){
        boolean flag = (cardAuthorizationDTO.getFstUsgDte() == null
                || AuthConstans.FST_USG_DTE
                .equals(new SimpleDateFormat("yyyy-MM-dd").format( Date.from(cardAuthorizationDTO.getFstUsgDte().atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        if (flag) {
            cardAuthorizationDTO.setFstUsgDte(LocalDate.now());

            CardAuthorizationInfo authorizationInfo = BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class);
            authorizationInfo.setUpdateTime(LocalDateTime.now());

            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
            } else {
                int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(authorizationInfo);
                if(res != 1){
                    logger.error("Card first date update failed, result: {}", res);
                    throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.DUF);
                }
            }
            authRecordedDTO.setFirstUsed(true);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }


    /**
     * 建立委托 无卡自助支付开关更新
     * @param cardAuthorizationDTO {@link CardAuthorizationDTO }
     * @return int
     */
    public int updateCardNotPresentSwitch(CardAuthorizationDTO cardAuthorizationDTO, boolean isAuthorized){
        logger.info("Judgment of cardless self-service switch, cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
        String isAuthorizedNow = cardAuthorizationDTO.getCardNotPresentSwitch();
        if (isAuthorized) {
            if(!AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                cardAuthorizationDTO.setCardNotPresentSwitch(AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }else{
            cardAuthorizationDTO.setCardNotPresentSwitch(UN_AUTHORIZED_FLAG);
            this.updateCardAuthInfoNomal(cardAuthorizationDTO);
        }

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 建立委托冲正 无卡自助支付开关更新
     * @param authorizationCheckProcessingPayload {@link CardAuthorizationDTO }
     * @return int
     */
    public int updateCardNotPresentSwitchReverse(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload,boolean isAuthorized){
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        String isAuthorizedNow = cardAuthorizationDTO.getCardNotPresentSwitch();
        if (isAuthorized){
            if (AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                cardAuthorizationDTO.setCardNotPresentSwitch(UN_AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }else{
            if (!AUTHORIZED_FLAG.equals(isAuthorizedNow)){
                return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
            }else {
                cardAuthorizationDTO.setCardNotPresentSwitch(AUTHORIZED_FLAG);
                this.updateCardAuthInfoNomal(cardAuthorizationDTO);
            }
        }

        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 修改卡片授权信息通用处理
     */
    @BatchSharedAnnotation
    private int updateCardAuthInfoNomal(CardAuthorizationDTO cardAuthorizationDTO){
        CardAuthorizationInfo authorizationInfo = BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class);
        authorizationInfo.setUpdateTime(LocalDateTime.now());
        authorizationInfo.setUpdateBy("SYSTERM");
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
        } else {
            int res = cardAuthorizationInfoMapper.updateByPrimaryKeySelective(authorizationInfo);
            if(res != 1){
                logger.error("Card authorization info update failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.DUF);
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * auth reject
     * @return int
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicC(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        logger.info("Authorization denied, response code: {}", authRecordedDTO.getAuthResponseCode());
        //cardAuthorizationDTO 不存在
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        if(cardAuthorizationDTO == null){
            logger.error("Card authorization information does not exist!");
            return AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode();
        }
        List<ParmAuthCheckControlDTO> authCheckControlDTOList = authorizationCheckProcessingPayload.getParmAuthCheckControlDTOList();
        logger.info("Calling authCheckItemManager.getAccountManagementInfoDTO");
        AccountManagementInfoDTO accountManageInfo = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);
        logger.info("authCheckItemManager.getAccountManagementInfoDTO completed");
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        //拆箱时候判断空
        int pinTriesCount = cardAuthorizationDTO.getPinTriesCount() == null ? 0:cardAuthorizationDTO.getPinTriesCount();
        int cvvTriesCount = cardAuthorizationDTO.getCvvTriesCount() == null ? 0:cardAuthorizationDTO.getCvvTriesCount();
        int cvv2TriesCount = cardAuthorizationDTO.getCvv2TriesCount() == null ? 0:cardAuthorizationDTO.getCvv2TriesCount();
        //获取授权检查参数
        AuthorizationRuleDTO authorizationRuleDTO = authorizationCheckProcessingPayload.getAuthorizationRuleDTO();
        int pinErrCnt = 0;
        int cvv2ErrCnt = 0;
        int cvvErrCnt = 0;
        if(authorizationRuleDTO != null){
            pinErrCnt  = authorizationRuleDTO.getMaxPinErrCnt();
            logger.info("pin:{}",pinErrCnt);
            cvv2ErrCnt = authorizationRuleDTO.getMaxCvv2ErrCnt();
            logger.info("cvv2:{}",cvv2ErrCnt);
            cvvErrCnt  = authorizationRuleDTO.getMaxCvvErrCnt();
            logger.info("cvv:{}",cvvErrCnt);
        }
        //撤销交易
        if(AuthTransTypeEnum.REVOCATION_TRANS.getCode().equals(authRecordedDTO.getAuthTransactionTypeCode())){
            LocalDate lastStateDate = accountManageInfo.getLastStatementDate();
            LocalDate cycleSttDte;
            if (lastStateDate != null) {
                cycleSttDte = lastStateDate.plusDays(1);
            } else {
                cycleSttDte = accountManageInfo.getOpenDate();
            }
            LocalDate cleCtrSttDte = cardAuthorizationDTO.getCleCtrSttDte();
            LocalDate dailyCtrSttDte = cardAuthorizationDTO.getDlyCtrSttDte();
            Integer dcrAthCleNbr = cardAuthorizationDTO.getDceAthCleNbr();
            Integer dceAthDlyNbr = cardAuthorizationDTO.getDceAthDlyNbr();
            if(cycleSttDte.equals(cleCtrSttDte)) {
                cardAuthorizationDTO.setDceAthCleNbr(dcrAthCleNbr-1);
            }
            if(Objects.equals(dailyCtrSttDte, orgInfo.getNextProcessingDay())){
                cardAuthorizationDTO.setDceAthDlyNbr(dceAthDlyNbr - 1);
            }
            if (CustAccountBO.isBatch()) {
                CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
            } else {
                cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class));
            }
        }
        //密码、CVV、CVV2，检查 如果变为N0插入卡片封锁码维护历史表
        BlockCodeMaintenanceLog blockCodeMaintenanceLog = new BlockCodeMaintenanceLog();
        blockCodeMaintenanceLog.setLogId(sequenceIdGen.generateId(TenantUtils.getTenantId()));
        blockCodeMaintenanceLog.setKey(cardAuthorizationDTO.getCardNumber());
        blockCodeMaintenanceLog.setKeyType(CardBusinessConstant.KEY_TYPE_B);
        blockCodeMaintenanceLog.setBranchNumber(cardAuthorizationDTO.getOrganizationNumber());
        blockCodeMaintenanceLog.setBlockCodeBefore(cardAuthorizationDTO.getBlockCode());
        blockCodeMaintenanceLog.setBlockCodeDateBefore(cardAuthorizationDTO.getBlockCodeDate());
        blockCodeMaintenanceLog.setPreviousBlockCodeBefore(cardAuthorizationDTO.getPreviousBlock());
        blockCodeMaintenanceLog.setPreviousBlockDateBefore(cardAuthorizationDTO.getPreviousBlockDate());
        blockCodeMaintenanceLog.setPreBlockStopDateBefore(cardAuthorizationDTO.getPreviousBlockStopDate());
        //密码、CVV、CVV2，检查
        if (!CollectionUtils.isEmpty(authCheckControlDTOList)) {
            logger.info("Processing auth check control list, size: {}", authCheckControlDTOList.size());
            for (ParmAuthCheckControlDTO parmAuthCheckControlDto : authCheckControlDTOList) {
                if (parmAuthCheckControlDto != null && parmAuthCheckControlDto.getCheckItem() != null) {
                    AuthCheckItemEnum authCheckItemEnum = AuthCheckItemEnum.getEnum(parmAuthCheckControlDto.getCheckItem());
                    Objects.requireNonNull(authCheckItemEnum,"authCheckItemEnum不能为空");
                    switch (authCheckItemEnum) {
                        case CVV:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setCvvTriesCount(cvvTriesCount+1);
                                if(cvvTriesCount+1 >= cvvErrCnt){
                                    logger.info("CVV error count exceeded limit, adding block code");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxCvvErrBlk());
                                        logger.info("Calling cardMdesNotificationService.notificationByCardUpdate for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                        logger.info("cardMdesNotificationService.notificationByCardUpdate completed for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                    }
                                }
                            }
                            break;
                        case CVV2:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setCvv2TriesCount(cvv2TriesCount+1);
                                if(cvv2TriesCount+1 >= cvv2ErrCnt){
                                    logger.info("CVV2 error count exceeded limit, adding block code");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxCvv2ErrBlk());
                                        logger.info("Calling cardMdesNotificationService.notificationByCardUpdate for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                        logger.info("cardMdesNotificationService.notificationByCardUpdate completed for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                    }
                                }
                            }
                            break;
                        case PASS_WORD:
                            if(AuthConstans.AUTH_CHECK_RESULT_REJECT.equals(parmAuthCheckControlDto.getCheckResult())){
                                cardAuthorizationDTO.setPinTriesCount(pinTriesCount+1);
                                if(pinTriesCount+1 > 3){
                                    logger.info("PIN error count exceeded limit, adding block code");
                                    if (authorizationRuleDTO != null){
                                        cardAuthorizationDTO.setBlockCode(authorizationRuleDTO.getMaxPinErrBlk());
                                        logger.info("Calling cardMdesNotificationService.notificationByCardUpdate for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                        cardMdesNotificationService.notificationByCardUpdate(cardAuthorizationDTO.getCardNumber(), CardMdesCustomerServiceCodeEnum.SUSPEND.getCode());
                                        logger.info("cardMdesNotificationService.notificationByCardUpdate completed for cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
                                    }
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        //过度用卡检查拒绝
        //更新过度用卡的相关字段
        buildCardAuthByCardOverUse(authorizationCheckProcessingPayload);
        logger.info("Authorization denied, cardNumber: {}", DesensitizedUtils.bankCard(cardAuthorizationDTO.getCardNumber()));
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getAuthBO().updateCardAuthorizationMap(cardAuthorizationDTO);
        } else {
            cardAuthorizationInfoMapper.updateByPrimaryKeySelective(BeanMapping.copy(cardAuthorizationDTO, CardAuthorizationInfo.class));
        }
        blockCodeMaintenanceLog.setBlockCodeAfter(cardAuthorizationDTO.getBlockCode());
        blockCodeMaintenanceLog.setBlockCodeDateAfter(orgInfo.getToday());
        blockCodeMaintenanceLog.setPreviousBlockCodeAfter(cardAuthorizationDTO.getPreviousBlock());
        blockCodeMaintenanceLog.setPreBlockDateAfter(cardAuthorizationDTO.getPreviousBlockDate());
        blockCodeMaintenanceLog.setPreBlockStopDateAfter(cardAuthorizationDTO.getPreviousBlockStopDate());
        blockCodeMaintenanceLog.setCreateTime(LocalDateTime.now());
        blockCodeMaintenanceLog.setUpdateTime(LocalDateTime.now());
        blockCodeMaintenanceLog.setUpdateBy("SYSTEM");
        blockCodeMaintenanceLog.setVersionNumber(1);
        try{
            if(AuthConstans.BLOCKCODE.equals(cardAuthorizationDTO.getBlockCode())){
                if (CustAccountBO.isBatch()) {
                    CustAccountBO.threadCustAccountBO.get().insertBlockCodeMaintenanceLogList(BeanMapping.copy(blockCodeMaintenanceLog, BlockCodeMaintenanceLogDTO.class));
                } else {
                    blockCodeMaintenanceLogMapper.insertSelective(blockCodeMaintenanceLog);
                }
            }
        }catch (Exception e){
            logger.error("Failed to insert the blocked code into the maintenance table during authorization check for password, CVV, and CVV2.", e);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATABASE_ERROR,e);
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * visa 逻辑D ATM/POS查询
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * <AUTHOR>
     * @date 2019/04/09
     */
    @BatchSharedAnnotation
    public void authDataUpdateLogicD(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        CardAuthorizationDTO cardAuthorizationInfo = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();

        // 调用额度试算接口
        logger.info("Calling limitRequestPrepareService.getLimitAvailable for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        CalLimitTrialResDTO calLimitTrialResDTO =  limitRequestPrepareService.getLimitAvailable(authorizationCheckProcessingPayload);
        logger.info("limitRequestPrepareService.getLimitAvailable completed for cardNumber: {}, minAvailableAmount: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), calLimitTrialResDTO.getMinAvailableAmount());
        authRecordedDTO.setAuthAvailableLimit(calLimitTrialResDTO.getMinAvailableAmount());
//        visaAuthDetailDataModifyService.modifyCardAuthorizationInAuthPhase(authorizationCheckProcessingPayload);
        // 卡片限额开启
        if(LimitCheckIndicatorEnum.OPEN.getCode().equals(cardAuthorizationInfo.getLimitCheckIndicator())){
            //计算卡层面可用额度
            BigDecimal availableRetailAmountCard = cardAuthorizationDTO.getBillingCycleRetailLimit()
                    .subtract(cardAuthorizationDTO.getBillingCycleRetailAmount());
            BigDecimal availableCashAmountCard = cardAuthorizationDTO.getBillingCycleCashLimit()
                    .subtract(cardAuthorizationDTO.getBillingCycleCashAmount());
            //附加金额（authAdditionalAmounts）字段
            StringBuilder authAdditionalAmounts = new StringBuilder();
            authAdditionalAmounts.append(authRecordedDTO.getAuthProcessingCode(), 2, 4);
//            authAdditionalAmounts.append("30");
            if (StringUtils.equalsAny(authRecordedDTO.getAuthProcessingCode().substring(2, 4), "10", "20", "40")) {
                authAdditionalAmounts.append(AuthConstans.ZERO_TWO);
            } else {
                authAdditionalAmounts.append(AuthConstans.ZERO_ONE);
            }
            authAdditionalAmounts.append(authRecordedDTO.getAuthBillingCurrencyCode());
            // POS查询
            if (TranTypeDetailEnum.POS.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
               /* if (!authRecordedDTO.getAuthTransactionCurrencyCode().equals(cardAuthorizationDTO.getAccount1Currency())) {
                    // 汇率转换逻辑
                    availableRetailAmountCard = lmtCommon.getCurrencyRate(cardAuthorizationInfo.getOrganizationNumber(),cardAuthorizationDTO.getAccount1Currency(),
                            authRecordedDTO.getAuthTransactionCurrencyCode(),availableRetailAmountCard);
                }*/
                //卡层可用额度与客户层额度比较 哪个小取哪个
                BigDecimal availableRetailAmount = calLimitTrialResDTO.getMinAvailableAmount().compareTo(availableRetailAmountCard) < 0 ? calLimitTrialResDTO.getMinAvailableAmount(): availableRetailAmountCard;
                //正值为C负值为D
                if (availableRetailAmount.compareTo(BigDecimal.ZERO) < 0) {
                    authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
                } else {
                    authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
                }
                //赋值最小可用金额
                authAdditionalAmounts.append(format12Amount(availableRetailAmount.abs()));
                //附加金额（authAdditionalAmounts）字段
                authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
            } else if (TranTypeDetailEnum.ATM.getCode().equals(authRecordedDTO.getAuthTransactionTypeDetailCode())) {
               /* if (!authRecordedDTO.getAuthTransactionCurrencyCode().equals(cardAuthorizationDTO.getAccount1Currency())) {
                    //汇率 转换
                    availableCashAmountCard = lmtCommon.getCurrencyRate(AuthConstans.DEFAULT_ORG_NUMBER,cardAuthorizationDTO.getAccount1Currency(),
                            authRecordedDTO.getAuthTransactionCurrencyCode(),availableCashAmountCard);
                }*/
                //卡层可用额度与客户层额度比较 哪个小取哪个
                BigDecimal availableCashAmount = calLimitTrialResDTO.getMinAvailableAmount().compareTo(availableCashAmountCard) < 0 ? calLimitTrialResDTO.getMinAvailableAmount(): availableCashAmountCard;
                //正值为C负值为D
                if (availableCashAmount.compareTo(BigDecimal.ZERO) < 0) {
                    authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
                } else {
                    authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
                }
                //赋值最小可用金额
                authAdditionalAmounts.append(format12Amount(availableCashAmount.abs()));
                //附加金额（authAdditionalAmounts）字段
                authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
            }
        }else {
            //附加金额（authAdditionalAmounts）字段
            StringBuilder authAdditionalAmounts = new StringBuilder();
            authAdditionalAmounts.append(authRecordedDTO.getAuthProcessingCode(), 2, 4);
//            authAdditionalAmounts.append("30");
            if (StringUtils.equalsAny(authRecordedDTO.getAuthProcessingCode().substring(2, 4), "10", "20", "40")) {
                authAdditionalAmounts.append(AuthConstans.ZERO_TWO);
            } else {
                authAdditionalAmounts.append(AuthConstans.ZERO_ONE);
            }
            authAdditionalAmounts.append(authRecordedDTO.getAuthBillingCurrencyCode());
            //正值为C负值为D
            if (calLimitTrialResDTO.getMinAvailableAmount().compareTo(BigDecimal.ZERO) < 0) {
                authAdditionalAmounts.append(PositiveNegativeEnum.NEGATIVE.getCode());
            } else {
                authAdditionalAmounts.append(PositiveNegativeEnum.POSITIVE.getCode());
            }
            //赋值最小可用金额
            authAdditionalAmounts.append(format12Amount(calLimitTrialResDTO.getMinAvailableAmount().abs()));
            //附加金额（authAdditionalAmounts）字段
            authRecordedDTO.setAuthAdditionalAmounts(authAdditionalAmounts.toString());
        }
        //恢复大类为查询交易
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.QUERY_TRANS.getCode());
        logger.info("POS/ATM query function processing completed");
        //插入授权流水表
        logger.info("Calling visaAuthDetailDataModifyService.modifyAuthorizationLog for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
        visaAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
        logger.info("visaAuthDetailDataModifyService.modifyAuthorizationLog completed for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
    }

    /**
     * 查询类冲正交易更新数据
     * @param authorizationCheckProcessingPayload
     * @return
     */
    @BatchSharedAnnotation
    public int authDataUpdateLogicInquiry(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        SystemTableDTO systemInfo = authorizationCheckProcessingPayload.getSystemInfo();
        int res = 0;
        //sysflag判断
        if(systemInfo == null){
            logger.error("System info is null");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.SE);
        }
        final boolean flagCheck = (systemInfo.getLimitUpdateFlag() == null) || (systemInfo.getOutstandingLogFlag() == null)
                || (systemInfo.getAuthorizationLogFlag() == null) || (systemInfo.getCardAuthorizationUpdateFlag() == null);
        if(flagCheck){
            logger.error("System info flag check failed, limitUpdateFlag: {}, outstandingLogFlag: {}, authorizationLogFlag: {}, cardAuthorizationUpdateFlag: {}", 
                    systemInfo.getLimitUpdateFlag(), systemInfo.getOutstandingLogFlag(), systemInfo.getAuthorizationLogFlag(), systemInfo.getCardAuthorizationUpdateFlag());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.ST_E);
        }
        //授权流水表更新
        if(AuthConstans.I.equals(systemInfo.getAuthorizationLogFlag())){
            logger.info("Calling visaAuthDetailDataModifyService.modifyAuthorizationLog for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            res = visaAuthDetailDataModifyService.modifyAuthorizationLog(authRecordedDTO);
            logger.info("visaAuthDetailDataModifyService.modifyAuthorizationLog completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), res);
            if(res != AuthConstans.ONE){
                logger.error("Authorization log modify failed, result: {}", res);
                throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.S_DATA_UPDATE_FAIL, AuthRepDetailEnum.AU_FL);
            }
        }
        return AuthItemCheckResCodeEnum.APPROVE_CODE.getCode();
    }

    /**
     * 金额12位格式化
     * @param amount
     * @return
     */
    private String format12Amount(BigDecimal amount) {
        BigDecimal addAmount = amount.multiply(new BigDecimal(AuthConstans.DIVIDE_100));
        return String.valueOf(String.format("%012d",addAmount.intValue()));
    }

    /**
     * 预授权数据更新
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     * @return int
     */
    @BatchSharedAnnotation
    public int preAuthUpdate(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        //预授权请求,撤销,冲正,撤销冲正
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        if(authRecordedDTO.getPreAuth()){
            logger.info("Calling upiPreAuthDataUpdateService.preAuthRequestDataUpdate for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            int result = upiPreAuthDataUpdateService.preAuthRequestDataUpdate(authorizationCheckProcessingPayload);
            logger.info("upiPreAuthDataUpdateService.preAuthRequestDataUpdate completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), result);
            return result;
        }
        //预授权完成,撤销,冲正,撤销冲正
        if(authRecordedDTO.getPreAuthComplete()){
            logger.info("Calling upiPreAuthDataUpdateService.preAuthFinishDataUpdate for cardNumber: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()));
            int result = upiPreAuthDataUpdateService.preAuthFinishDataUpdate(authorizationCheckProcessingPayload);
            logger.info("upiPreAuthDataUpdateService.preAuthFinishDataUpdate completed for cardNumber: {}, result: {}", DesensitizedUtils.bankCard(authRecordedDTO.getAuthCardNumber()), result);
            return result;
        }
        return 0;
    }


    /**
     * 构建过度用卡相关字段
     * @param authorizationCheckProcessingPayload {@link AuthorizationCheckProcessingPayload}
     */
    private void buildCardAuthByCardOverUse(AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload) {
        AuthRecordedDTO authRecordedDTO = authorizationCheckProcessingPayload.getAuthRecordedDTO();
        CardAuthorizationDTO cardAuthorizationDTO = authorizationCheckProcessingPayload.getCardAuthorizationDTO();
        AccountManagementInfoDTO accountManageInfo = authCheckItemManager.getAccountManagementInfoDTO(authorizationCheckProcessingPayload);
        OrganizationInfoResDTO orgInfo = authorizationCheckProcessingPayload.getOrgInfo();
        LocalDate cleCtrSttDte = cardAuthorizationDTO.getCleCtrSttDte();
        LocalDate dlyCtrSttDte = cardAuthorizationDTO.getDlyCtrSttDte();
        LocalDate cycleSttDte;
        LocalDate lastStateDate = accountManageInfo.getLastStatementDate();
        if (lastStateDate != null) {
            cycleSttDte = lastStateDate.plusDays(1);
        } else {
            cycleSttDte = accountManageInfo.getOpenDate();
        }
        LocalDate dailySttDte = orgInfo.getNextProcessingDay();
        int aprAthCleNbr = cardAuthorizationDTO.getAprAthCleNbr()==null
                ? 0:cardAuthorizationDTO.getAprAthCleNbr();
        int dceAthCleNbr = cardAuthorizationDTO.getDceAthCleNbr()==null
                ? 0:cardAuthorizationDTO.getDceAthCleNbr();
        int aprAthDlyNbr = cardAuthorizationDTO.getAprAthDlyNbr()==null
                ? 0:cardAuthorizationDTO.getAprAthDlyNbr();
        int dceAthDlyNbr = cardAuthorizationDTO.getDceAthDlyNbr()==null
                ? 0:cardAuthorizationDTO.getDceAthDlyNbr();
        if (AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_300.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_400.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_600.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.APPROVED_BY_XPRESS.getCode().equals(authRecordedDTO.getAuthResponseCode())
                || AuthResponseCodeEnum.ACCEPTED_800.getCode().equals(authRecordedDTO.getAuthResponseCode())) {
            if (cycleSttDte.equals(cleCtrSttDte)){
                cardAuthorizationDTO.setAprAthCleNbr(aprAthCleNbr + AuthConstans.ONE);
            }else {
                cardAuthorizationDTO.setAprAthCleNbr(1);
                cardAuthorizationDTO.setCleCtrSttDte(cycleSttDte);
            }
            if (dailySttDte.equals(dlyCtrSttDte)){
                cardAuthorizationDTO.setAprAthDlyNbr(aprAthDlyNbr + AuthConstans.ONE);
            }else {
                cardAuthorizationDTO.setAprAthDlyNbr(1);
                cardAuthorizationDTO.setDlyCtrSttDte(dailySttDte);
            }
        }else{
            if (cycleSttDte.equals(cleCtrSttDte)){
                cardAuthorizationDTO.setDceAthCleNbr(dceAthCleNbr + 1);
            }else {
                cardAuthorizationDTO.setDceAthCleNbr(1);
                cardAuthorizationDTO.setCleCtrSttDte(cycleSttDte);
            }
            if (dailySttDte.equals(dlyCtrSttDte)){
                cardAuthorizationDTO.setDceAthDlyNbr(dceAthDlyNbr + 1);
            }else {
                cardAuthorizationDTO.setDceAthDlyNbr(1);
                cardAuthorizationDTO.setDlyCtrSttDte(dailySttDte);
            }
        }
    }
}
