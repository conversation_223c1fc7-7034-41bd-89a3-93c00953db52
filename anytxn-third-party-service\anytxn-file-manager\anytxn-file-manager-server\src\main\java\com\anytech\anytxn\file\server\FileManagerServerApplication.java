package com.anytech.anytxn.file.server;

import com.anytech.anytxn.common.core.annotation.EnableCacheAnnotation;
import com.anytech.anytxn.file.EnableFileManagerApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * @description: 文件管理启动类
 * @author: zhangnan
 * @create: 2021-03-18
 **/
@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.batch.BatchAutoConfiguration.class
})
@EnableFileManagerApi
@EnableCacheAnnotation
@ComponentScan(basePackages = {
        "com.anytech.anytxn.common",
        "com.anytech.anytxn.file"
})
public class FileManagerServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(FileManagerServerApplication.class, args);
    }
}
