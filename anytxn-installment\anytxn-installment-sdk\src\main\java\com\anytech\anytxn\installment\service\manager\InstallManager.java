package com.anytech.anytxn.installment.service.manager;

import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.installment.base.domain.dto.InstallEntryDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.authorization.base.enums.ActivationCodeEnum;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoSelfMapper;
import com.anytech.anytxn.business.dao.card.model.CardAuthorizationInfo;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.service.IInstallProductInfoService;
import com.anytech.anytxn.transaction.base.enums.CardStatusEnum;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.ManagedBean;
import java.security.SecureRandom;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: sukang
 * @Date: 2021/8/4 17:00
 *
 * 公共逻辑,管理整个分期
 */
@ManagedBean
public class InstallManager {

    private static final Logger logger = LoggerFactory.getLogger(InstallManager.class);

    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;

    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;

    @Autowired
    private CardAuthorizationInfoSelfMapper cardAuthorizationInfoSelfMapper;

    @Autowired
    private IInstallProductInfoService installProductInfoService;



    public void getInstallmentProductDesc(InstallEntryDTO installEntryDTO){
        if (StringUtils.isBlank(installEntryDTO.getTransactionDesc())){
            logger.info("Calling installProductInfoService.findByIndex with org={}, productCode={}", OrgNumberUtils.getOrg(), installEntryDTO.getProductCode());
            InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(OrgNumberUtils.getOrg(), installEntryDTO.getProductCode());
            logger.info("installProductInfoService.findByIndex completed: productCode={}", installEntryDTO.getProductCode());
            installEntryDTO.setTransactionDesc(installProConf.getProductDesc());
        }
    }

    public String getInstallmentProductDesc(String productCode){
        try {
            if (StringUtils.isBlank(productCode)){
                logger.info("Calling installProductInfoService.findByIndex with org={}, productCode={}", OrgNumberUtils.getOrg(), productCode);
                InstallProductInfoResDTO installProConf = installProductInfoService.findByIndex(OrgNumberUtils.getOrg(), productCode);
                logger.info("installProductInfoService.findByIndex completed: productCode={}", productCode);
                return installProConf.getProductDesc();
            }
        } catch (Exception e) {
            logger.error("Failed to get installment product description for productCode={}", productCode, e);
            return "";
        }
        return "";
    }


    /**
     * 如果不传卡号的话
     *   1.根据管理账户id获取客户号,再根据客户号获取已激活的卡片列表
     *   2.从已激活的卡片列表中 获取卡状态为 0,1,2的随机一张卡片
     * @param installEntryDTO {@link InstallEntryDTO}
     * @return 卡号
     */
    public String getCardNumberByAccountManagementId(InstallEntryDTO installEntryDTO){
        if (StringUtils.isNotBlank(installEntryDTO.getCardNumber())){
            return installEntryDTO.getCardNumber();
        }

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(installEntryDTO.getAccountManagementId());

        if (accountManagementInfo == null){
            logger.error("Account management info not found for accountManagementId={}", installEntryDTO.getAccountManagementId());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_NULL_FAULT, InstallRepDetailEnum.BI_IN_TR);
        }
        //获取客户号
        String customerId = accountManagementInfo.getCustomerId();
        //根据客户号获取卡号列表后在进行筛选
        List<CardAuthorizationInfo> authorizationInfos = cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(installEntryDTO.getOrganizationNumber(), customerId)
                .stream()
                .filter(t -> !Objects.equals(t.getActivationCode(), ActivationCodeEnum.INACTIVATED.getCode()))
                .filter(t -> StringUtils.equalsAny(t.getStatus(),
                        CardStatusEnum.NEW_STATUS.getCode(),CardStatusEnum.ACTIVE_STATUS.getCode(),CardStatusEnum.STATIC_STATUS.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(authorizationInfos)){
            logger.error("No valid card found for customerId={}, organizationNumber={}", customerId, installEntryDTO.getOrganizationNumber());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.NO_VALID_CARD_NUMBER);
        }

        SecureRandom secureRandom = new SecureRandom();
        int nextInt = secureRandom.nextInt(authorizationInfos.size());
        return authorizationInfos.get(nextInt).getCardNumber();
    }



    public static Boolean isFirstTerm(String balanceMethod){
        return Objects.equals("0", balanceMethod);
    }

    public static Boolean isLastTerm(String balanceMethod){
        return Objects.equals("1", balanceMethod);
    }


    /**
     *
     * @param accountManagerId 管理账号id
     * @return  卡片列表
     */
    public List<String> getCardNumberList(String accountManagerId){
        if (StringUtils.isBlank(accountManagerId)){
            return Collections.emptyList();
        }

        AccountManagementInfo accountManagementInfo = accountManagementInfoMapper.selectByPrimaryKey(accountManagerId);
        if (accountManagementInfo == null){
            return Collections.emptyList();
        }

        //根据客户号获取卡号列表后在进行筛选
        return cardAuthorizationInfoSelfMapper.selectByPrimaryCustomerId(
                accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId())
                .stream()
                .map(CardAuthorizationInfo::getCardNumber)
                .collect(Collectors.toList());
    }










}
