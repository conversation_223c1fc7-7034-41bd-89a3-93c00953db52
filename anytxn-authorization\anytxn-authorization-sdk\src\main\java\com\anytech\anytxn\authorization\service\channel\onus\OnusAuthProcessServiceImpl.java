package com.anytech.anytxn.authorization.service.channel.onus;

import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.domain.dto.*;
import com.anytech.anytxn.authorization.base.enums.DciMTIEnum;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.onus.IOnusHandlerAuthService;
import com.anytech.anytxn.authorization.base.service.partner.IPartnerMarginAuthService;
import com.anytech.anytxn.authorization.service.manager.AuthDataUpdateManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.base.service.onus.IOnusAuthProcessService;
import com.anytech.anytxn.authorization.base.utils.OnUsFieldCheckUtil;
import com.anytech.anytxn.authorization.base.utils.DinersFieldVerifyUtil;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DesensitizedUtils;
import com.anytech.anytxn.business.base.authorization.enums.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jpos.iso.ISOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 授权处理入口 AuthProcessServiceImpl
 *
 * <AUTHOR>
 * @date 2021/9/2718:18
 */
@Service
public class OnusAuthProcessServiceImpl implements IOnusAuthProcessService {

    private static final Logger logger = LoggerFactory.getLogger(OnusAuthProcessServiceImpl.class);

    @Resource
    private AuthDataUpdateManager authDataUpdateManager;

    @Autowired
    private IOnusHandlerAuthService onusHandlerAuthService;

    @Autowired
    private IPartnerMarginAuthService partnerMarginAuthService;

    /**
     * DCI pos 授权业务处理
     * @param iso8583Req {@link ISO8583ReqDTO}
     * @return
     * @throws IOException
     * @throws ISOException
     */
    @Override
    public OnusDcs8583RspDTO dealOnusIso8583Analog(Onus8583ReqDTO iso8583Req) throws IOException, ISOException {
        logger.info("Onus ISO8583 analog processing started");
        
        try {
            // 1.放请求报文到ThreadLocal
            AuthThreadLocalManager.buildReqData(iso8583Req);
            // 2.根据请求报文类型对报文域校验
            checkDataFieldOfOnUsiOnUs(iso8583Req);
            // 3.请求报文转换(RequestDTO转ISO8583DTO)
            ISO8583DTO iso8583BO = iso8583DtoSetFromOnUsReq(iso8583Req);
            // 4.授权业务处理
            logger.info("Calling onusHandlerAuthService.processAuth");
            ISO8583DTO result = onusHandlerAuthService.processAuth(iso8583BO);
            logger.info("Completed onusHandlerAuthService.processAuth");
            // 5.ISO8583DTO封装ResponseDTO
            OnusDcs8583RspDTO response = iso8583ResDtoSetOfOnUs(result);
            logger.info("Onus ISO8583 analog processing completed");
            return response;
        } catch (Exception e) {
            logger.error("Onus authorization processing failed", e);
            logger.error("On US authorization business processing exception: {}", e.getMessage(), e);
            logger.info("Calling partnerMarginAuthService.partnerMarginRollback");
            partnerMarginAuthService.partnerMarginRollback();
            logger.info("Completed partnerMarginAuthService.partnerMarginRollback");
            return authCheckOfDciDcsException(e, iso8583Req);
        } finally {
            logger.info("Calling partnerMarginAuthService.partnerMarginUnlock");
            partnerMarginAuthService.partnerMarginUnlock();
            logger.info("Completed partnerMarginAuthService.partnerMarginUnlock");
            AuthThreadLocalManager.remove();
        }
    }

    private OnusDcs8583RspDTO authCheckOfDciDcsException(Exception e, Onus8583ReqDTO iso8583Req) {
        OnusDcs8583RspDTO iso8583ResDTO = BeanMapping.copy(iso8583Req, OnusDcs8583RspDTO.class);
        ////null 00或空字符串，将39域设为909(系统异常)
        if(iso8583Req.getF039() == null || "00".equals(iso8583Req.getF039()) || iso8583Req.getF039().isEmpty()){
            iso8583ResDTO.setF039("909");
        }
        ////null 00或空字符串，将39域设为909(系统异常)
        //记录异常授权流水
        authDataUpdateManager.buildAuthorizationLog(iso8583Req.getF002(),
                "ONUS", e, iso8583ResDTO);
        // bug316
        iso8583ResDTO.setMTI(DciMTIEnum.getResponseMti(iso8583Req.getMTI()));
         return iso8583ResDTO;
    }



    /**
     * 请求报文转换(RequestDTO转ISO8583DTO) DCI pos请求对象转换
     * @param iso8583Req
     * @return
     */
    private ISO8583DTO iso8583DtoSetFromDciPosReq(DCIPos8583ReqDTO iso8583Req) {
        if (StringUtils.isBlank(iso8583Req.getSourceCode())) {
            logger.error("Source code is null or blank: sourceCode={}", iso8583Req.getSourceCode());
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL,AuthRepDetailEnum.SCE, iso8583Req.getSourceCode());
        }
        Map<Integer, String> fieldMap = new HashMap<>(32);
        //请求处理报文
        ISO8583DTO iso8583BO = new ISO8583DTO();
        iso8583BO.setMTI(iso8583Req.getMTI());
        iso8583BO.setHeaderHex(iso8583Req.getHeaderHex());
        iso8583BO.setSourceCode(iso8583Req.getSourceCode());
        if (!StringUtils.isEmpty(iso8583Req.getF002())) {
            fieldMap.put(2, iso8583Req.getF002());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF003())) {
            fieldMap.put(3, iso8583Req.getF003());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF004())) {
            fieldMap.put(4, iso8583Req.getF004());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF011())) {
            fieldMap.put(11, iso8583Req.getF011());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF012())) {
            fieldMap.put(12, iso8583Req.getF012());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF013())) {
            fieldMap.put(13, iso8583Req.getF013());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF014())) {
            fieldMap.put(14, iso8583Req.getF014());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF022())) {
            fieldMap.put(22, iso8583Req.getF022());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF023())) {
            fieldMap.put(23, iso8583Req.getF023());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF024())) {
            fieldMap.put(24, iso8583Req.getF024());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF025())) {
            fieldMap.put(25, iso8583Req.getF025());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF035())) {
            fieldMap.put(35, iso8583Req.getF035());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF037())) {
            fieldMap.put(37, iso8583Req.getF037());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF038())) {
            fieldMap.put(38, iso8583Req.getF038());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF039())) {
            fieldMap.put(39, iso8583Req.getF039());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF041())) {
            fieldMap.put(41, iso8583Req.getF041());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF042())) {
            fieldMap.put(42, iso8583Req.getF042());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF045())) {
            fieldMap.put(45, iso8583Req.getF045());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF048())) {
            fieldMap.put(48, iso8583Req.getF048());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF049())) {
            fieldMap.put(49, iso8583Req.getF049());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF052())) {
            fieldMap.put(52, iso8583Req.getF052());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF054())) {
            fieldMap.put(54, iso8583Req.getF054());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF055())){
            fieldMap.put(55, iso8583Req.getF055());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056())){
            fieldMap.put(56, iso8583Req.getF056());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF059())){
            fieldMap.put(59, iso8583Req.getF059());
        }
        // 60号域
        if (!StringUtils.isEmpty(iso8583Req.getF060())) {
            fieldMap.put(Constants8583Field.FIELD60, iso8583Req.getF060());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_1())) {
            fieldMap.put(Constants8583Field.FIELD60_1, iso8583Req.getF060_1());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_2())) {
            fieldMap.put(Constants8583Field.FIELD60_2, iso8583Req.getF060_2());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_3())) {
            fieldMap.put(Constants8583Field.FIELD60_3, iso8583Req.getF060_3());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF060_4())) {
            fieldMap.put(Constants8583Field.FIELD60_4, iso8583Req.getF060_4());
        }

        if (!StringUtils.isEmpty(iso8583Req.getF062())) {
            fieldMap.put(62, iso8583Req.getF062());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF063())) {
            fieldMap.put(63, iso8583Req.getF063());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF061())) {
            fieldMap.put(61, iso8583Req.getF061());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF064())) {
            fieldMap.put(64, iso8583Req.getF064());
        }
        iso8583BO.setFieldMap(fieldMap);
        return iso8583BO;
    }

    /**
     * 封装返回结果  DCI pos
     * @param result
     * @return
     */
    private DCIPos8583RspDTO iso8583ResDtoSetOfDciPos(ISO8583DTO result) {
        if (result == null) {
            logger.error("Result is null when setting DCI POS response");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.EE_8583);
        }
        DCIPos8583RspDTO iso8583Res = new DCIPos8583RspDTO();
        if (!result.getMTI().isEmpty()) {
            iso8583Res.setMTI(result.getMTI());
        }
        if (null != result.getHeaderHex() && !result.getHeaderHex().isEmpty()) {
            iso8583Res.setHeaderHex(result.getHeaderHex());
        }
        Map<Integer, String> map = result.getFieldMap();
        iso8583Res.setF002(map.get(2));
        iso8583Res.setF003(map.get(3));
        iso8583Res.setF011(map.get(11));
        iso8583Res.setF012(map.get(12));
        iso8583Res.setF013(map.get(13));
        iso8583Res.setF014(map.get(14));
        iso8583Res.setF022(map.get(22));
        iso8583Res.setF023(map.get(23));
        iso8583Res.setF024(map.get(24));
        iso8583Res.setF025(map.get(25));
        iso8583Res.setF035(map.get(35));
        iso8583Res.setF037(map.get(37));
        if (map.containsKey(38)) {
            iso8583Res.setF038(StringUtils.leftPad(map.get(38), 6, "0"));
        }
        iso8583Res.setF039(map.get(39));
        iso8583Res.setF041(map.get(41));
        iso8583Res.setF042(map.get(42));
        iso8583Res.setF045(map.get(45));
        iso8583Res.setF048(map.get(48));
        iso8583Res.setF049(map.get(49));
        iso8583Res.setF052(map.get(52));
        iso8583Res.setF054(map.get(54));
        iso8583Res.setF055(map.get(55));
        iso8583Res.setF056(map.get(56));
        iso8583Res.setF059(map.get(59));
        if(map.containsKey(Constants8583Field.FIELD60)){
            iso8583Res.setF060(map.get(Constants8583Field.FIELD60));
            iso8583Res.setF060_1(map.get(Constants8583Field.FIELD60_1));
            iso8583Res.setF060_2(map.get(Constants8583Field.FIELD60_2));
            iso8583Res.setF060_3(map.get(Constants8583Field.FIELD60_3));
            iso8583Res.setF060_4(map.get(Constants8583Field.FIELD60_4));
        }
        iso8583Res.setF062(map.get(62));
        iso8583Res.setF063(map.get(63));
        iso8583Res.setF061(map.get(61));
        iso8583Res.setF064(map.get(64));
        logger.warn("DCI POS return message object fields: {}", DesensitizedUtils.getJson(iso8583Res));
        return iso8583Res;
    }

    /**
     * 授权业务处理异常返回
     * @param e
     * @param iso8583Req
     * @return
     */
    private DCIPos8583RspDTO authCheckOfDciPosException(Exception e, DCIPos8583ReqDTO iso8583Req) {
        logger.error("Authorization exception for card number: {}", DesensitizedUtils.bankCard(iso8583Req.getF002()), e);

        DCIPos8583RspDTO iso8583ResDTO = BeanMapping.copy(iso8583Req, DCIPos8583RspDTO.class);
        //null 00或空字符串，将39域设为96
        if(iso8583Req.getF039() == null ||"00".equals(iso8583Req.getF039()) || iso8583Req.getF039().isEmpty()){
            iso8583ResDTO.setF039(AuthResponseCodeEnum.EXCEPTION.getCode());
        }
        //记录异常授权流水
        authDataUpdateManager.buildAuthorizationLog(iso8583Req.getF002(),
                iso8583Req.getSourceCode(), e, iso8583ResDTO);
        return iso8583ResDTO;
    }



    /**
     * 根据请求报文类型对报文域校验  onus
     * @param iso8583Req
     */
    private void checkDataFieldOfOnUsiOnUs(Onus8583ReqDTO iso8583Req) {
        String mti = iso8583Req.getMTI();
        if(!DinersFieldVerifyUtil.nonDigit(mti) || mti.length() != 4){
            logger.error("MTI check failed: mti={}, length={}", mti, mti != null ? mti.length() : 0);
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_MTI_CHECK_FAIL);
        }
        switch (mti){
            //Authorization Request
            case "1100":
                OnUsFieldCheckUtil.checkDCIField2(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField3(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField4(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField7(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField11(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField12(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField22(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField24(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField26(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField32(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField33(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField42(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField43(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField49(iso8583Req);
                //OnUsFieldCheckUtil.checkDCIField92(iso8583Req);

                break;
            //Authorization pos purchase
            case "1120":
                //OnUsFieldCheckUtil.checkDCIField2(iso8583Req);
                //应对1120+18类型的交易所做的修改
                if(StringUtils.isNotBlank(iso8583Req.getF003()) && !"180000".equals(iso8583Req.getF003())){
                    OnUsFieldCheckUtil.checkDCIField2(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField3(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField4(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField7(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField11(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField12(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField22(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField24(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField26(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField32(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField33(iso8583Req);
                    //OnUsFieldCheckUtil.checkDCIField39(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField42(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField43(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField49(iso8583Req);
                    //OnUsFieldCheckUtil.checkDCIField51(iso8583Req);
                    //OnUsFieldCheckUtil.checkDCIField55(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField56(iso8583Req);
                    OnUsFieldCheckUtil.checkDCIField92(iso8583Req);
                    //OnUsFieldCheckUtil.checkDCIField100(iso8583Req);
                    //OnUsFieldCheckUtil.checkDCIField123(iso8583Req);
                }
                break;
            //Authorization Reversal Request
            case "1420":
                OnUsFieldCheckUtil.checkDCIField2(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField3(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField4(iso8583Req);
                //OnUsFieldCheckUtil.checkDCIField6(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField7(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField11(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField12(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField24(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField26(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField32(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField33(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField49(iso8583Req);
                //OnUsFieldCheckUtil.checkDCIField51(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField56(iso8583Req);
                break;
            //Network Management Request
            case "1804":
                OnUsFieldCheckUtil.checkDCIField7(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField11(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField12(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField24(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField93(iso8583Req);
                OnUsFieldCheckUtil.checkDCIField94(iso8583Req);
            default:
                break;
        }
    }

    /**
     * 请求报文转换(RequestDTO转ISO8583DTO)onus请求对象转换
     * @param iso8583Req
     * @return
     */
    private ISO8583DTO iso8583DtoSetFromOnUsReq(Onus8583ReqDTO iso8583Req) {
        Map<Integer, String> fieldMap = new HashMap<>(64);
        //请求处理报文
        ISO8583DTO iso8583BO = new ISO8583DTO();
        iso8583BO.setMTI(iso8583Req.getMTI());
        iso8583BO.setHeaderHex(iso8583Req.getHeaderHex());
        iso8583BO.setSourceCode(iso8583Req.getSourceCode());
        if (!StringUtils.isEmpty(iso8583Req.getF002())) {
            fieldMap.put(2, iso8583Req.getF002());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF003())) {
            fieldMap.put(3, iso8583Req.getF003());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF004())) {
            fieldMap.put(4, iso8583Req.getF004());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF005())) {
            fieldMap.put(5, iso8583Req.getF005());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF006())) {
            fieldMap.put(6, iso8583Req.getF006());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF007())) {
            fieldMap.put(7, iso8583Req.getF007());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF009())) {
            fieldMap.put(9, iso8583Req.getF009());
        }
        if (!StringUtils.isEmpty(iso8583Req.getF010())) {
            fieldMap.put(10, iso8583Req.getF010());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF011())) {
            fieldMap.put(11, iso8583Req.getF011());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF012())) {
            fieldMap.put(12, iso8583Req.getF012());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF013())) {
            fieldMap.put(13, iso8583Req.getF013());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF014())) {
            fieldMap.put(14, iso8583Req.getF014());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF022())) {
            fieldMap.put(22, iso8583Req.getF022());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF023())) {
            fieldMap.put(23, iso8583Req.getF023());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF024())) {
            fieldMap.put(24, iso8583Req.getF024());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF026())) {
            fieldMap.put(26, iso8583Req.getF026());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF032())) {
            fieldMap.put(32, iso8583Req.getF032());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF033())) {
            fieldMap.put(33, iso8583Req.getF033());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF035())) {
            fieldMap.put(35, iso8583Req.getF035());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF037())) {
            fieldMap.put(37, iso8583Req.getF037());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF038())) {
            fieldMap.put(38, iso8583Req.getF038());
        }else {
            fieldMap.put(38, RandomStringUtils.randomNumeric( 6));
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF039())) {
            fieldMap.put(39, iso8583Req.getF039());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF040())) {
            fieldMap.put(40, iso8583Req.getF040());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF041())) {
            fieldMap.put(41, iso8583Req.getF041());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF042())) {
            fieldMap.put(42, iso8583Req.getF042());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF043())) {
            fieldMap.put(43, iso8583Req.getF043());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF044())) {
            fieldMap.put(44, iso8583Req.getF044());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF045())) {
            fieldMap.put(45, iso8583Req.getF045());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF046())) {
            fieldMap.put(46, iso8583Req.getF046());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF048())) {
            fieldMap.put(48, iso8583Req.getF048());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF049())) {
            fieldMap.put(49, iso8583Req.getF049());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF050())) {
            fieldMap.put(50, iso8583Req.getF050());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF051())) {
            fieldMap.put(51, iso8583Req.getF051());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF052())) {
            fieldMap.put(52, iso8583Req.getF052());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF054())) {
            fieldMap.put(54, iso8583Req.getF054());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF055())){
            fieldMap.put(55, iso8583Req.getF055());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF056())){
            fieldMap.put(56, iso8583Req.getF056());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF059())) {
            fieldMap.put(59, iso8583Req.getF059());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF062())) {
            fieldMap.put(62, iso8583Req.getF062());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF063())) {
            fieldMap.put(63, iso8583Req.getF063());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF064())) {
            fieldMap.put(64, iso8583Req.getF064());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF072())) {
            fieldMap.put(72, iso8583Req.getF072());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF092())) {
            fieldMap.put(92, iso8583Req.getF092());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF093())) {
            fieldMap.put(93, iso8583Req.getF093());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF100())) {
            fieldMap.put(100, iso8583Req.getF100());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF106())) {
            fieldMap.put(106, iso8583Req.getF106());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF122())) {
            fieldMap.put(122, iso8583Req.getF122());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF123())) {
            fieldMap.put(123, iso8583Req.getF123());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF124())) {
            fieldMap.put(124, iso8583Req.getF124());
        }
        if (StringUtils.isNotEmpty(iso8583Req.getF125())) {
            fieldMap.put(125, iso8583Req.getF125());
        }
        iso8583BO.setFieldMap(fieldMap);
        return iso8583BO;
    }

    /**
     * 封装返回结果  onus
     * @param result
     * @return
     */
    private OnusDcs8583RspDTO iso8583ResDtoSetOfOnUs(ISO8583DTO result) {
        if (result == null) {
            logger.error("Result is null when setting ONUS response");
            throw new AnyTxnAuthException(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, AuthRepDetailEnum.EE_8583);
        }
        OnusDcs8583RspDTO iso8583Res = new OnusDcs8583RspDTO();
        if (!result.getMTI().isEmpty()) {
            iso8583Res.setMTI(result.getMTI());
        }
        if (result.getHeaderHex() != null) {
            iso8583Res.setHeaderHex(result.getHeaderHex());
        }
        Map<Integer, String> map = result.getFieldMap();
        iso8583Res.setF002(map.get(2));
        iso8583Res.setF003(map.get(3));
        if (map.containsKey(4)) {
            iso8583Res.setF004(StringUtils.leftPad(map.get(4), 12, "0"));
        }
        if (map.containsKey(5)) {
            iso8583Res.setF005(StringUtils.leftPad(map.get(5), 12, "0"));
        }
        iso8583Res.setF006(map.get(6));
        iso8583Res.setF007(map.get(7));
        iso8583Res.setF009(map.get(9));
        iso8583Res.setF010(map.get(10));
        iso8583Res.setF011(map.get(11));
        iso8583Res.setF012(map.get(12));
        iso8583Res.setF013(map.get(13));
        iso8583Res.setF014(map.get(14));
        iso8583Res.setF015(map.get(15));
        iso8583Res.setF022(map.get(22));
        iso8583Res.setF023(map.get(23));
        iso8583Res.setF024(map.get(24));
        iso8583Res.setF025(map.get(25));
        iso8583Res.setF026(map.get(26));
        iso8583Res.setF030(map.get(30));
        iso8583Res.setF032(map.get(32));
        String f033 = map.get(33);
        if (StringUtils.isNotEmpty(map.get(100))) {
            iso8583Res.setF033(map.get(100));
        } else {
            iso8583Res.setF033("00000361588");
        }
        iso8583Res.setF035(map.get(35));
        iso8583Res.setF037(map.get(37));
        if (map.containsKey(38)) {
            iso8583Res.setF038(StringUtils.leftPad(map.get(38), 6, "0"));
        }
        iso8583Res.setF039(map.get(39));
        iso8583Res.setF040(map.get(40));
        iso8583Res.setF041(map.get(41));
        iso8583Res.setF042(map.get(42));
        iso8583Res.setF043(map.get(43));
        iso8583Res.setF044(map.get(44));
        iso8583Res.setF045(map.get(45));
        iso8583Res.setF046(map.get(46));
//        iso8583Res.setF048(map.get(48));
        iso8583Res.setF049(map.get(49));
        iso8583Res.setF050(map.get(50));
        iso8583Res.setF051(map.get(51));
        iso8583Res.setF052(map.get(52));
        iso8583Res.setF054(map.get(54));
        iso8583Res.setF055(map.get(55));
        iso8583Res.setF056(map.get(56));
        iso8583Res.setF058(map.get(58));
        iso8583Res.setF059(map.get(59));
        iso8583Res.setF062(map.get(62));
        iso8583Res.setF064(map.get(64));
        iso8583Res.setF072(map.get(72));
        iso8583Res.setF092(map.get(92));
        iso8583Res.setF093(map.get(93));
        iso8583Res.setF094(map.get(94));
        iso8583Res.setF096(map.get(96));
        //iso8583Res.setF100(map.get(100));
        if (StringUtils.isNotEmpty(f033)) {
            iso8583Res.setF100(f033);
        } else {
            iso8583Res.setF100("00000361589");
        }
        iso8583Res.setF101(map.get(101));
        iso8583Res.setF104(map.get(104));
        iso8583Res.setF106(map.get(106));
        iso8583Res.setF122(map.get(122));
        iso8583Res.setF123(map.get(123));
        iso8583Res.setF124(map.get(124));
        iso8583Res.setF125(map.get(125));
        logger.warn("ON_US return message object field: {}", DesensitizedUtils.getJson(iso8583Res));
        return iso8583Res;
    }


}
