#
#spring.main.allow-bean-definition-overriding=true
#server.port=18082
#spring.application.name=anytxn-file-server
#anytxn.number.segmentEnable=true
#anytxn.number.tenantIds=6001,6002
#anytxn.number.segmentMap.ds6001=6001
#anytxn.number.segmentMap.ds6002=6002
#
#spring.config.import=optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml
#
#spring.shardingsphere.datasource.names=ds6001,ds6002
#spring.shardingsphere.datasource.ds6001.url=************************************************************************************************************************************
#spring.shardingsphere.datasource.ds6001.type=com.alibaba.druid.pool.DruidDataSource
#spring.shardingsphere.datasource.ds6001.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.shardingsphere.datasource.ds6001.username=jrxuser
#spring.shardingsphere.datasource.ds6001.password=any123!
#spring.shardingsphere.datasource.ds6002.url=************************************************************************************************************************************
#spring.shardingsphere.datasource.ds6002.type=com.alibaba.druid.pool.DruidDataSource
#spring.shardingsphere.datasource.ds6002.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.shardingsphere.datasource.ds6002.username=jrxuser
#spring.shardingsphere.datasource.ds6002.password=any123!
#spring.shardingsphere.props.sql-show=true
#
#spring.shardingsphere.rules.sharding.tables.t_user.actual-data-nodes=ds$->{["6001","6002"]}.t_user
#
#spring.shardingsphere.rules.sharding.tables.t_user.database-strategy.complex.sharding-columns=tenant_id,customer_id,user_id,channel,id_number,mobile
#spring.shardingsphere.rules.sharding.tables.t_user.database-strategy.complex.sharding-algorithm-name=tenantSplitDatabase
#
##spring.shardingsphere.rules.sharding.tables.t_user.table-strategy.complex.sharding-columns=tenant_id,user_id,id_no
##spring.shardingsphere.rules.sharding.tables.t_user.table-strategy.complex.sharding-algorithm-name=tenant-and-key-split
#
##????
#spring.shardingsphere.rules.sharding.sharding-algorithms.tenantSplitDatabase.type=CLASS_BASED
#spring.shardingsphere.rules.sharding.sharding-algorithms.tenantSplitDatabase.props.strategy=complex
#spring.shardingsphere.rules.sharding.sharding-algorithms.tenantSplitDatabase.props.algorithmClassName=com.jrx.anytech.common.sharding.algorithm.TenantSplitDatabaseAlgorithm
#
#
#spring.shardingsphere.rules.sharding.tables.mapping_applynum_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_applynum_cid
#spring.shardingsphere.rules.sharding.tables.mapping_bankcard_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_bankcard_cid
#spring.shardingsphere.rules.sharding.tables.mapping_business_token.actual-data-nodes=ds$->{["6002","6001"]}.mapping_business_token
#spring.shardingsphere.rules.sharding.tables.mapping_ecif_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_idtype_cid
#spring.shardingsphere.rules.sharding.tables.mapping_mid_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_mid_cid
#spring.shardingsphere.rules.sharding.tables.mapping_mobile_phone_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_mobile_phone_cid
#spring.shardingsphere.rules.sharding.tables.mapping_sub_card_cid.actual-data-nodes=ds$->{["6002","6001"]}.mapping_sub_card_cid
##spring.shardingsphere.rules.sharding.tables.mapping_applynum_cid.database-strategy.complex.sharding-columns=TENANT_ID
##spring.shardingsphere.rules.sharding.tables.mapping_applynum_cid.database-strategy.complex.sharding-algorithm-name=tenantAndKeySplit
#
#spring.shardingsphere.rules.sharding.default-database-strategy.complex.sharding-algorithm-name=tenantSplitDatabase
#spring.shardingsphere.rules.sharding.default-database-strategy.complex.sharding-columns=TENANT_ID
#
#
#spring.shardingsphere.rules.encrypt.encryptors.apply_num_encryptor.type=AES
#spring.shardingsphere.rules.encrypt.encryptors.apply_num_encryptor.props.aes-key-value=123456abc
#
#spring.shardingsphere.rules.encrypt.tables.mapping_applynum_cid.columns.APPLY_NUM.cipher-column=APPLY_NUM
#spring.shardingsphere.rules.encrypt.tables.mapping_applynum_cid.columns.APPLY_NUM.encryptor-name=apply_num_encryptor
#
## false ???????
#spring.shardingsphere.props.query-with-cipher-column=true
#
#mybatis.type-aliases-package=com.jrx.anytech.common.sharding.entity
## 自定义算法规则
#algorithm.tenantandkeysplit.rule[0]=1|000~000|0~50000|hash,1|001~001|50001~*|hash
#algorithm.tenantandkeysplit.rule[1]=2|001~003|0~2000|hash,2|004~004|2001~*|hash
#algorithm.tenantandkeysplit.rule[2]=3|001~003|0~*|hash
#
#
#
#
