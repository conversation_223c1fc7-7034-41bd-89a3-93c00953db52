package com.anytech.anytxn.file;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * @description: 文件管理sdk
 * @author: z<PERSON><PERSON>
 * @create: 2021-03-18
 **/

@Import(EnableFileManagerApi.FileManagerConfigurer.class)
public @interface EnableFileManagerApi {

    @Configuration
    @ComponentScan(basePackages = {"com.anytech.anytxn.file", "com.anytech.anytxn.common"})
    @MapperScan(basePackages = "com.anytech.anytxn.file.mapper")
    class FileManagerConfigurer {
    }
}
